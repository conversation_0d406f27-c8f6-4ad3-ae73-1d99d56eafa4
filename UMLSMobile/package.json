{"name": "umlsmobile", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.8.2", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "expo": "~53.0.11", "expo-barcode-scanner": "^13.0.1", "expo-camera": "^16.1.8", "expo-crypto": "^14.1.5", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-haptics": "^14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-local-authentication": "^16.0.4", "expo-location": "^18.1.5", "expo-notifications": "^0.31.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-task-manager": "^13.1.5", "expo-web-browser": "~14.1.6", "i18next": "^25.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-keychain": "^10.0.0", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.7", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}