from django.contrib import admin
from .models import (
    QuestionBank, Question, Assessment, AssessmentQuestion,
    AssessmentAttempt, QuestionResponse
)


@admin.register(QuestionBank)
class QuestionBankAdmin(admin.ModelAdmin):
    list_display = ['title', 'course', 'created_by', 'questions_count', 'is_public', 'is_active', 'created_at']
    list_filter = ['is_public', 'is_active', 'course', 'created_at']
    search_fields = ['title', 'title_ar', 'description']
    readonly_fields = ['questions_count', 'created_at', 'updated_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('course', 'created_by')


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ['question_text_short', 'question_type', 'difficulty_level', 'points', 'question_bank', 'is_active', 'times_used']
    list_filter = ['question_type', 'difficulty_level', 'is_active', 'question_bank__course']
    search_fields = ['question_text', 'question_text_ar', 'tags', 'category']
    readonly_fields = ['times_used', 'average_score', 'created_at', 'updated_at']
    
    def question_text_short(self, obj):
        return obj.question_text[:50] + '...' if len(obj.question_text) > 50 else obj.question_text
    question_text_short.short_description = 'Question Text'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('question_bank', 'created_by')


class AssessmentQuestionInline(admin.TabularInline):
    model = AssessmentQuestion
    extra = 0
    readonly_fields = ['created_at']


@admin.register(Assessment)
class AssessmentAdmin(admin.ModelAdmin):
    list_display = ['title', 'course', 'assessment_type', 'status', 'questions_count', 'attempts_count', 'available_from', 'available_until']
    list_filter = ['assessment_type', 'status', 'course', 'created_at']
    search_fields = ['title', 'title_ar', 'description']
    readonly_fields = ['questions_count', 'attempts_count', 'created_at', 'updated_at', 'published_at']
    inlines = [AssessmentQuestionInline]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('course', 'created_by')


class QuestionResponseInline(admin.TabularInline):
    model = QuestionResponse
    extra = 0
    readonly_fields = ['is_correct', 'points_earned', 'answered_at', 'requires_manual_grading']


@admin.register(AssessmentAttempt)
class AssessmentAttemptAdmin(admin.ModelAdmin):
    list_display = ['student', 'assessment', 'attempt_number', 'status', 'percentage_score', 'passed', 'started_at', 'submitted_at']
    list_filter = ['status', 'passed', 'assessment__course', 'started_at']
    search_fields = ['student__first_name', 'student__last_name', 'student__email', 'assessment__title']
    readonly_fields = ['total_points', 'percentage_score', 'passed', 'time_spent_minutes', 'auto_graded', 'graded_at']
    inlines = [QuestionResponseInline]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('assessment', 'student')


@admin.register(QuestionResponse)
class QuestionResponseAdmin(admin.ModelAdmin):
    list_display = ['attempt', 'question_short', 'is_correct', 'points_earned', 'requires_manual_grading', 'answered_at']
    list_filter = ['is_correct', 'requires_manual_grading', 'manually_graded', 'question__question_type']
    search_fields = ['attempt__student__first_name', 'attempt__student__last_name', 'question__question_text']
    readonly_fields = ['is_correct', 'points_earned', 'answered_at']
    
    def question_short(self, obj):
        return obj.question.question_text[:30] + '...' if len(obj.question.question_text) > 30 else obj.question.question_text
    question_short.short_description = 'Question'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('attempt', 'question', 'assessment_question')
