from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from assessments.models import (
    Assessment, Question, QuestionBank, AssessmentQuestion,
    QuestionType, DifficultyLevel
)
from courses.models import Course

User = get_user_model()


class Command(BaseCommand):
    help = 'Add sample questions to the existing assessment'

    def handle(self, *args, **options):
        self.stdout.write('Adding sample questions to assessment...')
        
        try:
            # Get the existing assessment
            assessment = Assessment.objects.get(id='7b71e040-8d30-4546-813b-75ccfe1a239e')
            self.stdout.write(f'Found assessment: {assessment.title}')
            
            # Get or create a question bank
            question_bank, created = QuestionBank.objects.get_or_create(
                course=assessment.course,
                title='Programming Fundamentals Question Bank',
                defaults={
                    'description': 'Questions for Introduction to Programming course',
                    'created_by': User.objects.filter(is_superuser=True).first() or User.objects.first()
                }
            )
            
            if created:
                self.stdout.write(f'Created question bank: {question_bank.title}')
            else:
                self.stdout.write(f'Using existing question bank: {question_bank.title}')
            
            # Sample questions data
            questions_data = [
                {
                    'question_text': 'What is the correct way to declare a variable in Python?',
                    'question_text_ar': 'ما هي الطريقة الصحيحة لإعلان متغير في Python؟',
                    'question_type': QuestionType.MULTIPLE_CHOICE,
                    'difficulty_level': DifficultyLevel.EASY,
                    'points': 1.0,
                    'question_data': {
                        'options': [
                            {'id': 'a', 'text': 'var x = 5', 'text_ar': 'var x = 5'},
                            {'id': 'b', 'text': 'x = 5', 'text_ar': 'x = 5'},
                            {'id': 'c', 'text': 'int x = 5', 'text_ar': 'int x = 5'},
                            {'id': 'd', 'text': 'declare x = 5', 'text_ar': 'declare x = 5'}
                        ],
                        'correct_answers': ['b']
                    },
                    'explanation': 'In Python, variables are declared by simply assigning a value to them.',
                    'explanation_ar': 'في Python، يتم إعلان المتغيرات ببساطة عن طريق تعيين قيمة لها.',
                    'order_index': 1,
                    'assessment_points': 1.0
                },
                {
                    'question_text': 'Python is a compiled programming language.',
                    'question_text_ar': 'Python هي لغة برمجة مترجمة.',
                    'question_type': QuestionType.TRUE_FALSE,
                    'difficulty_level': DifficultyLevel.EASY,
                    'points': 1.0,
                    'question_data': {
                        'correct_answer': False
                    },
                    'explanation': 'Python is an interpreted language, not a compiled language.',
                    'explanation_ar': 'Python هي لغة مفسرة، وليست لغة مترجمة.',
                    'order_index': 2,
                    'assessment_points': 1.0
                },
                {
                    'question_text': 'Which of the following are valid Python data types? (Select all that apply)',
                    'question_text_ar': 'أي من التالي هي أنواع بيانات Python صالحة؟ (اختر كل ما ينطبق)',
                    'question_type': QuestionType.MULTIPLE_CHOICE,
                    'difficulty_level': DifficultyLevel.MEDIUM,
                    'points': 2.0,
                    'question_data': {
                        'options': [
                            {'id': 'a', 'text': 'int', 'text_ar': 'int'},
                            {'id': 'b', 'text': 'string', 'text_ar': 'string'},
                            {'id': 'c', 'text': 'list', 'text_ar': 'list'},
                            {'id': 'd', 'text': 'boolean', 'text_ar': 'boolean'}
                        ],
                        'correct_answers': ['a', 'c'],
                        'multiple_select': True
                    },
                    'explanation': 'int and list are valid Python data types. The correct types are int, str (not string), list, and bool (not boolean).',
                    'explanation_ar': 'int و list هي أنواع بيانات Python صالحة. الأنواع الصحيحة هي int و str (وليس string) و list و bool (وليس boolean).',
                    'order_index': 3,
                    'assessment_points': 2.0
                }
            ]
            
            # Create questions and add them to the assessment
            created_count = 0
            for q_data in questions_data:
                # Create the question
                question, created = Question.objects.get_or_create(
                    question_text=q_data['question_text'],
                    question_bank=question_bank,
                    defaults={
                        'question_text_ar': q_data['question_text_ar'],
                        'question_type': q_data['question_type'],
                        'difficulty_level': q_data['difficulty_level'],
                        'points': q_data['points'],
                        'question_data': q_data['question_data'],
                        'explanation': q_data['explanation'],
                        'explanation_ar': q_data['explanation_ar'],
                        'created_by': User.objects.filter(is_superuser=True).first() or User.objects.first()
                    }
                )
                
                if created:
                    self.stdout.write(f'Created question: {question.question_text[:50]}...')
                    created_count += 1
                
                # Add question to assessment
                assessment_question, created = AssessmentQuestion.objects.get_or_create(
                    assessment=assessment,
                    question=question,
                    defaults={
                        'points': q_data['assessment_points'],
                        'order_index': q_data['order_index'],
                        'is_required': True
                    }
                )
                
                if created:
                    self.stdout.write(f'Added question to assessment with {q_data["assessment_points"]} points')
            
            # Update assessment total points
            total_points = sum(q['assessment_points'] for q in questions_data)
            assessment.total_points = total_points
            assessment.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully added {created_count} questions to assessment. '
                    f'Total assessment points: {total_points}'
                )
            )
            
        except Assessment.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Assessment not found. Please check the assessment ID.')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error adding questions: {str(e)}')
            )