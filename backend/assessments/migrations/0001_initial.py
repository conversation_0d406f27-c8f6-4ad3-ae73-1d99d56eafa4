# Generated by Django 4.2.7 on 2025-07-13 13:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0004_add_retake_tracking'),
    ]

    operations = [
        migrations.CreateModel(
            name='Assessment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='title')),
                ('title_ar', models.CharField(blank=True, max_length=200, verbose_name='title (Arabic)')),
                ('description', models.TextField(blank=True, verbose_name='description')),
                ('description_ar', models.TextField(blank=True, verbose_name='description (Arabic)')),
                ('instructions', models.TextField(blank=True, verbose_name='instructions')),
                ('instructions_ar', models.TextField(blank=True, verbose_name='instructions (Arabic)')),
                ('assessment_type', models.Char<PERSON>ield(choices=[('quiz', 'Quiz'), ('exam', 'Exam'), ('test', 'Test'), ('practice', 'Practice'), ('survey', 'Survey')], max_length=20, verbose_name='assessment type')),
                ('total_points', models.DecimalField(decimal_places=2, default=100.0, max_digits=8, verbose_name='total points')),
                ('passing_score', models.DecimalField(decimal_places=2, default=60.0, max_digits=5, verbose_name='passing score')),
                ('time_limit_minutes', models.PositiveIntegerField(blank=True, null=True, verbose_name='time limit (minutes)')),
                ('available_from', models.DateTimeField(verbose_name='available from')),
                ('available_until', models.DateTimeField(verbose_name='available until')),
                ('max_attempts', models.PositiveIntegerField(default=1, verbose_name='maximum attempts')),
                ('allow_review', models.BooleanField(default=True, verbose_name='allow review')),
                ('show_correct_answers', models.BooleanField(default=False, verbose_name='show correct answers')),
                ('show_score_immediately', models.BooleanField(default=True, verbose_name='show score immediately')),
                ('randomize_questions', models.BooleanField(default=False, verbose_name='randomize questions')),
                ('questions_per_page', models.PositiveIntegerField(default=1, verbose_name='questions per page')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('active', 'Active'), ('closed', 'Closed'), ('archived', 'Archived')], default='draft', max_length=20, verbose_name='status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('published_at', models.DateTimeField(blank=True, null=True, verbose_name='published at')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='courses.course')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_assessments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Assessment',
                'verbose_name_plural': 'Assessments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AssessmentAttempt',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('attempt_number', models.PositiveIntegerField(verbose_name='attempt number')),
                ('status', models.CharField(choices=[('in_progress', 'In Progress'), ('submitted', 'Submitted'), ('graded', 'Graded'), ('expired', 'Expired')], default='in_progress', max_length=20, verbose_name='status')),
                ('total_points', models.DecimalField(decimal_places=2, default=0.0, max_digits=8, verbose_name='total points')),
                ('max_points', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='maximum points')),
                ('percentage_score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='percentage score')),
                ('passed', models.BooleanField(default=False, verbose_name='passed')),
                ('started_at', models.DateTimeField(auto_now_add=True, verbose_name='started at')),
                ('submitted_at', models.DateTimeField(blank=True, null=True, verbose_name='submitted at')),
                ('time_spent_minutes', models.PositiveIntegerField(default=0, verbose_name='time spent (minutes)')),
                ('time_limit_minutes', models.PositiveIntegerField(blank=True, null=True, verbose_name='time limit (minutes)')),
                ('randomized_questions', models.JSONField(default=list, help_text='Question order for this attempt', verbose_name='randomized questions')),
                ('auto_graded', models.BooleanField(default=False, verbose_name='auto graded')),
                ('manually_graded', models.BooleanField(default=False, verbose_name='manually graded')),
                ('graded_at', models.DateTimeField(blank=True, null=True, verbose_name='graded at')),
                ('instructor_feedback', models.TextField(blank=True, verbose_name='instructor feedback')),
                ('assessment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='assessments.assessment')),
                ('graded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_attempts', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessment_attempts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Assessment Attempt',
                'verbose_name_plural': 'Assessment Attempts',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='QuestionBank',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='title')),
                ('title_ar', models.CharField(blank=True, max_length=200, verbose_name='title (Arabic)')),
                ('description', models.TextField(blank=True, verbose_name='description')),
                ('description_ar', models.TextField(blank=True, verbose_name='description (Arabic)')),
                ('is_public', models.BooleanField(default=False, help_text='Allow other instructors to use questions', verbose_name='public')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_banks', to='courses.course')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_question_banks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Question Bank',
                'verbose_name_plural': 'Question Banks',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('question_text', models.TextField(verbose_name='question text')),
                ('question_text_ar', models.TextField(blank=True, verbose_name='question text (Arabic)')),
                ('explanation', models.TextField(blank=True, help_text='Explanation for correct answer', verbose_name='explanation')),
                ('explanation_ar', models.TextField(blank=True, verbose_name='explanation (Arabic)')),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Multiple Choice'), ('true_false', 'True/False'), ('short_answer', 'Short Answer'), ('essay', 'Essay'), ('fill_blank', 'Fill in the Blank'), ('matching', 'Matching'), ('ordering', 'Ordering')], max_length=20, verbose_name='question type')),
                ('difficulty_level', models.CharField(choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard')], default='medium', max_length=10, verbose_name='difficulty level')),
                ('points', models.DecimalField(decimal_places=2, default=1.0, max_digits=5, verbose_name='points')),
                ('question_data', models.JSONField(default=dict, help_text='Question-specific data like options, correct answers', verbose_name='question data')),
                ('image', models.ImageField(blank=True, null=True, upload_to='questions/images/', verbose_name='image')),
                ('audio', models.FileField(blank=True, null=True, upload_to='questions/audio/', verbose_name='audio')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500, verbose_name='tags')),
                ('category', models.CharField(blank=True, max_length=100, verbose_name='category')),
                ('learning_objective', models.CharField(blank=True, max_length=200, verbose_name='learning objective')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('randomize_options', models.BooleanField(default=True, verbose_name='randomize options')),
                ('times_used', models.PositiveIntegerField(default=0, verbose_name='times used')),
                ('average_score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='average score')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_questions', to=settings.AUTH_USER_MODEL)),
                ('question_bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='assessments.questionbank')),
            ],
            options={
                'verbose_name': 'Question',
                'verbose_name_plural': 'Questions',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='AssessmentQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('points', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='points')),
                ('order_index', models.PositiveIntegerField(verbose_name='order')),
                ('is_required', models.BooleanField(default=True, verbose_name='required')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('assessment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessment_questions', to='assessments.assessment')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessment_uses', to='assessments.question')),
            ],
            options={
                'verbose_name': 'Assessment Question',
                'verbose_name_plural': 'Assessment Questions',
                'ordering': ['order_index'],
            },
        ),
        migrations.CreateModel(
            name='QuestionResponse',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('student_answer', models.JSONField(help_text="Student's answer in appropriate format", verbose_name='student answer')),
                ('is_correct', models.BooleanField(default=False, verbose_name='is correct')),
                ('points_earned', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, verbose_name='points earned')),
                ('time_spent_seconds', models.PositiveIntegerField(default=0, verbose_name='time spent (seconds)')),
                ('answered_at', models.DateTimeField(auto_now=True, verbose_name='answered at')),
                ('requires_manual_grading', models.BooleanField(default=False, verbose_name='requires manual grading')),
                ('manually_graded', models.BooleanField(default=False, verbose_name='manually graded')),
                ('grader_feedback', models.TextField(blank=True, verbose_name='grader feedback')),
                ('assessment_question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='assessments.assessmentquestion')),
                ('attempt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='assessments.assessmentattempt')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='assessments.question')),
            ],
            options={
                'verbose_name': 'Question Response',
                'verbose_name_plural': 'Question Responses',
                'ordering': ['assessment_question__order_index'],
                'unique_together': {('attempt', 'question')},
            },
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['question_type', 'difficulty_level'], name='assessments_questio_3f9466_idx'),
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['question_bank', 'is_active'], name='assessments_questio_23b5f3_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='assessmentquestion',
            unique_together={('assessment', 'question')},
        ),
        migrations.AddIndex(
            model_name='assessmentattempt',
            index=models.Index(fields=['student', 'status'], name='assessments_student_30c9da_idx'),
        ),
        migrations.AddIndex(
            model_name='assessmentattempt',
            index=models.Index(fields=['assessment', 'status'], name='assessments_assessm_f3a416_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='assessmentattempt',
            unique_together={('assessment', 'student', 'attempt_number')},
        ),
        migrations.AddIndex(
            model_name='assessment',
            index=models.Index(fields=['course', 'status'], name='assessments_course__6e055a_idx'),
        ),
        migrations.AddIndex(
            model_name='assessment',
            index=models.Index(fields=['available_from', 'available_until'], name='assessments_availab_0506d4_idx'),
        ),
    ]
