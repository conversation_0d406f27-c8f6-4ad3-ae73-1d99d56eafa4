from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from courses.models import Course
import uuid
import json

User = get_user_model()


class QuestionType(models.TextChoices):
    MULTIPLE_CHOICE = 'multiple_choice', _('Multiple Choice')
    TRUE_FALSE = 'true_false', _('True/False')
    SHORT_ANSWER = 'short_answer', _('Short Answer')
    ESSAY = 'essay', _('Essay')
    FILL_BLANK = 'fill_blank', _('Fill in the Blank')
    MATCHING = 'matching', _('Matching')
    ORDERING = 'ordering', _('Ordering')


class DifficultyLevel(models.TextChoices):
    EASY = 'easy', _('Easy')
    MEDIUM = 'medium', _('Medium')
    HARD = 'hard', _('Hard')


class QuestionBank(models.Model):
    """
    Question bank for storing reusable questions
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Basic Information
    title = models.CharField(_('title'), max_length=200)
    title_ar = models.CharField(_('title (Arabic)'), max_length=200, blank=True)
    description = models.TextField(_('description'), blank=True)
    description_ar = models.TextField(_('description (Arabic)'), blank=True)
    
    # Associations
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='question_banks')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_question_banks')
    
    # Settings
    is_public = models.BooleanField(_('public'), default=False, help_text=_('Allow other instructors to use questions'))
    is_active = models.BooleanField(_('active'), default=True)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Question Bank')
        verbose_name_plural = _('Question Banks')
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"{self.course.code} - {self.title}"
    
    @property
    def questions_count(self):
        return self.questions.filter(is_active=True).count()


class Question(models.Model):
    """
    Individual questions in the question bank
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Basic Information
    question_text = models.TextField(_('question text'))
    question_text_ar = models.TextField(_('question text (Arabic)'), blank=True)
    explanation = models.TextField(_('explanation'), blank=True, help_text=_('Explanation for correct answer'))
    explanation_ar = models.TextField(_('explanation (Arabic)'), blank=True)
    
    # Question Properties
    question_bank = models.ForeignKey(QuestionBank, on_delete=models.CASCADE, related_name='questions')
    question_type = models.CharField(_('question type'), max_length=20, choices=QuestionType.choices)
    difficulty_level = models.CharField(_('difficulty level'), max_length=10, choices=DifficultyLevel.choices, default=DifficultyLevel.MEDIUM)
    points = models.DecimalField(_('points'), max_digits=5, decimal_places=2, default=1.0)
    
    # Question Data (JSON field for flexibility)
    question_data = models.JSONField(_('question data'), default=dict, help_text=_('Question-specific data like options, correct answers'))
    
    # Media
    image = models.ImageField(_('image'), upload_to='questions/images/', blank=True, null=True)
    audio = models.FileField(_('audio'), upload_to='questions/audio/', blank=True, null=True)
    
    # Metadata
    tags = models.CharField(_('tags'), max_length=500, blank=True, help_text=_('Comma-separated tags'))
    category = models.CharField(_('category'), max_length=100, blank=True)
    learning_objective = models.CharField(_('learning objective'), max_length=200, blank=True)
    
    # Settings
    is_active = models.BooleanField(_('active'), default=True)
    randomize_options = models.BooleanField(_('randomize options'), default=True)
    
    # Statistics
    times_used = models.PositiveIntegerField(_('times used'), default=0)
    average_score = models.DecimalField(_('average score'), max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_questions')
    
    class Meta:
        verbose_name = _('Question')
        verbose_name_plural = _('Questions')
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['question_type', 'difficulty_level']),
            models.Index(fields=['question_bank', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.question_type} - {self.question_text[:50]}..."
    
    def clean(self):
        """Validate question data based on question type"""
        if self.question_type == QuestionType.MULTIPLE_CHOICE:
            self._validate_multiple_choice()
        elif self.question_type == QuestionType.TRUE_FALSE:
            self._validate_true_false()
        elif self.question_type == QuestionType.MATCHING:
            self._validate_matching()
    
    def _validate_multiple_choice(self):
        """Validate multiple choice question data"""
        if not self.question_data.get('options'):
            raise ValidationError(_('Multiple choice questions must have options'))
        
        options = self.question_data.get('options', [])
        if len(options) < 2:
            raise ValidationError(_('Multiple choice questions must have at least 2 options'))
        
        correct_answers = self.question_data.get('correct_answers', [])
        if not correct_answers:
            raise ValidationError(_('Multiple choice questions must have at least one correct answer'))
    
    def _validate_true_false(self):
        """Validate true/false question data"""
        correct_answer = self.question_data.get('correct_answer')
        if correct_answer not in [True, False]:
            raise ValidationError(_('True/False questions must have a boolean correct answer'))
    
    def _validate_matching(self):
        """Validate matching question data"""
        pairs = self.question_data.get('pairs', [])
        if len(pairs) < 2:
            raise ValidationError(_('Matching questions must have at least 2 pairs'))
    
    def get_correct_answers(self):
        """Get correct answers based on question type"""
        if self.question_type == QuestionType.MULTIPLE_CHOICE:
            return self.question_data.get('correct_answers', [])
        elif self.question_type == QuestionType.TRUE_FALSE:
            return [self.question_data.get('correct_answer')]
        elif self.question_type == QuestionType.SHORT_ANSWER:
            return self.question_data.get('acceptable_answers', [])
        elif self.question_type == QuestionType.MATCHING:
            return self.question_data.get('pairs', [])
        return []
    
    def check_answer(self, student_answer):
        """Check if student answer is correct"""
        if self.question_type == QuestionType.MULTIPLE_CHOICE:
            correct_answers = set(self.question_data.get('correct_answers', []))
            student_answers = set(student_answer if isinstance(student_answer, list) else [student_answer])
            return correct_answers == student_answers
        
        elif self.question_type == QuestionType.TRUE_FALSE:
            return self.question_data.get('correct_answer') == student_answer
        
        elif self.question_type == QuestionType.SHORT_ANSWER:
            acceptable_answers = [ans.lower().strip() for ans in self.question_data.get('acceptable_answers', [])]
            return student_answer.lower().strip() in acceptable_answers
        
        elif self.question_type == QuestionType.MATCHING:
            correct_pairs = {pair['left']: pair['right'] for pair in self.question_data.get('pairs', [])}
            return student_answer == correct_pairs
        
        return False


class Assessment(models.Model):
    """
    Assessments (quizzes, exams, tests)
    """
    class AssessmentType(models.TextChoices):
        QUIZ = 'quiz', _('Quiz')
        EXAM = 'exam', _('Exam')
        TEST = 'test', _('Test')
        PRACTICE = 'practice', _('Practice')
        SURVEY = 'survey', _('Survey')
    
    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        PUBLISHED = 'published', _('Published')
        ACTIVE = 'active', _('Active')
        CLOSED = 'closed', _('Closed')
        ARCHIVED = 'archived', _('Archived')
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Basic Information
    title = models.CharField(_('title'), max_length=200)
    title_ar = models.CharField(_('title (Arabic)'), max_length=200, blank=True)
    description = models.TextField(_('description'), blank=True)
    description_ar = models.TextField(_('description (Arabic)'), blank=True)
    instructions = models.TextField(_('instructions'), blank=True)
    instructions_ar = models.TextField(_('instructions (Arabic)'), blank=True)
    
    # Associations
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='assessments')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_assessments')
    
    # Assessment Properties
    assessment_type = models.CharField(_('assessment type'), max_length=20, choices=AssessmentType.choices)
    total_points = models.DecimalField(_('total points'), max_digits=8, decimal_places=2, default=100.0)
    passing_score = models.DecimalField(_('passing score'), max_digits=5, decimal_places=2, default=60.0)
    
    # Timing
    time_limit_minutes = models.PositiveIntegerField(_('time limit (minutes)'), null=True, blank=True)
    available_from = models.DateTimeField(_('available from'))
    available_until = models.DateTimeField(_('available until'))
    
    # Attempt Settings
    max_attempts = models.PositiveIntegerField(_('maximum attempts'), default=1)
    allow_review = models.BooleanField(_('allow review'), default=True)
    show_correct_answers = models.BooleanField(_('show correct answers'), default=False)
    show_score_immediately = models.BooleanField(_('show score immediately'), default=True)
    
    # Question Settings
    randomize_questions = models.BooleanField(_('randomize questions'), default=False)
    questions_per_page = models.PositiveIntegerField(_('questions per page'), default=1)
    
    # Status
    status = models.CharField(_('status'), max_length=20, choices=Status.choices, default=Status.DRAFT)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    published_at = models.DateTimeField(_('published at'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('Assessment')
        verbose_name_plural = _('Assessments')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['course', 'status']),
            models.Index(fields=['available_from', 'available_until']),
        ]
    
    def __str__(self):
        return f"{self.course.code} - {self.title}"
    
    @property
    def is_available(self):
        """Check if assessment is currently available"""
        from django.utils import timezone
        now = timezone.now()
        return (self.status == self.Status.ACTIVE and 
                self.available_from <= now <= self.available_until)
    
    @property
    def questions_count(self):
        return self.assessment_questions.count()
    
    @property
    def attempts_count(self):
        return self.attempts.count()


class AssessmentQuestion(models.Model):
    """
    Questions included in an assessment
    """
    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE, related_name='assessment_questions')
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='assessment_uses')
    
    # Question-specific settings for this assessment
    points = models.DecimalField(_('points'), max_digits=5, decimal_places=2)
    order_index = models.PositiveIntegerField(_('order'))
    is_required = models.BooleanField(_('required'), default=True)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Assessment Question')
        verbose_name_plural = _('Assessment Questions')
        ordering = ['order_index']
        unique_together = ['assessment', 'question']
    
    def __str__(self):
        return f"{self.assessment.title} - Q{self.order_index}"


class AssessmentAttempt(models.Model):
    """
    Student attempts at assessments
    """
    class Status(models.TextChoices):
        IN_PROGRESS = 'in_progress', _('In Progress')
        SUBMITTED = 'submitted', _('Submitted')
        GRADED = 'graded', _('Graded')
        EXPIRED = 'expired', _('Expired')

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Associations
    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE, related_name='attempts')
    student = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assessment_attempts')

    # Attempt Information
    attempt_number = models.PositiveIntegerField(_('attempt number'))
    status = models.CharField(_('status'), max_length=20, choices=Status.choices, default=Status.IN_PROGRESS)

    # Scoring
    total_points = models.DecimalField(_('total points'), max_digits=8, decimal_places=2, default=0.0)
    max_points = models.DecimalField(_('maximum points'), max_digits=8, decimal_places=2)
    percentage_score = models.DecimalField(_('percentage score'), max_digits=5, decimal_places=2, null=True, blank=True)
    passed = models.BooleanField(_('passed'), default=False)

    # Timing
    started_at = models.DateTimeField(_('started at'), auto_now_add=True)
    submitted_at = models.DateTimeField(_('submitted at'), null=True, blank=True)
    time_spent_minutes = models.PositiveIntegerField(_('time spent (minutes)'), default=0)

    # Settings snapshot (in case assessment settings change)
    time_limit_minutes = models.PositiveIntegerField(_('time limit (minutes)'), null=True, blank=True)
    randomized_questions = models.JSONField(_('randomized questions'), default=list, help_text=_('Question order for this attempt'))

    # Grading
    auto_graded = models.BooleanField(_('auto graded'), default=False)
    manually_graded = models.BooleanField(_('manually graded'), default=False)
    graded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='graded_attempts')
    graded_at = models.DateTimeField(_('graded at'), null=True, blank=True)

    # Feedback
    instructor_feedback = models.TextField(_('instructor feedback'), blank=True)

    class Meta:
        verbose_name = _('Assessment Attempt')
        verbose_name_plural = _('Assessment Attempts')
        ordering = ['-started_at']
        unique_together = ['assessment', 'student', 'attempt_number']
        indexes = [
            models.Index(fields=['student', 'status']),
            models.Index(fields=['assessment', 'status']),
        ]

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.assessment.title} (Attempt {self.attempt_number})"

    def calculate_score(self):
        """Calculate the total score for this attempt"""
        responses = self.responses.all()
        total_points = sum(response.points_earned for response in responses)
        self.total_points = total_points

        if self.max_points > 0:
            self.percentage_score = (total_points / self.max_points) * 100
            self.passed = self.percentage_score >= self.assessment.passing_score

        self.save()
        return self.total_points

    @property
    def is_expired(self):
        """Check if attempt has expired"""
        if not self.time_limit_minutes:
            return False

        from django.utils import timezone
        time_limit = timezone.timedelta(minutes=self.time_limit_minutes)
        return timezone.now() > (self.started_at + time_limit)

    @property
    def time_remaining_minutes(self):
        """Get remaining time in minutes"""
        if not self.time_limit_minutes:
            return None

        from django.utils import timezone
        elapsed = timezone.now() - self.started_at
        remaining = timezone.timedelta(minutes=self.time_limit_minutes) - elapsed
        return max(0, int(remaining.total_seconds() / 60))


class QuestionResponse(models.Model):
    """
    Student responses to individual questions
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Associations
    attempt = models.ForeignKey(AssessmentAttempt, on_delete=models.CASCADE, related_name='responses')
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='responses')
    assessment_question = models.ForeignKey(AssessmentQuestion, on_delete=models.CASCADE, related_name='responses')

    # Response Data
    student_answer = models.JSONField(_('student answer'), help_text=_('Student\'s answer in appropriate format'))
    is_correct = models.BooleanField(_('is correct'), default=False)
    points_earned = models.DecimalField(_('points earned'), max_digits=5, decimal_places=2, default=0.0)

    # Timing
    time_spent_seconds = models.PositiveIntegerField(_('time spent (seconds)'), default=0)
    answered_at = models.DateTimeField(_('answered at'), auto_now=True)

    # Manual grading (for essay questions, etc.)
    requires_manual_grading = models.BooleanField(_('requires manual grading'), default=False)
    manually_graded = models.BooleanField(_('manually graded'), default=False)
    grader_feedback = models.TextField(_('grader feedback'), blank=True)

    class Meta:
        verbose_name = _('Question Response')
        verbose_name_plural = _('Question Responses')
        ordering = ['assessment_question__order_index']
        unique_together = ['attempt', 'question']

    def __str__(self):
        return f"{self.attempt.student.get_display_name()} - {self.question.question_text[:30]}..."

    def grade_response(self):
        """Auto-grade the response if possible"""
        if self.question.question_type in [QuestionType.ESSAY, QuestionType.SHORT_ANSWER]:
            # These typically require manual grading
            self.requires_manual_grading = True
            if self.question.question_type == QuestionType.SHORT_ANSWER:
                # Try auto-grading short answers
                self.is_correct = self.question.check_answer(self.student_answer)
                if self.is_correct:
                    self.points_earned = self.assessment_question.points
                    self.requires_manual_grading = False
        else:
            # Auto-gradeable question types
            self.is_correct = self.question.check_answer(self.student_answer)
            if self.is_correct:
                self.points_earned = self.assessment_question.points

        self.save()
        return self.points_earned
