from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    QuestionBank, Question, Assessment, AssessmentQuestion,
    AssessmentAttempt, QuestionResponse, QuestionType, DifficultyLevel
)

User = get_user_model()


class QuestionBankSerializer(serializers.ModelSerializer):
    questions_count = serializers.ReadOnlyField()
    created_by_name = serializers.CharField(source='created_by.get_display_name', read_only=True)
    course_name = serializers.CharField(source='course.title', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    
    class Meta:
        model = QuestionBank
        fields = [
            'id', 'title', 'title_ar', 'description', 'description_ar',
            'course', 'course_name', 'course_code', 'created_by', 'created_by_name',
            'is_public', 'is_active', 'questions_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']


class QuestionBankCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionBank
        fields = [
            'title', 'title_ar', 'description', 'description_ar',
            'course', 'is_public', 'is_active'
        ]


class QuestionSerializer(serializers.ModelSerializer):
    question_bank_title = serializers.CharField(source='question_bank.title', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_display_name', read_only=True)
    
    class Meta:
        model = Question
        fields = [
            'id', 'question_text', 'question_text_ar', 'explanation', 'explanation_ar',
            'question_bank', 'question_bank_title', 'question_type', 'difficulty_level',
            'points', 'question_data', 'image', 'audio', 'tags', 'category',
            'learning_objective', 'is_active', 'randomize_options', 'times_used',
            'average_score', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'times_used', 'average_score', 'created_by', 'created_at', 'updated_at']
    
    def validate_question_data(self, value):
        """Validate question data based on question type"""
        question_type = self.initial_data.get('question_type')
        
        if question_type == QuestionType.MULTIPLE_CHOICE:
            if not value.get('options') or len(value.get('options', [])) < 2:
                raise serializers.ValidationError("Multiple choice questions must have at least 2 options")
            if not value.get('correct_answers'):
                raise serializers.ValidationError("Multiple choice questions must have correct answers")
        
        elif question_type == QuestionType.TRUE_FALSE:
            if 'correct_answer' not in value or value['correct_answer'] not in [True, False]:
                raise serializers.ValidationError("True/False questions must have a boolean correct answer")
        
        elif question_type == QuestionType.MATCHING:
            if not value.get('pairs') or len(value.get('pairs', [])) < 2:
                raise serializers.ValidationError("Matching questions must have at least 2 pairs")
        
        return value


class QuestionCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Question
        fields = [
            'question_text', 'question_text_ar', 'explanation', 'explanation_ar',
            'question_bank', 'question_type', 'difficulty_level', 'points',
            'question_data', 'image', 'audio', 'tags', 'category',
            'learning_objective', 'randomize_options'
        ]
    
    def validate_question_data(self, value):
        """Validate question data based on question type"""
        question_type = self.initial_data.get('question_type')
        
        if question_type == QuestionType.MULTIPLE_CHOICE:
            if not value.get('options') or len(value.get('options', [])) < 2:
                raise serializers.ValidationError("Multiple choice questions must have at least 2 options")
            if not value.get('correct_answers'):
                raise serializers.ValidationError("Multiple choice questions must have correct answers")
        
        elif question_type == QuestionType.TRUE_FALSE:
            if 'correct_answer' not in value or value['correct_answer'] not in [True, False]:
                raise serializers.ValidationError("True/False questions must have a boolean correct answer")
        
        return value


class AssessmentQuestionSerializer(serializers.ModelSerializer):
    question_text = serializers.CharField(source='question.question_text', read_only=True)
    question_type = serializers.CharField(source='question.question_type', read_only=True)
    question_data = serializers.JSONField(source='question.question_data', read_only=True)
    
    class Meta:
        model = AssessmentQuestion
        fields = [
            'id', 'question', 'question_text', 'question_type', 'question_data',
            'points', 'order_index', 'is_required', 'created_at'
        ]


class AssessmentSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.get_display_name', read_only=True)
    course_name = serializers.CharField(source='course.title', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    questions_count = serializers.ReadOnlyField()
    attempts_count = serializers.ReadOnlyField()
    is_available = serializers.ReadOnlyField()
    
    class Meta:
        model = Assessment
        fields = [
            'id', 'title', 'title_ar', 'description', 'description_ar',
            'instructions', 'instructions_ar', 'course', 'course_name', 'course_code',
            'created_by', 'created_by_name', 'assessment_type', 'total_points',
            'passing_score', 'time_limit_minutes', 'available_from', 'available_until',
            'max_attempts', 'allow_review', 'show_correct_answers', 'show_score_immediately',
            'randomize_questions', 'questions_per_page', 'status', 'questions_count',
            'attempts_count', 'is_available', 'created_at', 'updated_at', 'published_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at', 'published_at']


class AssessmentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assessment
        fields = [
            'title', 'title_ar', 'description', 'description_ar',
            'instructions', 'instructions_ar', 'course', 'assessment_type',
            'total_points', 'passing_score', 'time_limit_minutes',
            'available_from', 'available_until', 'max_attempts',
            'allow_review', 'show_correct_answers', 'show_score_immediately',
            'randomize_questions', 'questions_per_page'
        ]
    
    def validate(self, attrs):
        if attrs['available_from'] >= attrs['available_until']:
            raise serializers.ValidationError("Available from date must be before available until date")
        return attrs


class AssessmentAttemptSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_display_name', read_only=True)
    assessment_title = serializers.CharField(source='assessment.title', read_only=True)
    time_remaining_minutes = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = AssessmentAttempt
        fields = [
            'id', 'assessment', 'assessment_title', 'student', 'student_name',
            'attempt_number', 'status', 'total_points', 'max_points',
            'percentage_score', 'passed', 'started_at', 'submitted_at',
            'time_spent_minutes', 'time_limit_minutes', 'time_remaining_minutes',
            'is_expired', 'auto_graded', 'manually_graded', 'graded_by',
            'graded_at', 'instructor_feedback'
        ]
        read_only_fields = [
            'id', 'total_points', 'percentage_score', 'passed',
            'time_spent_minutes', 'auto_graded', 'manually_graded',
            'graded_at'
        ]


class QuestionResponseSerializer(serializers.ModelSerializer):
    question_text = serializers.CharField(source='question.question_text', read_only=True)
    question_type = serializers.CharField(source='question.question_type', read_only=True)
    max_points = serializers.DecimalField(source='assessment_question.points', max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = QuestionResponse
        fields = [
            'id', 'question', 'question_text', 'question_type',
            'student_answer', 'is_correct', 'points_earned', 'max_points',
            'time_spent_seconds', 'answered_at', 'requires_manual_grading',
            'manually_graded', 'grader_feedback'
        ]
        read_only_fields = [
            'id', 'is_correct', 'points_earned', 'answered_at',
            'requires_manual_grading', 'manually_graded'
        ]


class QuestionResponseCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionResponse
        fields = ['question', 'student_answer', 'time_spent_seconds']


class AssessmentDetailSerializer(AssessmentSerializer):
    """Detailed assessment serializer with questions"""
    questions = AssessmentQuestionSerializer(source='assessment_questions', many=True, read_only=True)

    class Meta(AssessmentSerializer.Meta):
        fields = AssessmentSerializer.Meta.fields + ['questions']


class AssessmentAttemptDetailSerializer(AssessmentAttemptSerializer):
    """Detailed attempt serializer with responses"""
    responses = QuestionResponseSerializer(many=True, read_only=True)
    
    class Meta(AssessmentAttemptSerializer.Meta):
        fields = AssessmentAttemptSerializer.Meta.fields + ['responses']


class AssessmentStatsSerializer(serializers.Serializer):
    """Assessment statistics"""
    total_attempts = serializers.IntegerField()
    completed_attempts = serializers.IntegerField()
    average_score = serializers.FloatField()
    highest_score = serializers.FloatField()
    lowest_score = serializers.FloatField()
    pass_rate = serializers.FloatField()
    average_time_minutes = serializers.FloatField()
    question_analytics = serializers.ListField()


class BulkQuestionImportSerializer(serializers.Serializer):
    """Serializer for bulk question import"""
    question_bank = serializers.UUIDField()
    questions = serializers.ListField(
        child=serializers.DictField(),
        min_length=1
    )
    
    def validate_questions(self, value):
        """Validate each question in the bulk import"""
        for i, question_data in enumerate(value):
            if not question_data.get('question_text'):
                raise serializers.ValidationError(f"Question {i+1}: question_text is required")
            
            if not question_data.get('question_type'):
                raise serializers.ValidationError(f"Question {i+1}: question_type is required")
            
            if question_data.get('question_type') not in [choice[0] for choice in QuestionType.choices]:
                raise serializers.ValidationError(f"Question {i+1}: invalid question_type")
        
        return value
