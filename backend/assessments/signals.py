from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import QuestionResponse, AssessmentAttempt, Question


@receiver(post_save, sender=QuestionResponse)
def update_question_statistics(sender, instance, created, **kwargs):
    """Update question usage statistics when a response is created"""
    if created:
        question = instance.question
        question.times_used += 1
        
        # Update average score
        responses = QuestionResponse.objects.filter(
            question=question,
            attempt__status='graded'
        )
        
        if responses.exists():
            correct_responses = responses.filter(is_correct=True).count()
            total_responses = responses.count()
            question.average_score = (correct_responses / total_responses) * 100
        
        question.save(update_fields=['times_used', 'average_score'])


@receiver(pre_save, sender=AssessmentAttempt)
def check_attempt_expiry(sender, instance, **kwargs):
    """Check if attempt should be marked as expired"""
    if (instance.status == AssessmentAttempt.Status.IN_PROGRESS and 
        instance.is_expired and 
        instance.pk):  # Only for existing instances
        instance.status = AssessmentAttempt.Status.EXPIRED
        instance.submitted_at = timezone.now()


@receiver(post_save, sender=QuestionResponse)
def auto_submit_completed_attempt(sender, instance, created, **kwargs):
    """Auto-submit attempt if all questions are answered"""
    if created:
        attempt = instance.attempt
        
        if attempt.status == AssessmentAttempt.Status.IN_PROGRESS:
            # Check if all questions are answered
            total_questions = attempt.assessment.questions_count
            answered_questions = attempt.responses.count()
            
            if answered_questions >= total_questions:
                # All questions answered, auto-submit
                attempt.submitted_at = timezone.now()
                attempt.status = AssessmentAttempt.Status.SUBMITTED
                
                # Calculate time spent
                time_spent = timezone.now() - attempt.started_at
                attempt.time_spent_minutes = int(time_spent.total_seconds() / 60)
                
                # Calculate score
                attempt.calculate_score()
                
                # Check if manual grading is needed
                manual_grading_needed = attempt.responses.filter(
                    requires_manual_grading=True
                ).exists()
                
                if not manual_grading_needed:
                    attempt.status = AssessmentAttempt.Status.GRADED
                    attempt.auto_graded = True
                
                attempt.save()
