from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'question-banks', views.QuestionBankViewSet, basename='questionbank')
router.register(r'questions', views.QuestionViewSet, basename='question')
router.register(r'assessments', views.AssessmentViewSet, basename='assessment')
router.register(r'attempts', views.AssessmentAttemptViewSet, basename='assessmentattempt')

urlpatterns = [
    # Router URLs
    path('', include(router.urls)),
    
    # Bulk operations
    path('questions/bulk-import/', views.bulk_import_questions, name='bulk-import-questions'),
]
