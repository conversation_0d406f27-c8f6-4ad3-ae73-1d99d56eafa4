from rest_framework import generics, status, permissions, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q, Count, Avg, Max, Min
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from courses.models import Course
from .models import (
    QuestionBank, Question, Assessment, AssessmentQuestion,
    AssessmentAttempt, QuestionResponse, QuestionType
)
from .serializers import (
    QuestionBankSerializer, QuestionBankCreateSerializer,
    QuestionSerializer, QuestionCreateSerializer,
    AssessmentSerializer, AssessmentCreateSerializer, AssessmentDetailSerializer,
    AssessmentQuestionSerializer, AssessmentAttemptSerializer,
    AssessmentAttemptDetailSerializer, QuestionResponseSerializer,
    QuestionResponseCreateSerializer, AssessmentStatsSerializer,
    BulkQuestionImportSerializer
)

User = get_user_model()


class QuestionBankViewSet(viewsets.ModelViewSet):
    """ViewSet for question banks"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionBankCreateSerializer
        return QuestionBankSerializer
    
    def get_queryset(self):
        user = self.request.user
        queryset = QuestionBank.objects.select_related('course', 'created_by')
        
        # Filter based on user role
        if user.role == 'student':
            # Students can't access question banks directly
            return queryset.none()
        elif user.role == 'teacher':
            # Teachers see their own banks and public ones from their courses
            teaching_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            queryset = queryset.filter(
                Q(created_by=user) | 
                Q(is_public=True, course_id__in=teaching_courses)
            )
        
        # Filter by course if specified
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        
        return queryset.filter(is_active=True).order_by('-updated_at')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['get'])
    def questions(self, request, pk=None):
        """Get questions in this bank"""
        question_bank = self.get_object()
        questions = question_bank.questions.filter(is_active=True)
        
        # Filter by question type
        question_type = request.query_params.get('type')
        if question_type:
            questions = questions.filter(question_type=question_type)
        
        # Filter by difficulty
        difficulty = request.query_params.get('difficulty')
        if difficulty:
            questions = questions.filter(difficulty_level=difficulty)
        
        serializer = QuestionSerializer(questions, many=True)
        return Response(serializer.data)


class QuestionViewSet(viewsets.ModelViewSet):
    """ViewSet for questions"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionCreateSerializer
        return QuestionSerializer
    
    def get_queryset(self):
        user = self.request.user
        queryset = Question.objects.select_related('question_bank', 'created_by')
        
        # Filter based on user role
        if user.role == 'student':
            # Students can't access questions directly
            return queryset.none()
        elif user.role == 'teacher':
            # Teachers see questions from their banks and public banks
            teaching_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            queryset = queryset.filter(
                Q(created_by=user) |
                Q(question_bank__is_public=True, question_bank__course_id__in=teaching_courses)
            )
        
        # Filter by question bank
        bank_id = self.request.query_params.get('bank_id')
        if bank_id:
            queryset = queryset.filter(question_bank_id=bank_id)
        
        return queryset.filter(is_active=True).order_by('-updated_at')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def duplicate(self, request, pk=None):
        """Duplicate a question"""
        original_question = self.get_object()
        
        # Create a copy
        new_question = Question.objects.create(
            question_text=f"Copy of {original_question.question_text}",
            question_text_ar=original_question.question_text_ar,
            explanation=original_question.explanation,
            explanation_ar=original_question.explanation_ar,
            question_bank=original_question.question_bank,
            question_type=original_question.question_type,
            difficulty_level=original_question.difficulty_level,
            points=original_question.points,
            question_data=original_question.question_data,
            tags=original_question.tags,
            category=original_question.category,
            learning_objective=original_question.learning_objective,
            randomize_options=original_question.randomize_options,
            created_by=request.user
        )
        
        serializer = QuestionSerializer(new_question)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class AssessmentViewSet(viewsets.ModelViewSet):
    """ViewSet for assessments"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'retrieve':
            return AssessmentDetailSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return AssessmentCreateSerializer
        return AssessmentSerializer
    
    def get_queryset(self):
        user = self.request.user
        queryset = Assessment.objects.select_related('course', 'created_by')
        
        # Filter based on user role
        if user.role == 'student':
            # Students see published assessments from their enrolled courses
            enrolled_courses = user.enrollments.filter(
                status='enrolled',
                is_active=True
            ).values_list('course_id', flat=True)
            queryset = queryset.filter(
                course_id__in=enrolled_courses,
                status__in=['published', 'active']
            )
        elif user.role == 'teacher':
            # Teachers see assessments from courses they teach
            teaching_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            queryset = queryset.filter(course_id__in=teaching_courses)
        
        # Filter by course
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publish an assessment"""
        assessment = self.get_object()
        
        if assessment.created_by != request.user and request.user.role not in ['admin', 'super_admin']:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if assessment.questions_count == 0:
            return Response(
                {'error': 'Cannot publish assessment without questions'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        assessment.status = Assessment.Status.PUBLISHED
        assessment.published_at = timezone.now()
        assessment.save()
        
        serializer = AssessmentSerializer(assessment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_questions(self, request, pk=None):
        """Add questions to assessment"""
        assessment = self.get_object()
        question_ids = request.data.get('question_ids', [])
        
        if not question_ids:
            return Response(
                {'error': 'No questions provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the next order index
        last_order = assessment.assessment_questions.aggregate(
            max_order=Max('order_index')
        )['max_order'] or 0
        
        created_questions = []
        for i, question_id in enumerate(question_ids):
            try:
                question = Question.objects.get(id=question_id)
                assessment_question, created = AssessmentQuestion.objects.get_or_create(
                    assessment=assessment,
                    question=question,
                    defaults={
                        'points': question.points,
                        'order_index': last_order + i + 1
                    }
                )
                if created:
                    created_questions.append(assessment_question)
            except Question.DoesNotExist:
                continue
        
        serializer = AssessmentQuestionSerializer(created_questions, many=True)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """Get assessment statistics"""
        assessment = self.get_object()
        attempts = assessment.attempts.filter(status='graded')
        
        if not attempts.exists():
            return Response({
                'total_attempts': 0,
                'completed_attempts': 0,
                'average_score': 0,
                'highest_score': 0,
                'lowest_score': 0,
                'pass_rate': 0,
                'average_time_minutes': 0,
                'question_analytics': []
            })
        
        stats = attempts.aggregate(
            avg_score=Avg('percentage_score'),
            max_score=Max('percentage_score'),
            min_score=Min('percentage_score'),
            avg_time=Avg('time_spent_minutes')
        )
        
        pass_count = attempts.filter(passed=True).count()
        pass_rate = (pass_count / attempts.count()) * 100
        
        # Question analytics
        question_analytics = []
        for aq in assessment.assessment_questions.all():
            responses = QuestionResponse.objects.filter(
                assessment_question=aq,
                attempt__status='graded'
            )
            if responses.exists():
                correct_count = responses.filter(is_correct=True).count()
                total_count = responses.count()
                question_analytics.append({
                    'question_id': str(aq.question.id),
                    'question_text': aq.question.question_text[:50] + '...',
                    'correct_percentage': (correct_count / total_count) * 100,
                    'average_time': responses.aggregate(avg=Avg('time_spent_seconds'))['avg'] or 0
                })
        
        data = {
            'total_attempts': assessment.attempts.count(),
            'completed_attempts': attempts.count(),
            'average_score': round(stats['avg_score'] or 0, 2),
            'highest_score': round(stats['max_score'] or 0, 2),
            'lowest_score': round(stats['min_score'] or 0, 2),
            'pass_rate': round(pass_rate, 2),
            'average_time_minutes': round(stats['avg_time'] or 0, 2),
            'question_analytics': question_analytics
        }
        
        serializer = AssessmentStatsSerializer(data)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def attempt(self, request, pk=None):
        """Get or check existing attempt for student"""
        assessment = self.get_object()

        # Check if student has an active attempt
        try:
            attempt = AssessmentAttempt.objects.get(
                assessment=assessment,
                student=request.user,
                status='in_progress'
            )
            serializer = AssessmentAttemptDetailSerializer(attempt)
            return Response(serializer.data)
        except AssessmentAttempt.DoesNotExist:
            return Response(
                {'message': 'No active attempt found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """Start a new assessment attempt"""
        assessment = self.get_object()

        # Check if assessment is available
        if not assessment.is_available:
            return Response(
                {'error': 'Assessment is not available'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if student has reached max attempts
        attempt_count = AssessmentAttempt.objects.filter(
            assessment=assessment,
            student=request.user
        ).count()

        if assessment.max_attempts and attempt_count >= assessment.max_attempts:
            return Response(
                {'error': 'Maximum attempts reached'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if student has an active attempt
        active_attempt = AssessmentAttempt.objects.filter(
            assessment=assessment,
            student=request.user,
            status='in_progress'
        ).first()

        if active_attempt:
            serializer = AssessmentAttemptDetailSerializer(active_attempt)
            return Response(serializer.data)

        # Create new attempt
        attempt = AssessmentAttempt.objects.create(
            assessment=assessment,
            student=request.user,
            attempt_number=attempt_count + 1,
            started_at=timezone.now(),
            status='in_progress',
            max_points=assessment.total_points,
            time_limit_minutes=assessment.time_limit_minutes
        )

        serializer = AssessmentAttemptDetailSerializer(attempt)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class AssessmentAttemptViewSet(viewsets.ModelViewSet):
    """ViewSet for assessment attempts"""
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return AssessmentAttemptDetailSerializer
        return AssessmentAttemptSerializer

    def get_queryset(self):
        user = self.request.user
        queryset = AssessmentAttempt.objects.select_related('assessment', 'student')

        # Filter based on user role
        if user.role == 'student':
            # Students see only their own attempts
            queryset = queryset.filter(student=user)
        elif user.role == 'teacher':
            # Teachers see attempts from their courses
            teaching_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            queryset = queryset.filter(assessment__course_id__in=teaching_courses)

        # Filter by assessment
        assessment_id = self.request.query_params.get('assessment_id')
        if assessment_id:
            queryset = queryset.filter(assessment_id=assessment_id)

        # Filter by student
        student_id = self.request.query_params.get('student_id')
        if student_id and user.role in ['teacher', 'admin', 'super_admin']:
            queryset = queryset.filter(student_id=student_id)

        return queryset.order_by('-started_at')

    @action(detail=False, methods=['post'])
    def start_attempt(self, request):
        """Start a new assessment attempt"""
        assessment_id = request.data.get('assessment_id')

        if not assessment_id:
            return Response(
                {'error': 'Assessment ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            assessment = Assessment.objects.get(id=assessment_id)
        except Assessment.DoesNotExist:
            return Response(
                {'error': 'Assessment not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if assessment is available
        if not assessment.is_available:
            return Response(
                {'error': 'Assessment is not currently available'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if student has exceeded max attempts
        existing_attempts = AssessmentAttempt.objects.filter(
            assessment=assessment,
            student=request.user
        ).count()

        if existing_attempts >= assessment.max_attempts:
            return Response(
                {'error': 'Maximum attempts exceeded'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if student has an active attempt
        active_attempt = AssessmentAttempt.objects.filter(
            assessment=assessment,
            student=request.user,
            status=AssessmentAttempt.Status.IN_PROGRESS
        ).first()

        if active_attempt:
            serializer = AssessmentAttemptDetailSerializer(active_attempt)
            return Response(serializer.data)

        # Create new attempt
        attempt = AssessmentAttempt.objects.create(
            assessment=assessment,
            student=request.user,
            attempt_number=existing_attempts + 1,
            max_points=assessment.total_points,
            time_limit_minutes=assessment.time_limit_minutes
        )

        # Randomize questions if needed
        if assessment.randomize_questions:
            questions = list(assessment.assessment_questions.all())
            import random
            random.shuffle(questions)
            attempt.randomized_questions = [str(q.id) for q in questions]
            attempt.save()

        serializer = AssessmentAttemptDetailSerializer(attempt)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def submit_response(self, request, pk=None):
        """Submit a response to a question"""
        attempt = self.get_object()

        if attempt.student != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        if attempt.status != AssessmentAttempt.Status.IN_PROGRESS:
            return Response(
                {'error': 'Attempt is not in progress'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if attempt.is_expired:
            attempt.status = AssessmentAttempt.Status.EXPIRED
            attempt.save()
            return Response(
                {'error': 'Attempt has expired'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = QuestionResponseCreateSerializer(data=request.data)
        if serializer.is_valid():
            # Get the assessment question
            try:
                assessment_question = AssessmentQuestion.objects.get(
                    assessment=attempt.assessment,
                    question_id=serializer.validated_data['question'].id
                )
            except AssessmentQuestion.DoesNotExist:
                return Response(
                    {'error': 'Question not found in this assessment'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create or update response
            response, created = QuestionResponse.objects.update_or_create(
                attempt=attempt,
                question=serializer.validated_data['question'],
                defaults={
                    'assessment_question': assessment_question,
                    'student_answer': serializer.validated_data['student_answer'],
                    'time_spent_seconds': serializer.validated_data.get('time_spent_seconds', 0)
                }
            )

            # Auto-grade the response
            response.grade_response()

            response_serializer = QuestionResponseSerializer(response)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def submit_attempt(self, request, pk=None):
        """Submit the entire attempt"""
        attempt = self.get_object()

        if attempt.student != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        if attempt.status != AssessmentAttempt.Status.IN_PROGRESS:
            return Response(
                {'error': 'Attempt is not in progress'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Calculate time spent
        time_spent = timezone.now() - attempt.started_at
        attempt.time_spent_minutes = int(time_spent.total_seconds() / 60)
        attempt.submitted_at = timezone.now()
        attempt.status = AssessmentAttempt.Status.SUBMITTED

        # Calculate score
        attempt.calculate_score()

        # Check if all responses can be auto-graded
        manual_grading_needed = attempt.responses.filter(requires_manual_grading=True).exists()
        if not manual_grading_needed:
            attempt.status = AssessmentAttempt.Status.GRADED
            attempt.auto_graded = True

        attempt.save()

        serializer = AssessmentAttemptDetailSerializer(attempt)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def save(self, request, pk=None):
        """Save answers for an assessment attempt"""
        attempt = self.get_object()

        # Check if attempt belongs to current user
        if attempt.student != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if attempt is still in progress
        if attempt.status != AssessmentAttempt.Status.IN_PROGRESS:
            return Response(
                {'error': 'Attempt is not in progress'},
                status=status.HTTP_400_BAD_REQUEST
            )

        answers = request.data.get('answers', {})

        # Save answers
        for question_id, answer_data in answers.items():
            try:
                assessment_question = AssessmentQuestion.objects.get(
                    assessment=attempt.assessment,
                    question_id=question_id
                )

                # Update or create question response
                response, created = QuestionResponse.objects.update_or_create(
                    attempt=attempt,
                    assessment_question=assessment_question,
                    defaults={
                        'response_data': answer_data,
                        'updated_at': timezone.now()
                    }
                )
            except AssessmentQuestion.DoesNotExist:
                continue

        # Update attempt timestamp
        attempt.last_activity_at = timezone.now()
        attempt.save()

        return Response({'message': 'Answers saved successfully'})

    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """Submit an assessment attempt"""
        attempt = self.get_object()

        # Check if attempt belongs to current user
        if attempt.student != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if attempt is still in progress
        if attempt.status != AssessmentAttempt.Status.IN_PROGRESS:
            return Response(
                {'error': 'Attempt is not in progress'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Save final answers if provided
        answers = request.data.get('answers', {})
        if answers:
            for question_id, answer_data in answers.items():
                try:
                    assessment_question = AssessmentQuestion.objects.get(
                        assessment=attempt.assessment,
                        question_id=question_id
                    )

                    QuestionResponse.objects.update_or_create(
                        attempt=attempt,
                        assessment_question=assessment_question,
                        defaults={
                            'response_data': answer_data,
                            'updated_at': timezone.now()
                        }
                    )
                except AssessmentQuestion.DoesNotExist:
                    continue

        # Mark attempt as submitted
        attempt.status = AssessmentAttempt.Status.SUBMITTED
        attempt.submitted_at = timezone.now()

        # Calculate time spent
        if attempt.started_at:
            time_spent = timezone.now() - attempt.started_at
            attempt.time_spent_minutes = int(time_spent.total_seconds() / 60)

        # Auto-grade if possible
        attempt.auto_grade()

        attempt.save()

        serializer = AssessmentAttemptDetailSerializer(attempt)
        return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def bulk_import_questions(request):
    """Bulk import questions from JSON/CSV"""
    serializer = BulkQuestionImportSerializer(data=request.data)

    if serializer.is_valid():
        question_bank_id = serializer.validated_data['question_bank']
        questions_data = serializer.validated_data['questions']

        try:
            question_bank = QuestionBank.objects.get(id=question_bank_id)
        except QuestionBank.DoesNotExist:
            return Response(
                {'error': 'Question bank not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check permissions
        if (question_bank.created_by != request.user and
            request.user.role not in ['admin', 'super_admin']):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        created_questions = []
        errors = []

        for i, question_data in enumerate(questions_data):
            try:
                question = Question.objects.create(
                    question_bank=question_bank,
                    created_by=request.user,
                    **question_data
                )
                created_questions.append(question)
            except Exception as e:
                errors.append(f"Question {i+1}: {str(e)}")

        return Response({
            'created_count': len(created_questions),
            'errors': errors,
            'questions': QuestionSerializer(created_questions, many=True).data
        }, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
