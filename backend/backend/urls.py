"""
URL configuration for UMLS backend project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse

def health_check(request):
    """Simple health check endpoint for mobile app"""
    return JsonResponse({
        'status': 'healthy',
        'message': 'UMLS Backend is running',
        'version': '1.0.0'
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/health/', health_check, name='health_check'),
    path('api/auth/', include('authentication.urls')),
    path('api/users/', include('users.urls')),
    path('api/courses/', include('courses.urls')),
    path('api/assignments/', include('assignments.urls')),
    path('api/grades/', include('grades.urls')),
    path('api/exams/', include('exams.urls')),
    path('api/notifications/', include('notifications.urls')),
    path('api/study-groups/', include('study_groups.urls')),
    path('api/communications/', include('communications.urls')),
    path('api/meetings/', include('meetings.urls')),
    path('api/pdf/', include('apps.pdf_service.urls')),  # PDF service enabled
    path('api/analytics/', include('academic_analytics.urls')),
    path('api/payments/', include('payments.urls')),
    path('api/library/', include('library.urls')),
    path('api/attendance/', include('attendance.urls')),
    path('api/schedules/', include('schedules.urls')),
    path('api/requests/', include('requests_urls')),
    path('api/content/', include('content_management.urls')),  # Content management
    path('api/forums/', include('forums.urls')),  # Discussion forums
    path('api/assessments/', include('assessments.urls')),  # Assessment system
    path('api/video-streaming/', include('video_streaming.urls')),  # Video streaming
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
