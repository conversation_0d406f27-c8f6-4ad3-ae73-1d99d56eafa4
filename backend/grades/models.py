from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from courses.models import Course, Enrollment
from assignments.models import Assignment
from exams.models import Exam
from decimal import Decimal
from django.core.exceptions import ValidationError

User = get_user_model()


class GradingScale(models.Model):
    """
    Grading scales and letter grade mappings
    """
    name = models.CharField(_('name'), max_length=100)
    name_ar = models.CharField(_('name (Arabic)'), max_length=100, blank=True)
    description = models.TextField(_('description'), blank=True)
    is_default = models.BooleanField(_('default'), default=False)
    is_active = models.BooleanField(_('active'), default=True)

    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Grading Scale')
        verbose_name_plural = _('Grading Scales')
        ordering = ['name']

    def __str__(self):
        return self.name

    @classmethod
    def get_default_scale(cls):
        """Get the default grading scale"""
        default_scale = cls.objects.filter(is_default=True, is_active=True).first()
        if not default_scale:
            # Create a default scale if none exists
            default_scale = cls.objects.create(
                name='Standard Scale',
                name_ar='المقياس القياسي',
                description='Standard 4.0 GPA scale',
                is_default=True,
                is_active=True
            )
            # Create default grade ranges
            grade_ranges = [
                ('A+', 97, 100, 4.0),
                ('A', 93, 96, 4.0),
                ('A-', 90, 92, 3.7),
                ('B+', 87, 89, 3.3),
                ('B', 83, 86, 3.0),
                ('B-', 80, 82, 2.7),
                ('C+', 77, 79, 2.3),
                ('C', 73, 76, 2.0),
                ('C-', 70, 72, 1.7),
                ('D+', 67, 69, 1.3),
                ('D', 60, 66, 1.0),
                ('F', 0, 59, 0.0),
            ]
            for letter, min_pct, max_pct, points in grade_ranges:
                GradeRange.objects.create(
                    grading_scale=default_scale,
                    letter_grade=letter,
                    min_percentage=min_pct,
                    max_percentage=max_pct,
                    grade_points=points
                )
        return default_scale

    def get_letter_grade(self, percentage):
        """Get letter grade for a percentage"""
        grade_range = self.grade_ranges.filter(
            min_percentage__lte=percentage,
            max_percentage__gte=percentage
        ).first()
        return grade_range.letter_grade if grade_range else 'F'

    def get_gpa_points(self, letter_grade):
        """Get GPA points for a letter grade"""
        grade_range = self.grade_ranges.filter(letter_grade=letter_grade).first()
        return float(grade_range.grade_points) if grade_range else 0.0

    def save(self, *args, **kwargs):
        if self.is_default:
            # Ensure only one default grading scale
            GradingScale.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class GradeRange(models.Model):
    """
    Grade ranges for letter grades
    """
    grading_scale = models.ForeignKey(GradingScale, on_delete=models.CASCADE, related_name='grade_ranges')
    letter_grade = models.CharField(_('letter grade'), max_length=5)
    min_percentage = models.DecimalField(_('minimum percentage'), max_digits=5, decimal_places=2)
    max_percentage = models.DecimalField(_('maximum percentage'), max_digits=5, decimal_places=2)
    grade_points = models.DecimalField(_('grade points'), max_digits=4, decimal_places=2)
    description = models.CharField(_('description'), max_length=100, blank=True)

    created_at = models.DateTimeField(_('created at'), auto_now_add=True)

    class Meta:
        verbose_name = _('Grade Range')
        verbose_name_plural = _('Grade Ranges')
        ordering = ['-min_percentage']
        unique_together = ['grading_scale', 'letter_grade']

    def __str__(self):
        return f"{self.letter_grade} ({self.min_percentage}% - {self.max_percentage}%)"

    def clean(self):
        if self.min_percentage >= self.max_percentage:
            raise ValidationError(_('Minimum percentage must be less than maximum percentage'))


class GradeComponent(models.Model):
    """
    Grade components for courses (assignments, exams, participation, etc.)
    """
    class ComponentType(models.TextChoices):
        ASSIGNMENT = 'assignment', _('Assignment')
        EXAM = 'exam', _('Exam')
        QUIZ = 'quiz', _('Quiz')
        PROJECT = 'project', _('Project')
        PARTICIPATION = 'participation', _('Participation')
        ATTENDANCE = 'attendance', _('Attendance')
        LAB_WORK = 'lab_work', _('Lab Work')
        PRESENTATION = 'presentation', _('Presentation')
        OTHER = 'other', _('Other')

    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='grade_components')
    name = models.CharField(_('name'), max_length=100)
    name_ar = models.CharField(_('name (Arabic)'), max_length=100, blank=True)
    component_type = models.CharField(_('component type'), max_length=20, choices=ComponentType.choices)
    weight_percentage = models.DecimalField(_('weight percentage'), max_digits=5, decimal_places=2)
    max_points = models.DecimalField(_('maximum points'), max_digits=8, decimal_places=2, default=Decimal('100.00'))

    # Settings
    is_required = models.BooleanField(_('required'), default=True)
    drop_lowest = models.PositiveIntegerField(_('drop lowest scores'), default=0)
    is_active = models.BooleanField(_('active'), default=True)

    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Grade Component')
        verbose_name_plural = _('Grade Components')
        ordering = ['course', '-weight_percentage']

    def __str__(self):
        return f"{self.course.code} - {self.name} ({self.weight_percentage}%)"


class Grade(models.Model):
    """
    Individual grades for students
    """
    class GradeStatus(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        SUBMITTED = 'submitted', _('Submitted')
        PUBLISHED = 'published', _('Published')
        DISPUTED = 'disputed', _('Disputed')

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='grades',
        limit_choices_to={'role': 'student'}
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='grades')
    grade_component = models.ForeignKey(GradeComponent, on_delete=models.CASCADE, related_name='grades')

    # Grade Information
    points_earned = models.DecimalField(_('points earned'), max_digits=8, decimal_places=2, null=True, blank=True)
    points_possible = models.DecimalField(_('points possible'), max_digits=8, decimal_places=2)
    percentage = models.DecimalField(_('percentage'), max_digits=5, decimal_places=2, null=True, blank=True)
    letter_grade = models.CharField(_('letter grade'), max_length=5, blank=True)

    # Additional Information
    comments = models.TextField(_('comments'), blank=True)
    is_excused = models.BooleanField(_('excused'), default=False)
    is_late = models.BooleanField(_('late submission'), default=False)
    late_penalty = models.DecimalField(_('late penalty'), max_digits=5, decimal_places=2, default=Decimal('0.00'))

    # Status
    status = models.CharField(_('status'), max_length=20, choices=GradeStatus.choices, default=GradeStatus.DRAFT)

    # Grading Information
    graded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='graded_items',
        limit_choices_to={'role__in': ['teacher', 'admin']}
    )
    graded_at = models.DateTimeField(_('graded at'), null=True, blank=True)

    # References
    assignment = models.ForeignKey(Assignment, on_delete=models.SET_NULL, null=True, blank=True, related_name='grades')
    exam = models.ForeignKey(Exam, on_delete=models.SET_NULL, null=True, blank=True, related_name='grades')

    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Grade')
        verbose_name_plural = _('Grades')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['student', 'course']),
            models.Index(fields=['course', 'grade_component']),
            models.Index(fields=['status', 'graded_at']),
        ]

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.course.code} - {self.grade_component.name}"

    def save(self, *args, **kwargs):
        # Calculate percentage
        if self.points_earned is not None and self.points_possible > 0:
            # Apply late penalty
            adjusted_points = self.points_earned - (self.points_earned * self.late_penalty / 100)
            self.percentage = (adjusted_points / self.points_possible) * 100

            # Calculate letter grade
            self.letter_grade = self.calculate_letter_grade()

        super().save(*args, **kwargs)

    def calculate_letter_grade(self):
        """Calculate letter grade based on percentage"""
        if self.percentage is None:
            return ''

        # Get grading scale for the course or use default
        grading_scale = getattr(self.course, 'grading_scale', None)
        if not grading_scale:
            grading_scale = GradingScale.objects.filter(is_default=True).first()

        if grading_scale:
            grade_range = grading_scale.grade_ranges.filter(
                min_percentage__lte=self.percentage,
                max_percentage__gte=self.percentage
            ).first()

            if grade_range:
                return grade_range.letter_grade

        # Fallback to standard grading
        percentage = float(self.percentage)
        if percentage >= 90:
            return 'A+'
        elif percentage >= 85:
            return 'A'
        elif percentage >= 80:
            return 'B+'
        elif percentage >= 75:
            return 'B'
        elif percentage >= 70:
            return 'C+'
        elif percentage >= 65:
            return 'C'
        elif percentage >= 60:
            return 'D'
        else:
            return 'F'


class CourseGrade(models.Model):
    """
    Final course grades for students
    """
    class GradeStatus(models.TextChoices):
        IN_PROGRESS = 'in_progress', _('In Progress')
        COMPLETED = 'completed', _('Completed')
        INCOMPLETE = 'incomplete', _('Incomplete')
        WITHDRAWN = 'withdrawn', _('Withdrawn')
        AUDIT = 'audit', _('Audit')

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='course_grades',
        limit_choices_to={'role': 'student'}
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='final_grades')
    enrollment = models.OneToOneField(Enrollment, on_delete=models.CASCADE, related_name='course_grade')

    # Grade Information
    total_points_earned = models.DecimalField(_('total points earned'), max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_points_possible = models.DecimalField(_('total points possible'), max_digits=10, decimal_places=2, default=Decimal('0.00'))
    final_percentage = models.DecimalField(_('final percentage'), max_digits=5, decimal_places=2, null=True, blank=True)
    letter_grade = models.CharField(_('letter grade'), max_length=5, blank=True)
    grade_points = models.DecimalField(_('grade points'), max_digits=4, decimal_places=2, null=True, blank=True)

    # Status
    status = models.CharField(_('status'), max_length=20, choices=GradeStatus.choices, default=GradeStatus.IN_PROGRESS)
    is_published = models.BooleanField(_('published'), default=False)

    # Additional Information
    comments = models.TextField(_('comments'), blank=True)

    # Timestamps
    calculated_at = models.DateTimeField(_('calculated at'), null=True, blank=True)
    published_at = models.DateTimeField(_('published at'), null=True, blank=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Course Grade')
        verbose_name_plural = _('Course Grades')
        ordering = ['-updated_at']
        unique_together = ['student', 'course']
        indexes = [
            models.Index(fields=['student', '-updated_at']),
            models.Index(fields=['course', 'status']),
        ]

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.course.code} ({self.letter_grade})"

    def calculate_final_grade(self):
        """Calculate final grade based on all grade components"""
        from django.db.models import Sum, Avg

        # Get all grade components for the course
        components = self.course.grade_components.filter(is_active=True)
        total_weight = 0
        weighted_score = 0

        for component in components:
            # Get student's grades for this component
            grades = Grade.objects.filter(
                student=self.student,
                course=self.course,
                grade_component=component,
                status=Grade.GradeStatus.PUBLISHED
            ).exclude(is_excused=True)

            if grades.exists():
                if component.drop_lowest > 0:
                    # Drop lowest scores
                    grades = grades.order_by('-percentage')[:-component.drop_lowest]

                # Calculate average for this component
                avg_percentage = grades.aggregate(avg=Avg('percentage'))['avg'] or 0

                # Add to weighted score
                weighted_score += (avg_percentage * component.weight_percentage / 100)
                total_weight += component.weight_percentage

        if total_weight > 0:
            self.final_percentage = (weighted_score / total_weight) * 100
            self.letter_grade = self.calculate_letter_grade()
            self.grade_points = self.get_grade_points()

        from django.utils import timezone
        self.calculated_at = timezone.now()
        self.save()

    def calculate_letter_grade(self):
        """Calculate letter grade based on final percentage"""
        if self.final_percentage is None:
            return ''

        # Get grading scale for the course or use default
        grading_scale = getattr(self.course, 'grading_scale', None)
        if not grading_scale:
            grading_scale = GradingScale.objects.filter(is_default=True).first()

        if grading_scale:
            grade_range = grading_scale.grade_ranges.filter(
                min_percentage__lte=self.final_percentage,
                max_percentage__gte=self.final_percentage
            ).first()

            if grade_range:
                return grade_range.letter_grade

        # Fallback to standard grading
        percentage = float(self.final_percentage)
        if percentage >= 90:
            return 'A+'
        elif percentage >= 85:
            return 'A'
        elif percentage >= 80:
            return 'B+'
        elif percentage >= 75:
            return 'B'
        elif percentage >= 70:
            return 'C+'
        elif percentage >= 65:
            return 'C'
        elif percentage >= 60:
            return 'D'
        else:
            return 'F'

    def get_grade_points(self):
        """Get grade points for GPA calculation"""
        if not self.letter_grade:
            return None

        # Get grading scale
        grading_scale = getattr(self.course, 'grading_scale', None)
        if not grading_scale:
            grading_scale = GradingScale.objects.filter(is_default=True).first()

        if grading_scale:
            grade_range = grading_scale.grade_ranges.filter(letter_grade=self.letter_grade).first()
            if grade_range:
                return grade_range.grade_points

        # Fallback grade points
        grade_points_map = {
            'A+': 4.0, 'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'D-': 0.7,
            'F': 0.0
        }
        return Decimal(str(grade_points_map.get(self.letter_grade, 0.0)))
