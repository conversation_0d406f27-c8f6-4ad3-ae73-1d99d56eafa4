from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Grade, GradeComponent

User = get_user_model()


class GradeComponentSerializer(serializers.ModelSerializer):
    course_name = serializers.CharField(source='course.title', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    grades_count = serializers.SerializerMethodField()
    average_grade = serializers.SerializerMethodField()

    class Meta:
        model = GradeComponent
        fields = [
            'id', 'name', 'name_ar', 'course', 'course_name', 'course_code',
            'component_type', 'max_points', 'weight_percentage', 'is_required',
            'drop_lowest', 'is_active', 'grades_count', 'average_grade',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'grades_count', 'average_grade', 'created_at', 'updated_at']
    
    def get_grades_count(self, obj):
        return obj.grades.count()
    
    def get_average_grade(self, obj):
        from django.db.models import Avg
        grades = obj.grades.filter(status=Grade.GradeStatus.PUBLISHED)
        if grades.exists():
            return round(grades.aggregate(avg=Avg('percentage'))['avg'] or 0, 2)
        return 0


class GradeComponentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = GradeComponent
        fields = [
            'name', 'name_ar', 'course', 'component_type', 'max_points',
            'weight_percentage', 'is_required', 'drop_lowest', 'is_active'
        ]
    
    def validate_max_points(self, value):
        if value <= 0:
            raise serializers.ValidationError("Maximum points must be greater than 0.")
        return value
    
    def validate_weight_percentage(self, value):
        if value < 0 or value > 100:
            raise serializers.ValidationError("Weight percentage must be between 0 and 100.")
        return value


class GradeSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.get_display_name', read_only=True)
    student_id_number = serializers.CharField(source='student.student_id', read_only=True)
    component_name = serializers.CharField(source='grade_component.name', read_only=True)
    course_name = serializers.CharField(source='course.title', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    max_points = serializers.DecimalField(source='points_possible', max_digits=8, decimal_places=2, read_only=True)
    graded_by_name = serializers.CharField(source='graded_by.get_display_name', read_only=True)

    class Meta:
        model = Grade
        fields = [
            'id', 'student', 'student_name', 'student_id_number',
            'grade_component', 'component_name', 'course_name', 'course_code',
            'points_earned', 'max_points', 'percentage', 'letter_grade',
            'status', 'comments', 'graded_at', 'graded_by', 'graded_by_name',
            'is_excused', 'late_penalty', 'is_late', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'percentage', 'letter_grade', 'created_at', 'updated_at'
        ]


class GradeCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Grade
        fields = [
            'student', 'grade_component', 'points_earned', 'points_possible', 'comments',
            'is_excused', 'late_penalty', 'is_late'
        ]

    def validate_points_earned(self, value):
        if value < 0:
            raise serializers.ValidationError("Points cannot be negative.")
        return value

    def validate(self, attrs):
        points_earned = attrs.get('points_earned', 0)
        points_possible = attrs.get('points_possible', 0)

        if points_earned > points_possible * 1.2:  # Allow 20% bonus
            raise serializers.ValidationError(
                f"Points earned cannot exceed {points_possible * 1.2} "
                f"(max points: {points_possible} + 20% bonus)"
            )

        return attrs


class GradeUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Grade
        fields = [
            'points_earned', 'comments', 'status', 'is_excused',
            'late_penalty', 'is_late'
        ]

    def validate_points_earned(self, value):
        if value < 0:
            raise serializers.ValidationError("Points cannot be negative.")
        return value


class GradeStatsSerializer(serializers.Serializer):
    total_grades = serializers.IntegerField()
    graded_assignments = serializers.IntegerField()
    pending_grades = serializers.IntegerField()
    average_grade = serializers.FloatField()
    highest_grade = serializers.FloatField()
    lowest_grade = serializers.FloatField()
    grade_distribution = serializers.DictField()
    course_averages = serializers.ListField()


class TranscriptSerializer(serializers.Serializer):
    student_id = serializers.CharField()
    student_name = serializers.CharField()
    student_name_ar = serializers.CharField(required=False)
    total_credits = serializers.IntegerField()
    completed_credits = serializers.IntegerField()
    gpa = serializers.FloatField()
    cumulative_gpa = serializers.FloatField()
    academic_standing = serializers.CharField()
    courses = serializers.ListField()
    generated_at = serializers.DateTimeField()


class CourseGradeSerializer(serializers.Serializer):
    course_code = serializers.CharField()
    course_name = serializers.CharField()
    course_name_ar = serializers.CharField(required=False)
    credits = serializers.IntegerField()
    semester = serializers.CharField()
    year = serializers.IntegerField()
    final_grade = serializers.CharField()
    grade_points = serializers.FloatField()
    status = serializers.CharField()


class GradebookSerializer(serializers.Serializer):
    course = serializers.DictField()
    students = serializers.ListField()
    components = serializers.ListField()
    statistics = serializers.DictField()


class BulkGradeUpdateSerializer(serializers.Serializer):
    grades = serializers.ListField(
        child=serializers.DictField(),
        min_length=1
    )

    def validate_grades(self, value):
        required_fields = ['grade_id', 'points_earned']
        for grade_data in value:
            for field in required_fields:
                if field not in grade_data:
                    raise serializers.ValidationError(f"Missing required field: {field}")

            if grade_data['points_earned'] < 0:
                raise serializers.ValidationError("Points cannot be negative")

        return value
