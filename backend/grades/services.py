from django.db.models import Avg, Sum, Count, Q, F
from django.utils import timezone
from django.contrib.auth import get_user_model
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timedelta
import json

from .models import Grade, GradeComponent, GradingScale
from courses.models import Course, Enrollment
from assignments.models import Assignment

User = get_user_model()


class GradeCalculationService:
    """
    Advanced grade calculation and management service
    """
    
    def calculate_course_grade(self, student, course):
        """
        Calculate comprehensive course grade for a student
        """
        # Get all grade components for the course
        components = GradeComponent.objects.filter(course=course, is_active=True)

        if not components.exists():
            # Simple average if no categories defined
            return self._calculate_simple_average(student, course)
        
        total_weight = 0
        weighted_score = Decimal('0.00')
        component_details = []

        for component in components:
            component_grade = self._calculate_component_grade(student, course, component)

            if component_grade is not None:
                weight = component.weight_percentage / 100  # Convert percentage to decimal
                weighted_score += component_grade * Decimal(str(weight))
                total_weight += weight

                component_details.append({
                    'component': component.name,
                    'grade': float(component_grade),
                    'weight': float(component.weight_percentage),
                    'weighted_contribution': float(component_grade * Decimal(str(weight)))
                })
        
        # Normalize if total weight is not 100%
        if total_weight > 0 and total_weight != 1:
            final_grade = weighted_score / Decimal(str(total_weight))
        else:
            final_grade = weighted_score
        
        # Get grading scale
        grading_scale = getattr(course, 'grading_scale', None) or GradingScale.get_default_scale()
        letter_grade = grading_scale.get_letter_grade(float(final_grade))
        gpa_points = grading_scale.get_gpa_points(letter_grade)
        
        return {
            'final_percentage': float(final_grade.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
            'letter_grade': letter_grade,
            'gpa_points': gpa_points,
            'total_weight_used': float(total_weight * 100),
            'component_breakdown': component_details,
            'calculation_date': timezone.now().isoformat()
        }
    
    def _calculate_component_grade(self, student, course, component):
        """
        Calculate grade for a specific component
        """
        # Get all grades for this component
        grades = Grade.objects.filter(
            student=student,
            course=course,
            grade_component=component,
            status='published'
        ).exclude(is_excused=True)

        if not grades.exists():
            return None

        # Calculate based on component settings
        grade_values = []
        for grade in grades:
            if grade.percentage is not None:
                grade_values.append(float(grade.percentage))

        if not grade_values:
            return None
        
        # Apply drop policies
        grade_values.sort()

        # Drop lowest scores
        if component.drop_lowest > 0 and len(grade_values) > component.drop_lowest:
            grade_values = grade_values[component.drop_lowest:]
        
        if not grade_values:
            return None
        
        # Calculate average
        average = sum(grade_values) / len(grade_values)
        return Decimal(str(average)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    def _calculate_simple_average(self, student, course):
        """
        Calculate simple average when no components are defined
        """
        grades = Grade.objects.filter(
            student=student,
            course=course
        ).exclude(is_excused=True).exclude(percentage__isnull=True)
        
        if not grades.exists():
            return {
                'final_percentage': 0.0,
                'letter_grade': 'N/A',
                'gpa_points': 0.0,
                'total_weight_used': 0.0,
                'category_breakdown': [],
                'calculation_date': timezone.now().isoformat()
            }
        
        average = grades.aggregate(avg=Avg('percentage'))['avg']
        final_percentage = float(average or 0)
        
        grading_scale = getattr(course, 'grading_scale', None) or GradingScale.get_default_scale()
        letter_grade = grading_scale.get_letter_grade(final_percentage)
        gpa_points = grading_scale.get_gpa_points(letter_grade)
        
        return {
            'final_percentage': final_percentage,
            'letter_grade': letter_grade,
            'gpa_points': gpa_points,
            'total_weight_used': 100.0,
            'component_breakdown': [{
                'component': 'All Assignments',
                'grade': final_percentage,
                'weight': 100.0,
                'weighted_contribution': final_percentage
            }],
            'calculation_date': timezone.now().isoformat()
        }
    
    def calculate_gpa(self, student, semester=None, year=None):
        """
        Calculate GPA for a student
        """
        # Get enrollments
        enrollments = Enrollment.objects.filter(
            student=student,
            status='completed'
        )
        
        if semester:
            enrollments = enrollments.filter(course__semester=semester)
        if year:
            enrollments = enrollments.filter(course__semester__academic_year=year)
        
        total_credit_hours = Decimal('0.00')
        total_grade_points = Decimal('0.00')
        course_grades = []
        
        for enrollment in enrollments:
            course = enrollment.course
            course_grade_data = self.calculate_course_grade(student, course)
            
            credit_hours = Decimal(str(course.credit_hours))
            gpa_points = Decimal(str(course_grade_data['gpa_points']))
            
            total_credit_hours += credit_hours
            total_grade_points += (gpa_points * credit_hours)
            
            course_grades.append({
                'course_code': course.code,
                'course_name': course.name,
                'credit_hours': float(credit_hours),
                'final_percentage': course_grade_data['final_percentage'],
                'letter_grade': course_grade_data['letter_grade'],
                'gpa_points': float(gpa_points),
                'quality_points': float(gpa_points * credit_hours)
            })
        
        if total_credit_hours > 0:
            gpa = total_grade_points / total_credit_hours
        else:
            gpa = Decimal('0.00')
        
        return {
            'gpa': float(gpa.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
            'total_credit_hours': float(total_credit_hours),
            'total_grade_points': float(total_grade_points),
            'course_count': len(course_grades),
            'course_grades': course_grades,
            'calculation_date': timezone.now().isoformat()
        }
    
    def get_grade_trends(self, student, course=None, period_days=30):
        """
        Get grade trends for a student
        """
        end_date = timezone.now()
        start_date = end_date - timedelta(days=period_days)
        
        grades_query = Grade.objects.filter(
            student=student,
            graded_at__gte=start_date,
            graded_at__lte=end_date
        ).exclude(percentage__isnull=True)
        
        if course:
            grades_query = grades_query.filter(course=course)
        
        grades = grades_query.order_by('graded_at')
        
        trend_data = []
        for grade in grades:
            trend_data.append({
                'date': grade.graded_at.date().isoformat(),
                'percentage': float(grade.percentage),
                'letter_grade': grade.letter_grade,
                'assignment': grade.assignment.title if grade.assignment else 'N/A',
                'course': grade.course.code
            })
        
        # Calculate trend direction
        if len(trend_data) >= 2:
            recent_avg = sum(item['percentage'] for item in trend_data[-5:]) / min(5, len(trend_data))
            earlier_avg = sum(item['percentage'] for item in trend_data[:5]) / min(5, len(trend_data))
            
            if recent_avg > earlier_avg + 2:
                trend_direction = 'improving'
            elif recent_avg < earlier_avg - 2:
                trend_direction = 'declining'
            else:
                trend_direction = 'stable'
        else:
            trend_direction = 'insufficient_data'
        
        return {
            'trend_direction': trend_direction,
            'period_days': period_days,
            'grade_count': len(trend_data),
            'average_percentage': sum(item['percentage'] for item in trend_data) / len(trend_data) if trend_data else 0,
            'trend_data': trend_data
        }
    
    def get_class_statistics(self, course, assignment=None):
        """
        Get class statistics for a course or assignment
        """
        if assignment:
            grades = Grade.objects.filter(
                assignment=assignment
            ).exclude(percentage__isnull=True).exclude(is_excused=True)
        else:
            # Get all grades for the course
            grades = Grade.objects.filter(
                course=course
            ).exclude(percentage__isnull=True).exclude(is_excused=True)
        
        if not grades.exists():
            return {
                'count': 0,
                'average': 0,
                'median': 0,
                'min': 0,
                'max': 0,
                'std_deviation': 0,
                'grade_distribution': {},
                'percentile_25': 0,
                'percentile_75': 0
            }
        
        percentages = [float(grade.percentage) for grade in grades]
        percentages.sort()
        
        count = len(percentages)
        average = sum(percentages) / count
        median = percentages[count // 2] if count % 2 == 1 else (percentages[count // 2 - 1] + percentages[count // 2]) / 2
        min_grade = min(percentages)
        max_grade = max(percentages)
        
        # Calculate standard deviation
        variance = sum((x - average) ** 2 for x in percentages) / count
        std_deviation = variance ** 0.5
        
        # Calculate percentiles
        percentile_25 = percentages[int(count * 0.25)]
        percentile_75 = percentages[int(count * 0.75)]
        
        # Grade distribution
        grading_scale = GradingScale.get_default_scale()
        grade_distribution = {}
        
        for percentage in percentages:
            letter_grade = grading_scale.get_letter_grade(percentage)
            grade_distribution[letter_grade] = grade_distribution.get(letter_grade, 0) + 1
        
        return {
            'count': count,
            'average': round(average, 2),
            'median': round(median, 2),
            'min': round(min_grade, 2),
            'max': round(max_grade, 2),
            'std_deviation': round(std_deviation, 2),
            'grade_distribution': grade_distribution,
            'percentile_25': round(percentile_25, 2),
            'percentile_75': round(percentile_75, 2)
        }
    
    def predict_final_grade(self, student, course):
        """
        Predict final grade based on current performance
        """
        current_grade_data = self.calculate_course_grade(student, course)
        
        # Get all components and their completion status
        components = GradeComponent.objects.filter(course=course, is_active=True)
        
        if not components.exists():
            return {
                'predicted_percentage': current_grade_data['final_percentage'],
                'confidence': 'low',
                'recommendation': 'Insufficient data for prediction'
            }
        
        total_weight_remaining = 100 - current_grade_data['total_weight_used']
        
        if total_weight_remaining <= 0:
            return {
                'predicted_percentage': current_grade_data['final_percentage'],
                'confidence': 'high',
                'recommendation': 'All assignments completed'
            }
        
        # Simple prediction: assume student maintains current average
        current_avg = current_grade_data['final_percentage']
        predicted_percentage = current_avg  # Simplified prediction
        
        confidence = 'medium' if current_grade_data['total_weight_used'] > 50 else 'low'
        
        # Generate recommendation
        if predicted_percentage >= 90:
            recommendation = 'Excellent performance! Keep up the good work.'
        elif predicted_percentage >= 80:
            recommendation = 'Good performance. Continue current study habits.'
        elif predicted_percentage >= 70:
            recommendation = 'Satisfactory performance. Consider additional study time.'
        elif predicted_percentage >= 60:
            recommendation = 'Below average performance. Seek help and increase study efforts.'
        else:
            recommendation = 'Poor performance. Immediate intervention needed.'
        
        return {
            'predicted_percentage': round(predicted_percentage, 2),
            'confidence': confidence,
            'recommendation': recommendation,
            'weight_remaining': round(total_weight_remaining, 2)
        }
