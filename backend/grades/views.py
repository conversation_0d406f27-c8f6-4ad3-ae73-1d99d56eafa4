from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q, Count, Avg, Sum, Max, Min
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.http import HttpResponse
from .models import Grade, GradeComponent
from courses.models import Course, Enrollment
from .serializers import (
    GradeSerializer, GradeCreateSerializer, GradeUpdateSerializer,
    GradeComponentSerializer, GradeComponentCreateSerializer,
    GradeStatsSerializer, TranscriptSerializer, GradebookSerializer,
    BulkGradeUpdateSerializer
)

User = get_user_model()


class GradeComponentListCreateView(generics.ListCreateAPIView):
    """
    List all grade components or create a new grade component
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return GradeComponentCreateSerializer
        return GradeComponentSerializer

    def get_queryset(self):
        queryset = GradeComponent.objects.all()
        user = self.request.user

        # Filter based on user role
        if user.role == 'teacher':
            teacher_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            queryset = queryset.filter(course_id__in=teacher_courses)
        elif user.role == 'student':
            # Students see components for their enrolled courses
            enrolled_courses = Enrollment.objects.filter(
                student=user,
                status='enrolled',
                is_active=True
            ).values_list('course', flat=True)
            queryset = queryset.filter(course_id__in=enrolled_courses)

        # Apply filters
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)

        return queryset.select_related('course').order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save()


class GradeComponentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a grade component
    """
    queryset = GradeComponent.objects.all()
    serializer_class = GradeComponentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        queryset = GradeComponent.objects.all()

        if user.role == 'teacher':
            # Teachers can only modify components for their courses
            if self.request.method in ['PUT', 'PATCH', 'DELETE']:
                teacher_courses = Course.objects.filter(
                    Q(instructor=user) | Q(assistant_instructors=user)
                ).values_list('id', flat=True)
                queryset = queryset.filter(course_id__in=teacher_courses)

        return queryset


class GradeListCreateView(generics.ListCreateAPIView):
    """
    List all grades or create a new grade
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return GradeCreateSerializer
        return GradeSerializer

    def get_queryset(self):
        queryset = Grade.objects.all()
        user = self.request.user

        if user.role == 'student':
            queryset = queryset.filter(student=user)
        elif user.role == 'teacher':
            # Get grades for courses taught by this teacher
            teacher_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            queryset = queryset.filter(grade_component__course_id__in=teacher_courses)

        # Apply filters
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(grade_component__course_id=course_id)

        student_id = self.request.query_params.get('student_id')
        if student_id:
            queryset = queryset.filter(student_id=student_id)

        component_id = self.request.query_params.get('component_id')
        if component_id:
            queryset = queryset.filter(grade_component_id=component_id)

        return queryset.select_related('student', 'grade_component', 'grade_component__course').order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(graded_by=self.request.user)


class GradeDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a grade
    """
    queryset = Grade.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return GradeUpdateSerializer
        return GradeSerializer

    def get_queryset(self):
        user = self.request.user
        queryset = Grade.objects.all()

        if user.role == 'student':
            queryset = queryset.filter(student=user)
        elif user.role == 'teacher':
            # Teachers can only modify grades for their courses
            if self.request.method in ['PUT', 'PATCH', 'DELETE']:
                teacher_courses = Course.objects.filter(
                    Q(instructor=user) | Q(assistant_instructors=user)
                ).values_list('id', flat=True)
                queryset = queryset.filter(grade_component__course_id__in=teacher_courses)

        return queryset


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def student_grades(request, student_id):
    """
    Get all grades for a specific student
    """
    user = request.user

    # Check permissions
    if user.role == 'student' and user.student_id != student_id:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )
    elif user.role not in ['student', 'teacher', 'admin', 'super_admin']:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        student = User.objects.get(student_id=student_id, role='student')
        grades = Grade.objects.filter(student=student)

        # If teacher, only show grades for their courses
        if user.role == 'teacher':
            teacher_courses = Course.objects.filter(instructor=user).values_list('id', flat=True)
            grades = grades.filter(grade_component__course_id__in=teacher_courses)

        # Group grades by course
        courses_data = {}
        for grade in grades:
            course_id = grade.grade_component.course.id
            if course_id not in courses_data:
                courses_data[course_id] = {
                    'course': {
                        'id': grade.grade_component.course.id,
                        'title': grade.grade_component.course.title,
                        'code': grade.grade_component.course.code,
                    },
                    'grades': []
                }

            courses_data[course_id]['grades'].append({
                'id': grade.id,
                'component': grade.grade_component.name,
                'points': grade.points_earned,
                'max_points': grade.grade_component.max_points,
                'percentage': grade.percentage,
                'letter_grade': grade.letter_grade,
                'status': grade.status,
                'graded_at': grade.graded_at,
            })

        return Response(list(courses_data.values()))

    except User.DoesNotExist:
        return Response(
            {'error': 'Student not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def course_grades(request, course_id):
    """
    Get all grades for a specific course
    """
    user = request.user

    if user.role not in ['teacher', 'admin', 'super_admin']:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        course = Course.objects.get(id=course_id)

        # Check if teacher owns the course
        if user.role == 'teacher' and course.instructor != user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        grades = Grade.objects.filter(grade_component__course=course)

        # Group by student
        students_data = {}
        for grade in grades:
            student_id = grade.student.id
            if student_id not in students_data:
                students_data[student_id] = {
                    'student': {
                        'id': grade.student.id,
                        'name': grade.student.get_display_name(),
                        'student_id': grade.student.student_id,
                    },
                    'grades': []
                }

            students_data[student_id]['grades'].append({
                'id': grade.id,
                'component': grade.grade_component.name,
                'points': grade.points_earned,
                'max_points': grade.grade_component.max_points,
                'percentage': grade.percentage,
                'letter_grade': grade.letter_grade,
                'status': grade.status,
            })

        return Response(list(students_data.values()))

    except Course.DoesNotExist:
        return Response(
            {'error': 'Course not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def generate_transcript(request, student_id):
    """
    Generate transcript for a student
    """
    try:
        # Check permissions
        if request.user.role == 'student' and request.user.student_id != student_id:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        elif request.user.role not in ['student', 'teacher', 'admin', 'super_admin']:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get student
        try:
            student = User.objects.get(student_id=student_id, role='student')
        except User.DoesNotExist:
            return Response(
                {'error': 'Student not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all enrollments for the student
        enrollments = Enrollment.objects.filter(
            student=student,
            status__in=['enrolled', 'completed', 'withdrawn']
        ).select_related('course')

        # Calculate transcript data
        courses_data = []
        total_credits = 0
        total_grade_points = 0
        completed_credits = 0

        for enrollment in enrollments:
            course = enrollment.course

            # Get final grades for this course
            course_grades = Grade.objects.filter(
                student=student,
                grade_component__course=course,
                status='final'
            )

            if course_grades.exists():
                # Calculate course final grade
                total_points = 0
                total_weight = 0

                for grade in course_grades:
                    component = grade.grade_component
                    total_points += (grade.percentage * component.weight_percentage / 100)
                    total_weight += component.weight_percentage

                if total_weight > 0:
                    final_percentage = total_points

                    # Convert to letter grade
                    if final_percentage >= 90:
                        letter_grade = 'A'
                        grade_points = 4.0
                    elif final_percentage >= 80:
                        letter_grade = 'B'
                        grade_points = 3.0
                    elif final_percentage >= 70:
                        letter_grade = 'C'
                        grade_points = 2.0
                    elif final_percentage >= 60:
                        letter_grade = 'D'
                        grade_points = 1.0
                    else:
                        letter_grade = 'F'
                        grade_points = 0.0

                    courses_data.append({
                        'course_code': course.code,
                        'course_name': course.title,
                        'course_name_ar': course.title_ar or '',
                        'credits': course.credit_hours,
                        'semester': course.semester,
                        'year': course.year,
                        'final_grade': letter_grade,
                        'grade_points': grade_points * course.credit_hours,
                        'status': enrollment.status
                    })

                    total_credits += course.credit_hours
                    if enrollment.status == 'completed':
                        completed_credits += course.credit_hours
                        total_grade_points += (grade_points * course.credit_hours)

        # Calculate GPA
        gpa = total_grade_points / total_credits if total_credits > 0 else 0.0
        cumulative_gpa = gpa  # For now, same as GPA

        # Determine academic standing
        if gpa >= 3.5:
            academic_standing = 'Dean\'s List'
        elif gpa >= 3.0:
            academic_standing = 'Good Standing'
        elif gpa >= 2.0:
            academic_standing = 'Satisfactory'
        else:
            academic_standing = 'Academic Probation'

        transcript_data = {
            'student_id': student.student_id,
            'student_name': student.get_display_name(),
            'student_name_ar': f"{student.first_name_ar} {student.last_name_ar}" if student.first_name_ar else '',
            'total_credits': total_credits,
            'completed_credits': completed_credits,
            'gpa': round(gpa, 2),
            'cumulative_gpa': round(cumulative_gpa, 2),
            'academic_standing': academic_standing,
            'courses': courses_data,
            'generated_at': timezone.now()
        }

        serializer = TranscriptSerializer(transcript_data)
        return Response(serializer.data)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def gradebook_view(request, course_id):
    """
    Get gradebook view for a course
    """
    user = request.user

    if user.role not in ['teacher', 'admin', 'super_admin']:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        course = Course.objects.get(id=course_id)

        # Check if teacher owns the course
        if user.role == 'teacher' and course.instructor != user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Fetch real gradebook data from database
        enrollments = Enrollment.objects.filter(course=course).select_related('student')
        assignments = Assignment.objects.filter(course=course)

        students_data = []
        for enrollment in enrollments:
            student = enrollment.student
            student_grades = Grade.objects.filter(
                student=student,
                assignment__course=course
            ).select_related('assignment')

            grades_dict = {}
            for grade in student_grades:
                grades_dict[str(grade.assignment.id)] = {
                    'points': float(grade.points_earned) if grade.points_earned else 0,
                    'percentage': float(grade.percentage) if grade.percentage else 0,
                    'letter_grade': grade.letter_grade or 'N/A'
                }

            students_data.append({
                'id': str(student.id),
                'name': f"{student.first_name} {student.last_name}",
                'student_id': student.student_id or '',
                'grades': grades_dict
            })

        components_data = []
        for assignment in assignments:
            components_data.append({
                'id': str(assignment.id),
                'name': assignment.title,
                'max_points': float(assignment.max_points) if assignment.max_points else 100,
                'weight_percentage': float(assignment.weight_percentage) if assignment.weight_percentage else 0
            })

        gradebook_data = {
            'course': {
                'id': course.id,
                'title': course.title,
                'code': course.code,
            },
            'students': students_data,
            'components': components_data
        }

        return Response(gradebook_data)

    except Course.DoesNotExist:
        return Response(
            {'error': 'Course not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def grade_stats(request):
    """
    Get grade statistics
    """
    try:
        user = request.user

        # Base querysets
        grades_queryset = Grade.objects.all()
        components_queryset = GradeComponent.objects.all()

        # Filter by user role
        if user.role == 'teacher':
            teacher_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            grades_queryset = grades_queryset.filter(grade_component__course_id__in=teacher_courses)
            components_queryset = components_queryset.filter(course_id__in=teacher_courses)
        elif user.role == 'student':
            grades_queryset = grades_queryset.filter(student=user)
            enrolled_courses = Enrollment.objects.filter(
                student=user,
                status='enrolled',
                is_active=True
            ).values_list('course', flat=True)
            components_queryset = components_queryset.filter(course_id__in=enrolled_courses)
        elif user.role not in ['admin', 'super_admin']:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate statistics
        total_grades = grades_queryset.count()
        graded_assignments = grades_queryset.filter(status='final').count()
        pending_grades = grades_queryset.filter(status='draft').count()

        final_grades = grades_queryset.filter(status='final')
        if final_grades.exists():
            average_grade = final_grades.aggregate(avg=Avg('percentage'))['avg'] or 0
            highest_grade = final_grades.aggregate(max=Max('percentage'))['max'] or 0
            lowest_grade = final_grades.aggregate(min=Min('percentage'))['min'] or 0
        else:
            average_grade = highest_grade = lowest_grade = 0

        # Grade distribution
        grade_distribution = {
            'A': final_grades.filter(percentage__gte=90).count(),
            'B': final_grades.filter(percentage__gte=80, percentage__lt=90).count(),
            'C': final_grades.filter(percentage__gte=70, percentage__lt=80).count(),
            'D': final_grades.filter(percentage__gte=60, percentage__lt=70).count(),
            'F': final_grades.filter(percentage__lt=60).count(),
        }

        # Course averages (for teachers and admins)
        course_averages = []
        if user.role in ['teacher', 'admin', 'super_admin']:
            courses = Course.objects.filter(id__in=components_queryset.values_list('course_id', flat=True))
            for course in courses:
                course_grades = grades_queryset.filter(grade_component__course=course, status='final')
                if course_grades.exists():
                    avg = course_grades.aggregate(avg=Avg('percentage'))['avg'] or 0
                    course_averages.append({
                        'course_code': course.code,
                        'course_name': course.title,
                        'average': round(avg, 2),
                        'total_grades': course_grades.count()
                    })

        stats = {
            'total_grades': total_grades,
            'graded_assignments': graded_assignments,
            'pending_grades': pending_grades,
            'average_grade': round(average_grade, 2),
            'highest_grade': round(highest_grade, 2),
            'lowest_grade': round(lowest_grade, 2),
            'grade_distribution': grade_distribution,
            'course_averages': course_averages
        }

        serializer = GradeStatsSerializer(stats)
        return Response(serializer.data)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def bulk_update_grades(request):
    """
    Update multiple grades at once
    """
    if request.user.role not in ['teacher', 'admin', 'super_admin']:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        serializer = BulkGradeUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        grades_data = serializer.validated_data['grades']
        updated_grades = []
        errors = []

        for grade_data in grades_data:
            try:
                grade = Grade.objects.get(id=grade_data['grade_id'])

                # Check permissions
                if request.user.role == 'teacher':
                    if (grade.grade_component.course.instructor != request.user and
                        request.user not in grade.grade_component.course.assistant_instructors.all()):
                        errors.append(f"Permission denied for grade {grade.id}")
                        continue

                # Update grade
                grade.points_earned = grade_data['points']
                if 'feedback' in grade_data:
                    grade.feedback = grade_data['feedback']
                if 'status' in grade_data:
                    grade.status = grade_data['status']

                grade.graded_by = request.user
                grade.graded_at = timezone.now()
                grade.save()

                updated_grades.append(GradeSerializer(grade).data)

            except Grade.DoesNotExist:
                errors.append(f"Grade {grade_data['grade_id']} not found")
            except Exception as e:
                errors.append(f"Error updating grade {grade_data['grade_id']}: {str(e)}")

        return Response({
            'updated_grades': updated_grades,
            'errors': errors,
            'total_updated': len(updated_grades),
            'total_errors': len(errors)
        })

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def export_grades(request):
    """
    Export grades data
    """
    try:
        user = request.user

        # Get query parameters
        course_id = request.query_params.get('course_id')
        student_id = request.query_params.get('student_id')
        format_type = request.query_params.get('format', 'csv')

        # Base queryset
        grades = Grade.objects.all()

        # Apply filters
        if course_id:
            grades = grades.filter(grade_component__course_id=course_id)
        if student_id:
            grades = grades.filter(student__student_id=student_id)

        # Check permissions and filter by user role
        if user.role == 'teacher':
            teacher_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            grades = grades.filter(grade_component__course_id__in=teacher_courses)
        elif user.role == 'student':
            grades = grades.filter(student=user)
        elif user.role not in ['admin', 'super_admin']:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Prepare export data
        export_data = []
        for grade in grades.select_related('student', 'grade_component', 'grade_component__course', 'graded_by'):
            export_data.append({
                'student_id': grade.student.student_id,
                'student_name': grade.student.get_display_name(),
                'course_code': grade.grade_component.course.code,
                'course_name': grade.grade_component.course.title,
                'component_name': grade.grade_component.name,
                'component_type': grade.grade_component.component_type,
                'max_points': float(grade.grade_component.max_points),
                'points': float(grade.points_earned or 0),
                'percentage': float(grade.percentage),
                'letter_grade': grade.letter_grade,
                'status': grade.status,
                'feedback': grade.feedback,
                'graded_at': grade.graded_at.isoformat() if grade.graded_at else None,
                'graded_by': grade.graded_by.get_display_name() if grade.graded_by else None
            })

        return Response({
            'data': export_data,
            'total_records': len(export_data),
            'format': format_type,
            'exported_at': timezone.now().isoformat()
        })

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
