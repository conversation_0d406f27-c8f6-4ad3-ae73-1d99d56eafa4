from django.contrib import admin
from .models import (
    Video, VideoVariant, VideoProgress, VideoAnalytics, VideoSubtitle
)


class VideoVariantInline(admin.TabularInline):
    model = VideoVariant
    extra = 0
    readonly_fields = ['file_size_bytes', 'is_ready', 'processing_completed_at']


class VideoSubtitleInline(admin.TabularInline):
    model = VideoSubtitle
    extra = 0


@admin.register(Video)
class VideoAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'course', 'status', 'duration_formatted', 'file_size_formatted',
        'view_count', 'unique_viewers', 'uploaded_by', 'created_at'
    ]
    list_filter = ['status', 'is_public', 'require_authentication', 'course', 'created_at']
    search_fields = ['title', 'title_ar', 'description', 'uploaded_by__first_name', 'uploaded_by__last_name']
    readonly_fields = [
        'duration_seconds', 'file_size_bytes', 'original_resolution',
        'frame_rate', 'bitrate', 'codec', 'status', 'processing_progress',
        'view_count', 'unique_viewers', 'created_at', 'updated_at', 'processed_at'
    ]
    inlines = [VideoVariantInline, VideoSubtitleInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'description', 'description_ar', 'thumbnail')
        }),
        ('File Information', {
            'fields': ('original_file', 'duration_seconds', 'file_size_bytes', 'original_resolution',
                      'frame_rate', 'bitrate', 'codec')
        }),
        ('Processing', {
            'fields': ('status', 'processing_progress', 'error_message', 'processed_at')
        }),
        ('Associations', {
            'fields': ('course', 'module', 'lesson', 'uploaded_by')
        }),
        ('Settings', {
            'fields': ('is_public', 'allow_download', 'require_authentication')
        }),
        ('Analytics', {
            'fields': ('view_count', 'unique_viewers')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'course', 'module', 'lesson', 'uploaded_by'
        )


@admin.register(VideoVariant)
class VideoVariantAdmin(admin.ModelAdmin):
    list_display = [
        'video', 'quality', 'resolution', 'bitrate', 'file_size_formatted',
        'is_ready', 'processing_completed_at'
    ]
    list_filter = ['quality', 'is_ready', 'codec']
    search_fields = ['video__title']
    readonly_fields = ['file_size_bytes', 'processing_started_at', 'processing_completed_at']
    
    def file_size_formatted(self, obj):
        size = obj.file_size_bytes
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    file_size_formatted.short_description = 'File Size'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('video')


@admin.register(VideoProgress)
class VideoProgressAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'video', 'completion_percentage', 'is_completed',
        'session_count', 'last_watched_at'
    ]
    list_filter = ['is_completed', 'last_quality_watched', 'last_watched_at']
    search_fields = [
        'user__first_name', 'user__last_name', 'user__email',
        'video__title'
    ]
    readonly_fields = ['first_watched_at', 'completed_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('video', 'user')


@admin.register(VideoAnalytics)
class VideoAnalyticsAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'video', 'duration_watched_formatted', 'quality_used',
        'buffer_events', 'seek_events', 'session_started_at'
    ]
    list_filter = ['quality_used', 'session_started_at']
    search_fields = [
        'user__first_name', 'user__last_name', 'user__email',
        'video__title', 'session_id'
    ]
    readonly_fields = ['session_started_at']
    
    def duration_watched_formatted(self, obj):
        seconds = obj.duration_watched
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    duration_watched_formatted.short_description = 'Duration Watched'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('video', 'user')


@admin.register(VideoSubtitle)
class VideoSubtitleAdmin(admin.ModelAdmin):
    list_display = [
        'video', 'language_name', 'language_code', 'format',
        'is_default', 'is_auto_generated', 'created_at'
    ]
    list_filter = ['language_code', 'format', 'is_default', 'is_auto_generated']
    search_fields = ['video__title', 'language_name', 'language_code']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('video')
