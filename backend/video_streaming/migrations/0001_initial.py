# Generated by Django 4.2.7 on 2025-07-13 14:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid
import video_streaming.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('content_management', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0004_add_retake_tracking'),
    ]

    operations = [
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='title')),
                ('title_ar', models.CharField(blank=True, max_length=200, verbose_name='title (Arabic)')),
                ('description', models.TextField(blank=True, verbose_name='description')),
                ('description_ar', models.TextField(blank=True, verbose_name='description (Arabic)')),
                ('original_file', models.FileField(upload_to=video_streaming.models.video_upload_path, verbose_name='original file')),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to=video_streaming.models.thumbnail_upload_path, verbose_name='thumbnail')),
                ('duration_seconds', models.PositiveIntegerField(default=0, verbose_name='duration (seconds)')),
                ('file_size_bytes', models.BigIntegerField(default=0, verbose_name='file size (bytes)')),
                ('original_resolution', models.CharField(blank=True, max_length=20, verbose_name='original resolution')),
                ('frame_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='frame rate')),
                ('bitrate', models.PositiveIntegerField(blank=True, null=True, verbose_name='bitrate (kbps)')),
                ('codec', models.CharField(blank=True, max_length=50, verbose_name='codec')),
                ('status', models.CharField(choices=[('uploading', 'Uploading'), ('processing', 'Processing'), ('ready', 'Ready'), ('error', 'Error'), ('archived', 'Archived')], default='uploading', max_length=20, verbose_name='status')),
                ('processing_progress', models.PositiveIntegerField(default=0, verbose_name='processing progress (%)')),
                ('error_message', models.TextField(blank=True, verbose_name='error message')),
                ('is_public', models.BooleanField(default=False, verbose_name='public')),
                ('allow_download', models.BooleanField(default=False, verbose_name='allow download')),
                ('require_authentication', models.BooleanField(default=True, verbose_name='require authentication')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='view count')),
                ('unique_viewers', models.PositiveIntegerField(default=0, verbose_name='unique viewers')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='processed at')),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='videos', to='courses.course')),
                ('lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='videos', to='content_management.lesson')),
                ('module', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='videos', to='content_management.coursemodule')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_videos', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Video',
                'verbose_name_plural': 'Videos',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VideoVariant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quality', models.CharField(choices=[('240p', '240p'), ('360p', '360p'), ('480p', '480p'), ('720p', '720p (HD)'), ('1080p', '1080p (Full HD)'), ('1440p', '1440p (2K)'), ('2160p', '2160p (4K)')], max_length=10, verbose_name='quality')),
                ('file_path', models.CharField(max_length=500, verbose_name='file path')),
                ('file_size_bytes', models.BigIntegerField(default=0, verbose_name='file size (bytes)')),
                ('resolution_width', models.PositiveIntegerField(verbose_name='width')),
                ('resolution_height', models.PositiveIntegerField(verbose_name='height')),
                ('bitrate', models.PositiveIntegerField(verbose_name='bitrate (kbps)')),
                ('codec', models.CharField(default='h264', max_length=50, verbose_name='codec')),
                ('is_ready', models.BooleanField(default=False, verbose_name='ready')),
                ('processing_started_at', models.DateTimeField(blank=True, null=True, verbose_name='processing started')),
                ('processing_completed_at', models.DateTimeField(blank=True, null=True, verbose_name='processing completed')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='video_streaming.video')),
            ],
            options={
                'verbose_name': 'Video Variant',
                'verbose_name_plural': 'Video Variants',
                'ordering': ['quality'],
                'unique_together': {('video', 'quality')},
            },
        ),
        migrations.CreateModel(
            name='VideoSubtitle',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('language_code', models.CharField(max_length=10, verbose_name='language code')),
                ('language_name', models.CharField(max_length=50, verbose_name='language name')),
                ('subtitle_file', models.FileField(upload_to='videos/subtitles/', verbose_name='subtitle file')),
                ('format', models.CharField(default='vtt', max_length=10, verbose_name='format')),
                ('is_default', models.BooleanField(default=False, verbose_name='default')),
                ('is_auto_generated', models.BooleanField(default=False, verbose_name='auto-generated')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subtitles', to='video_streaming.video')),
            ],
            options={
                'verbose_name': 'Video Subtitle',
                'verbose_name_plural': 'Video Subtitles',
                'ordering': ['language_code'],
                'unique_together': {('video', 'language_code')},
            },
        ),
        migrations.CreateModel(
            name='VideoProgress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('current_time_seconds', models.PositiveIntegerField(default=0, verbose_name='current time (seconds)')),
                ('total_watched_seconds', models.PositiveIntegerField(default=0, verbose_name='total watched (seconds)')),
                ('completion_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, verbose_name='completion percentage')),
                ('is_completed', models.BooleanField(default=False, verbose_name='completed')),
                ('session_count', models.PositiveIntegerField(default=0, verbose_name='session count')),
                ('last_quality_watched', models.CharField(blank=True, choices=[('240p', '240p'), ('360p', '360p'), ('480p', '480p'), ('720p', '720p (HD)'), ('1080p', '1080p (Full HD)'), ('1440p', '1440p (2K)'), ('2160p', '2160p (4K)')], max_length=10, verbose_name='last quality')),
                ('first_watched_at', models.DateTimeField(auto_now_add=True, verbose_name='first watched')),
                ('last_watched_at', models.DateTimeField(auto_now=True, verbose_name='last watched')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='completed at')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='video_progress', to=settings.AUTH_USER_MODEL)),
                ('video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_records', to='video_streaming.video')),
            ],
            options={
                'verbose_name': 'Video Progress',
                'verbose_name_plural': 'Video Progress Records',
                'ordering': ['-last_watched_at'],
                'indexes': [models.Index(fields=['user', 'is_completed'], name='video_strea_user_id_01a62e_idx'), models.Index(fields=['video', 'completion_percentage'], name='video_strea_video_i_689911_idx')],
                'unique_together': {('video', 'user')},
            },
        ),
        migrations.CreateModel(
            name='VideoAnalytics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_id', models.CharField(max_length=100, verbose_name='session ID')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, verbose_name='user agent')),
                ('start_time', models.PositiveIntegerField(default=0, verbose_name='start time (seconds)')),
                ('end_time', models.PositiveIntegerField(default=0, verbose_name='end time (seconds)')),
                ('duration_watched', models.PositiveIntegerField(default=0, verbose_name='duration watched (seconds)')),
                ('quality_used', models.CharField(blank=True, choices=[('240p', '240p'), ('360p', '360p'), ('480p', '480p'), ('720p', '720p (HD)'), ('1080p', '1080p (Full HD)'), ('1440p', '1440p (2K)'), ('2160p', '2160p (4K)')], max_length=10, verbose_name='quality used')),
                ('buffer_events', models.PositiveIntegerField(default=0, verbose_name='buffer events')),
                ('seek_events', models.PositiveIntegerField(default=0, verbose_name='seek events')),
                ('pause_events', models.PositiveIntegerField(default=0, verbose_name='pause events')),
                ('session_started_at', models.DateTimeField(auto_now_add=True, verbose_name='session started')),
                ('session_ended_at', models.DateTimeField(blank=True, null=True, verbose_name='session ended')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='video_analytics', to=settings.AUTH_USER_MODEL)),
                ('video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='video_streaming.video')),
            ],
            options={
                'verbose_name': 'Video Analytics',
                'verbose_name_plural': 'Video Analytics',
                'ordering': ['-session_started_at'],
                'indexes': [models.Index(fields=['video', 'session_started_at'], name='video_strea_video_i_534c55_idx'), models.Index(fields=['user', 'session_started_at'], name='video_strea_user_id_ad35cc_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='video',
            index=models.Index(fields=['status', 'course'], name='video_strea_status_eff822_idx'),
        ),
        migrations.AddIndex(
            model_name='video',
            index=models.Index(fields=['uploaded_by', 'created_at'], name='video_strea_uploade_d0d453_idx'),
        ),
    ]
