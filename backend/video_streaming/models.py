from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from courses.models import Course
from content_management.models import CourseModule, Lesson
import uuid
import os

User = get_user_model()


class VideoQuality(models.TextChoices):
    """Video quality options for adaptive streaming"""
    QUALITY_240P = '240p', _('240p')
    QUALITY_360P = '360p', _('360p')
    QUALITY_480P = '480p', _('480p')
    QUALITY_720P = '720p', _('720p (HD)')
    QUALITY_1080P = '1080p', _('1080p (Full HD)')
    QUALITY_1440P = '1440p', _('1440p (2K)')
    QUALITY_2160P = '2160p', _('2160p (4K)')


class VideoStatus(models.TextChoices):
    """Video processing status"""
    UPLOADING = 'uploading', _('Uploading')
    PROCESSING = 'processing', _('Processing')
    READY = 'ready', _('Ready')
    ERROR = 'error', _('Error')
    ARCHIVED = 'archived', _('Archived')


def video_upload_path(instance, filename):
    """Generate upload path for video files"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4()}.{ext}"
    return f"videos/original/{filename}"


def thumbnail_upload_path(instance, filename):
    """Generate upload path for video thumbnails"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4()}.{ext}"
    return f"videos/thumbnails/{filename}"


class Video(models.Model):
    """
    Video model for storing video content and metadata
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Basic Information
    title = models.CharField(_('title'), max_length=200)
    title_ar = models.CharField(_('title (Arabic)'), max_length=200, blank=True)
    description = models.TextField(_('description'), blank=True)
    description_ar = models.TextField(_('description (Arabic)'), blank=True)
    
    # File Information
    original_file = models.FileField(_('original file'), upload_to=video_upload_path)
    thumbnail = models.ImageField(_('thumbnail'), upload_to=thumbnail_upload_path, blank=True, null=True)
    
    # Video Metadata
    duration_seconds = models.PositiveIntegerField(_('duration (seconds)'), default=0)
    file_size_bytes = models.BigIntegerField(_('file size (bytes)'), default=0)
    original_resolution = models.CharField(_('original resolution'), max_length=20, blank=True)
    frame_rate = models.DecimalField(_('frame rate'), max_digits=5, decimal_places=2, null=True, blank=True)
    bitrate = models.PositiveIntegerField(_('bitrate (kbps)'), null=True, blank=True)
    codec = models.CharField(_('codec'), max_length=50, blank=True)
    
    # Processing Status
    status = models.CharField(_('status'), max_length=20, choices=VideoStatus.choices, default=VideoStatus.UPLOADING)
    processing_progress = models.PositiveIntegerField(_('processing progress (%)'), default=0)
    error_message = models.TextField(_('error message'), blank=True)
    
    # Associations
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='videos', null=True, blank=True)
    module = models.ForeignKey(CourseModule, on_delete=models.CASCADE, related_name='videos', null=True, blank=True)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name='videos', null=True, blank=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='uploaded_videos')
    
    # Settings
    is_public = models.BooleanField(_('public'), default=False)
    allow_download = models.BooleanField(_('allow download'), default=False)
    require_authentication = models.BooleanField(_('require authentication'), default=True)
    
    # Analytics
    view_count = models.PositiveIntegerField(_('view count'), default=0)
    unique_viewers = models.PositiveIntegerField(_('unique viewers'), default=0)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    processed_at = models.DateTimeField(_('processed at'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('Video')
        verbose_name_plural = _('Videos')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'course']),
            models.Index(fields=['uploaded_by', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} ({self.get_status_display()})"
    
    @property
    def duration_formatted(self):
        """Return formatted duration (HH:MM:SS)"""
        hours = self.duration_seconds // 3600
        minutes = (self.duration_seconds % 3600) // 60
        seconds = self.duration_seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    @property
    def file_size_formatted(self):
        """Return formatted file size"""
        size = self.file_size_bytes
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def clean(self):
        """Validate video associations"""
        if self.lesson and not self.module:
            self.module = self.lesson.module
        if self.module and not self.course:
            self.course = self.module.course


class VideoVariant(models.Model):
    """
    Different quality variants of a video for adaptive streaming
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    video = models.ForeignKey(Video, on_delete=models.CASCADE, related_name='variants')
    quality = models.CharField(_('quality'), max_length=10, choices=VideoQuality.choices)
    
    # File Information
    file_path = models.CharField(_('file path'), max_length=500)
    file_size_bytes = models.BigIntegerField(_('file size (bytes)'), default=0)
    
    # Video Properties
    resolution_width = models.PositiveIntegerField(_('width'))
    resolution_height = models.PositiveIntegerField(_('height'))
    bitrate = models.PositiveIntegerField(_('bitrate (kbps)'))
    codec = models.CharField(_('codec'), max_length=50, default='h264')
    
    # Processing
    is_ready = models.BooleanField(_('ready'), default=False)
    processing_started_at = models.DateTimeField(_('processing started'), null=True, blank=True)
    processing_completed_at = models.DateTimeField(_('processing completed'), null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Video Variant')
        verbose_name_plural = _('Video Variants')
        unique_together = ['video', 'quality']
        ordering = ['quality']
    
    def __str__(self):
        return f"{self.video.title} - {self.quality}"
    
    @property
    def resolution(self):
        """Return resolution as string"""
        return f"{self.resolution_width}x{self.resolution_height}"


class VideoProgress(models.Model):
    """
    Track user progress watching videos
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    video = models.ForeignKey(Video, on_delete=models.CASCADE, related_name='progress_records')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='video_progress')
    
    # Progress Tracking
    current_time_seconds = models.PositiveIntegerField(_('current time (seconds)'), default=0)
    total_watched_seconds = models.PositiveIntegerField(_('total watched (seconds)'), default=0)
    completion_percentage = models.DecimalField(_('completion percentage'), max_digits=5, decimal_places=2, default=0.0)
    is_completed = models.BooleanField(_('completed'), default=False)
    
    # Session Information
    session_count = models.PositiveIntegerField(_('session count'), default=0)
    last_quality_watched = models.CharField(_('last quality'), max_length=10, choices=VideoQuality.choices, blank=True)
    
    # Timestamps
    first_watched_at = models.DateTimeField(_('first watched'), auto_now_add=True)
    last_watched_at = models.DateTimeField(_('last watched'), auto_now=True)
    completed_at = models.DateTimeField(_('completed at'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('Video Progress')
        verbose_name_plural = _('Video Progress Records')
        unique_together = ['video', 'user']
        ordering = ['-last_watched_at']
        indexes = [
            models.Index(fields=['user', 'is_completed']),
            models.Index(fields=['video', 'completion_percentage']),
        ]
    
    def __str__(self):
        return f"{self.user.get_display_name()} - {self.video.title} ({self.completion_percentage}%)"
    
    def update_progress(self, current_time, total_duration):
        """Update progress based on current time and total duration"""
        self.current_time_seconds = current_time
        
        # Calculate completion percentage
        if total_duration > 0:
            self.completion_percentage = min((current_time / total_duration) * 100, 100)
        
        # Mark as completed if watched 90% or more
        if self.completion_percentage >= 90 and not self.is_completed:
            self.is_completed = True
            self.completed_at = models.timezone.now()
        
        self.save()


class VideoAnalytics(models.Model):
    """
    Analytics data for video viewing
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    video = models.ForeignKey(Video, on_delete=models.CASCADE, related_name='analytics')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='video_analytics', null=True, blank=True)
    
    # Session Information
    session_id = models.CharField(_('session ID'), max_length=100)
    ip_address = models.GenericIPAddressField(_('IP address'), null=True, blank=True)
    user_agent = models.TextField(_('user agent'), blank=True)
    
    # Viewing Data
    start_time = models.PositiveIntegerField(_('start time (seconds)'), default=0)
    end_time = models.PositiveIntegerField(_('end time (seconds)'), default=0)
    duration_watched = models.PositiveIntegerField(_('duration watched (seconds)'), default=0)
    quality_used = models.CharField(_('quality used'), max_length=10, choices=VideoQuality.choices, blank=True)
    
    # Technical Data
    buffer_events = models.PositiveIntegerField(_('buffer events'), default=0)
    seek_events = models.PositiveIntegerField(_('seek events'), default=0)
    pause_events = models.PositiveIntegerField(_('pause events'), default=0)
    
    # Timestamps
    session_started_at = models.DateTimeField(_('session started'), auto_now_add=True)
    session_ended_at = models.DateTimeField(_('session ended'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('Video Analytics')
        verbose_name_plural = _('Video Analytics')
        ordering = ['-session_started_at']
        indexes = [
            models.Index(fields=['video', 'session_started_at']),
            models.Index(fields=['user', 'session_started_at']),
        ]
    
    def __str__(self):
        user_display = self.user.get_display_name() if self.user else 'Anonymous'
        return f"{user_display} - {self.video.title} ({self.duration_watched}s)"


class VideoSubtitle(models.Model):
    """
    Subtitles/captions for videos
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    video = models.ForeignKey(Video, on_delete=models.CASCADE, related_name='subtitles')
    language_code = models.CharField(_('language code'), max_length=10)  # e.g., 'en', 'ar'
    language_name = models.CharField(_('language name'), max_length=50)  # e.g., 'English', 'العربية'
    
    # Subtitle File
    subtitle_file = models.FileField(_('subtitle file'), upload_to='videos/subtitles/')
    format = models.CharField(_('format'), max_length=10, default='vtt')  # vtt, srt, etc.
    
    # Settings
    is_default = models.BooleanField(_('default'), default=False)
    is_auto_generated = models.BooleanField(_('auto-generated'), default=False)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Video Subtitle')
        verbose_name_plural = _('Video Subtitles')
        unique_together = ['video', 'language_code']
        ordering = ['language_code']
    
    def __str__(self):
        return f"{self.video.title} - {self.language_name}"
    
    def clean(self):
        """Ensure only one default subtitle per video"""
        if self.is_default:
            VideoSubtitle.objects.filter(
                video=self.video, 
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)
