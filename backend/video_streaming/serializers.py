from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Video, VideoVariant, VideoProgress, VideoAnalytics, 
    VideoSubtitle, VideoQuality, VideoStatus
)

User = get_user_model()


class VideoVariantSerializer(serializers.ModelSerializer):
    resolution = serializers.ReadOnlyField()
    file_size_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = VideoVariant
        fields = [
            'id', 'quality', 'resolution', 'resolution_width', 'resolution_height',
            'bitrate', 'codec', 'file_size_bytes', 'file_size_formatted', 'is_ready'
        ]
    
    def get_file_size_formatted(self, obj):
        size = obj.file_size_bytes
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"


class VideoSubtitleSerializer(serializers.ModelSerializer):
    class Meta:
        model = VideoSubtitle
        fields = [
            'id', 'language_code', 'language_name', 'subtitle_file',
            'format', 'is_default', 'is_auto_generated'
        ]


class VideoProgressSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.get_display_name', read_only=True)
    duration_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = VideoProgress
        fields = [
            'id', 'user', 'user_name', 'current_time_seconds', 'total_watched_seconds',
            'completion_percentage', 'is_completed', 'session_count',
            'last_quality_watched', 'first_watched_at', 'last_watched_at',
            'completed_at', 'duration_formatted'
        ]
        read_only_fields = ['user', 'first_watched_at']
    
    def get_duration_formatted(self, obj):
        seconds = obj.current_time_seconds
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"


class VideoSerializer(serializers.ModelSerializer):
    variants = VideoVariantSerializer(many=True, read_only=True)
    subtitles = VideoSubtitleSerializer(many=True, read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_display_name', read_only=True)
    course_name = serializers.CharField(source='course.title', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    module_name = serializers.CharField(source='module.title', read_only=True)
    lesson_name = serializers.CharField(source='lesson.title', read_only=True)
    duration_formatted = serializers.ReadOnlyField()
    file_size_formatted = serializers.ReadOnlyField()
    streaming_urls = serializers.SerializerMethodField()
    user_progress = serializers.SerializerMethodField()
    
    class Meta:
        model = Video
        fields = [
            'id', 'title', 'title_ar', 'description', 'description_ar',
            'thumbnail', 'duration_seconds', 'duration_formatted',
            'file_size_bytes', 'file_size_formatted', 'original_resolution',
            'frame_rate', 'bitrate', 'codec', 'status', 'processing_progress',
            'error_message', 'course', 'course_name', 'course_code',
            'module', 'module_name', 'lesson', 'lesson_name',
            'uploaded_by', 'uploaded_by_name', 'is_public', 'allow_download',
            'require_authentication', 'view_count', 'unique_viewers',
            'created_at', 'updated_at', 'processed_at', 'variants',
            'subtitles', 'streaming_urls', 'user_progress'
        ]
        read_only_fields = [
            'id', 'duration_seconds', 'file_size_bytes', 'original_resolution',
            'frame_rate', 'bitrate', 'codec', 'status', 'processing_progress',
            'error_message', 'uploaded_by', 'view_count', 'unique_viewers',
            'created_at', 'updated_at', 'processed_at'
        ]
    
    def get_streaming_urls(self, obj):
        """Get streaming URLs for the video"""
        request = self.context.get('request')
        if not request:
            return {}
        
        from .services import VideoStreamingService
        service = VideoStreamingService()
        return service.get_streaming_urls(obj, request.user)
    
    def get_user_progress(self, obj):
        """Get current user's progress for this video"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None
        
        from .services import VideoStreamingService
        service = VideoStreamingService()
        return service.get_user_progress(obj, request.user)


class VideoCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Video
        fields = [
            'title', 'title_ar', 'description', 'description_ar',
            'original_file', 'course', 'module', 'lesson',
            'is_public', 'allow_download', 'require_authentication'
        ]
    
    def validate_original_file(self, value):
        """Validate video file"""
        if not value:
            raise serializers.ValidationError("Video file is required")
        
        # Check file size (max 2GB)
        max_size = 2 * 1024 * 1024 * 1024  # 2GB
        if value.size > max_size:
            raise serializers.ValidationError("Video file too large. Maximum size is 2GB.")
        
        # Check file extension
        allowed_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv']
        file_extension = value.name.lower().split('.')[-1]
        if f'.{file_extension}' not in allowed_extensions:
            raise serializers.ValidationError(
                f"Unsupported file format. Allowed formats: {', '.join(allowed_extensions)}"
            )
        
        return value


class VideoUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Video
        fields = [
            'title', 'title_ar', 'description', 'description_ar',
            'course', 'module', 'lesson', 'is_public', 'allow_download',
            'require_authentication'
        ]


class VideoProgressUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = VideoProgress
        fields = ['current_time_seconds', 'last_quality_watched']
    
    def validate_current_time_seconds(self, value):
        if value < 0:
            raise serializers.ValidationError("Current time cannot be negative")
        return value


class VideoAnalyticsSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.get_display_name', read_only=True)
    duration_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = VideoAnalytics
        fields = [
            'id', 'user', 'user_name', 'session_id', 'start_time', 'end_time',
            'duration_watched', 'duration_formatted', 'quality_used',
            'buffer_events', 'seek_events', 'pause_events',
            'session_started_at', 'session_ended_at'
        ]
        read_only_fields = ['user', 'session_started_at']
    
    def get_duration_formatted(self, obj):
        seconds = obj.duration_watched
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"


class VideoStatsSerializer(serializers.Serializer):
    """Serializer for video statistics"""
    total_views = serializers.IntegerField()
    unique_viewers = serializers.IntegerField()
    total_watch_time = serializers.IntegerField()
    total_watch_time_formatted = serializers.SerializerMethodField()
    average_completion = serializers.FloatField()
    completion_rate = serializers.FloatField()
    quality_distribution = serializers.DictField()
    viewing_patterns = serializers.ListField()
    
    def get_total_watch_time_formatted(self, obj):
        seconds = obj.get('total_watch_time', 0)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"


class VideoSubtitleCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = VideoSubtitle
        fields = [
            'video', 'language_code', 'language_name', 'subtitle_file',
            'format', 'is_default', 'is_auto_generated'
        ]
    
    def validate_subtitle_file(self, value):
        """Validate subtitle file"""
        if not value:
            raise serializers.ValidationError("Subtitle file is required")
        
        # Check file extension
        allowed_extensions = ['.vtt', '.srt', '.ass', '.ssa']
        file_extension = value.name.lower().split('.')[-1]
        if f'.{file_extension}' not in allowed_extensions:
            raise serializers.ValidationError(
                f"Unsupported subtitle format. Allowed formats: {', '.join(allowed_extensions)}"
            )
        
        return value


class BulkVideoUploadSerializer(serializers.Serializer):
    """Serializer for bulk video upload"""
    videos = serializers.ListField(
        child=serializers.FileField(),
        min_length=1,
        max_length=10
    )
    course = serializers.UUIDField()
    module = serializers.UUIDField(required=False)
    is_public = serializers.BooleanField(default=False)
    allow_download = serializers.BooleanField(default=False)
    require_authentication = serializers.BooleanField(default=True)
    
    def validate_videos(self, value):
        """Validate video files"""
        for video_file in value:
            # Check file size (max 2GB per file)
            max_size = 2 * 1024 * 1024 * 1024  # 2GB
            if video_file.size > max_size:
                raise serializers.ValidationError(
                    f"Video file {video_file.name} too large. Maximum size is 2GB."
                )
            
            # Check file extension
            allowed_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv']
            file_extension = video_file.name.lower().split('.')[-1]
            if f'.{file_extension}' not in allowed_extensions:
                raise serializers.ValidationError(
                    f"Unsupported file format for {video_file.name}. "
                    f"Allowed formats: {', '.join(allowed_extensions)}"
                )
        
        return value
