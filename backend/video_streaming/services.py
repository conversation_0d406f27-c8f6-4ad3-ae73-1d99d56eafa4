import os
import subprocess
import json
import logging
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.core.files.base import ContentFile
from django.utils import timezone
from .models import Video, VideoVariant, VideoQuality, VideoStatus
import tempfile
import shutil

logger = logging.getLogger(__name__)


class VideoProcessingService:
    """
    Service for processing videos with FFmpeg
    """
    
    # Quality presets for adaptive streaming
    QUALITY_PRESETS = {
        VideoQuality.QUALITY_240P: {
            'width': 426, 'height': 240, 'bitrate': 400, 'audio_bitrate': 64
        },
        VideoQuality.QUALITY_360P: {
            'width': 640, 'height': 360, 'bitrate': 800, 'audio_bitrate': 96
        },
        VideoQuality.QUALITY_480P: {
            'width': 854, 'height': 480, 'bitrate': 1200, 'audio_bitrate': 128
        },
        VideoQuality.QUALITY_720P: {
            'width': 1280, 'height': 720, 'bitrate': 2500, 'audio_bitrate': 128
        },
        VideoQuality.QUALITY_1080P: {
            'width': 1920, 'height': 1080, 'bitrate': 4500, 'audio_bitrate': 192
        },
        VideoQuality.QUALITY_1440P: {
            'width': 2560, 'height': 1440, 'bitrate': 8000, 'audio_bitrate': 192
        },
        VideoQuality.QUALITY_2160P: {
            'width': 3840, 'height': 2160, 'bitrate': 15000, 'audio_bitrate': 256
        }
    }
    
    def __init__(self):
        self.ffmpeg_path = getattr(settings, 'FFMPEG_PATH', 'ffmpeg')
        self.ffprobe_path = getattr(settings, 'FFPROBE_PATH', 'ffprobe')
        self.output_dir = getattr(settings, 'VIDEO_OUTPUT_DIR', 'media/videos/processed/')
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
    
    def get_video_info(self, video_path: str) -> Dict:
        """
        Extract video metadata using FFprobe
        """
        try:
            cmd = [
                self.ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            data = json.loads(result.stdout)
            
            # Find video stream
            video_stream = None
            audio_stream = None
            
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video' and not video_stream:
                    video_stream = stream
                elif stream.get('codec_type') == 'audio' and not audio_stream:
                    audio_stream = stream
            
            if not video_stream:
                raise ValueError("No video stream found")
            
            # Extract metadata
            format_info = data.get('format', {})
            
            return {
                'duration': float(format_info.get('duration', 0)),
                'file_size': int(format_info.get('size', 0)),
                'width': int(video_stream.get('width', 0)),
                'height': int(video_stream.get('height', 0)),
                'frame_rate': self._parse_frame_rate(video_stream.get('r_frame_rate', '0/1')),
                'bitrate': int(format_info.get('bit_rate', 0)) // 1000,  # Convert to kbps
                'codec': video_stream.get('codec_name', ''),
                'has_audio': audio_stream is not None
            }
            
        except (subprocess.CalledProcessError, json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to get video info for {video_path}: {e}")
            raise
    
    def _parse_frame_rate(self, frame_rate_str: str) -> float:
        """Parse frame rate from FFprobe format (e.g., '30/1')"""
        try:
            if '/' in frame_rate_str:
                num, den = frame_rate_str.split('/')
                return float(num) / float(den)
            return float(frame_rate_str)
        except (ValueError, ZeroDivisionError):
            return 0.0
    
    def generate_thumbnail(self, video_path: str, output_path: str, timestamp: float = 10.0) -> bool:
        """
        Generate thumbnail from video at specified timestamp
        """
        try:
            cmd = [
                self.ffmpeg_path,
                '-i', video_path,
                '-ss', str(timestamp),
                '-vframes', '1',
                '-q:v', '2',
                '-y',  # Overwrite output file
                output_path
            ]
            
            subprocess.run(cmd, capture_output=True, check=True)
            return os.path.exists(output_path)
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to generate thumbnail: {e}")
            return False
    
    def process_video_variants(self, video: Video) -> bool:
        """
        Process video into multiple quality variants
        """
        try:
            video.status = VideoStatus.PROCESSING
            video.processing_progress = 0
            video.save()
            
            # Get video info
            video_info = self.get_video_info(video.original_file.path)
            
            # Update video metadata
            video.duration_seconds = int(video_info['duration'])
            video.file_size_bytes = video_info['file_size']
            video.original_resolution = f"{video_info['width']}x{video_info['height']}"
            video.frame_rate = video_info['frame_rate']
            video.bitrate = video_info['bitrate']
            video.codec = video_info['codec']
            video.save()
            
            # Generate thumbnail if not exists
            if not video.thumbnail:
                self._generate_and_save_thumbnail(video, video_info['duration'])
            
            # Determine which qualities to generate based on original resolution
            original_height = video_info['height']
            qualities_to_generate = self._get_qualities_for_resolution(original_height)
            
            total_qualities = len(qualities_to_generate)
            processed_qualities = 0
            
            for quality in qualities_to_generate:
                success = self._process_quality_variant(video, quality, video_info)
                if success:
                    processed_qualities += 1
                
                # Update progress
                progress = int((processed_qualities / total_qualities) * 100)
                video.processing_progress = progress
                video.save()
            
            # Mark as ready if at least one variant was processed
            if processed_qualities > 0:
                video.status = VideoStatus.READY
                video.processed_at = timezone.now()
            else:
                video.status = VideoStatus.ERROR
                video.error_message = "Failed to process any video variants"
            
            video.processing_progress = 100
            video.save()
            
            return processed_qualities > 0
            
        except Exception as e:
            logger.error(f"Failed to process video {video.id}: {e}")
            video.status = VideoStatus.ERROR
            video.error_message = str(e)
            video.save()
            return False
    
    def _generate_and_save_thumbnail(self, video: Video, duration: float):
        """Generate and save thumbnail for video"""
        try:
            # Use timestamp at 10% of video duration or 10 seconds, whichever is smaller
            timestamp = min(duration * 0.1, 10.0)
            
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                temp_path = temp_file.name
            
            if self.generate_thumbnail(video.original_file.path, temp_path, timestamp):
                with open(temp_path, 'rb') as f:
                    video.thumbnail.save(
                        f"{video.id}_thumbnail.jpg",
                        ContentFile(f.read()),
                        save=True
                    )
                os.unlink(temp_path)
                
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for video {video.id}: {e}")
    
    def _get_qualities_for_resolution(self, original_height: int) -> List[VideoQuality]:
        """Determine which quality variants to generate based on original resolution"""
        qualities = []
        
        # Only generate qualities that are equal or lower than original
        if original_height >= 240:
            qualities.append(VideoQuality.QUALITY_240P)
        if original_height >= 360:
            qualities.append(VideoQuality.QUALITY_360P)
        if original_height >= 480:
            qualities.append(VideoQuality.QUALITY_480P)
        if original_height >= 720:
            qualities.append(VideoQuality.QUALITY_720P)
        if original_height >= 1080:
            qualities.append(VideoQuality.QUALITY_1080P)
        if original_height >= 1440:
            qualities.append(VideoQuality.QUALITY_1440P)
        if original_height >= 2160:
            qualities.append(VideoQuality.QUALITY_2160P)
        
        return qualities
    
    def _process_quality_variant(self, video: Video, quality: VideoQuality, video_info: Dict) -> bool:
        """Process a single quality variant"""
        try:
            preset = self.QUALITY_PRESETS[quality]
            
            # Create output filename
            output_filename = f"{video.id}_{quality}.mp4"
            output_path = os.path.join(self.output_dir, output_filename)
            
            # Build FFmpeg command
            cmd = [
                self.ffmpeg_path,
                '-i', video.original_file.path,
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-maxrate', f"{preset['bitrate']}k",
                '-bufsize', f"{preset['bitrate'] * 2}k",
                '-vf', f"scale={preset['width']}:{preset['height']}:force_original_aspect_ratio=decrease,pad={preset['width']}:{preset['height']}:(ow-iw)/2:(oh-ih)/2",
                '-c:a', 'aac',
                '-b:a', f"{preset['audio_bitrate']}k",
                '-movflags', '+faststart',
                '-y',  # Overwrite output file
                output_path
            ]
            
            # Run FFmpeg
            subprocess.run(cmd, capture_output=True, check=True)
            
            # Create VideoVariant record
            file_size = os.path.getsize(output_path)
            
            variant, created = VideoVariant.objects.get_or_create(
                video=video,
                quality=quality,
                defaults={
                    'file_path': output_path,
                    'file_size_bytes': file_size,
                    'resolution_width': preset['width'],
                    'resolution_height': preset['height'],
                    'bitrate': preset['bitrate'],
                    'codec': 'h264',
                    'is_ready': True,
                    'processing_completed_at': timezone.now()
                }
            )
            
            if not created:
                # Update existing variant
                variant.file_path = output_path
                variant.file_size_bytes = file_size
                variant.is_ready = True
                variant.processing_completed_at = timezone.now()
                variant.save()
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg failed for quality {quality}: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to process quality {quality}: {e}")
            return False
    
    def cleanup_video_files(self, video: Video):
        """Clean up processed video files"""
        try:
            for variant in video.variants.all():
                if variant.file_path and os.path.exists(variant.file_path):
                    os.unlink(variant.file_path)
                    logger.info(f"Deleted variant file: {variant.file_path}")
            
            # Delete original file if needed
            if video.original_file and os.path.exists(video.original_file.path):
                os.unlink(video.original_file.path)
                logger.info(f"Deleted original file: {video.original_file.path}")
                
        except Exception as e:
            logger.error(f"Failed to cleanup files for video {video.id}: {e}")


class VideoStreamingService:
    """
    Service for handling video streaming and progress tracking
    """
    
    def __init__(self):
        self.processing_service = VideoProcessingService()
    
    def get_streaming_urls(self, video: Video, user=None) -> Dict:
        """
        Get streaming URLs for different quality variants
        """
        if video.status != VideoStatus.READY:
            return {}
        
        # Check permissions
        if video.require_authentication and not user:
            return {}
        
        urls = {}
        for variant in video.variants.filter(is_ready=True).order_by('quality'):
            # In production, these would be CDN URLs or signed URLs
            urls[variant.quality] = {
                'url': f'/api/video-streaming/stream/{video.id}/{variant.quality}/',
                'resolution': variant.resolution,
                'bitrate': variant.bitrate,
                'file_size': variant.file_size_bytes
            }
        
        return urls
    
    def update_watch_progress(self, video: Video, user, current_time: int, session_id: str = None):
        """
        Update user's watch progress for a video
        """
        from .models import VideoProgress, VideoAnalytics
        
        # Update or create progress record
        progress, created = VideoProgress.objects.get_or_create(
            video=video,
            user=user,
            defaults={'session_count': 1}
        )
        
        if not created:
            progress.session_count += 1
        
        # Update progress
        progress.update_progress(current_time, video.duration_seconds)
        
        # Create analytics record if session_id provided
        if session_id:
            VideoAnalytics.objects.create(
                video=video,
                user=user,
                session_id=session_id,
                start_time=progress.current_time_seconds,
                end_time=current_time,
                duration_watched=abs(current_time - progress.current_time_seconds)
            )
        
        # Update video view count
        if created:
            video.unique_viewers += 1
            video.save(update_fields=['unique_viewers'])
    
    def get_user_progress(self, video: Video, user) -> Dict:
        """
        Get user's progress for a video
        """
        try:
            progress = VideoProgress.objects.get(video=video, user=user)
            return {
                'current_time': progress.current_time_seconds,
                'completion_percentage': float(progress.completion_percentage),
                'is_completed': progress.is_completed,
                'last_watched': progress.last_watched_at.isoformat(),
                'session_count': progress.session_count
            }
        except VideoProgress.DoesNotExist:
            return {
                'current_time': 0,
                'completion_percentage': 0.0,
                'is_completed': False,
                'last_watched': None,
                'session_count': 0
            }
    
    def get_video_analytics(self, video: Video) -> Dict:
        """
        Get analytics data for a video
        """
        from .models import VideoAnalytics
        
        analytics = VideoAnalytics.objects.filter(video=video)
        
        return {
            'total_views': video.view_count,
            'unique_viewers': video.unique_viewers,
            'total_watch_time': analytics.aggregate(
                total=models.Sum('duration_watched')
            )['total'] or 0,
            'average_completion': VideoProgress.objects.filter(
                video=video
            ).aggregate(
                avg=models.Avg('completion_percentage')
            )['avg'] or 0,
            'completion_rate': VideoProgress.objects.filter(
                video=video, is_completed=True
            ).count() / max(video.unique_viewers, 1) * 100
        }
