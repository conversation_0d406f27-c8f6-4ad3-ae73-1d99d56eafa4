from django.db.models.signals import post_save, pre_delete, post_delete
from django.dispatch import receiver
from django.utils import timezone
from .models import Video, VideoProgress, VideoAnalytics, VideoStatus
from .services import VideoProcessingService
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Video)
def handle_video_upload(sender, instance, created, **kwargs):
    """
    Handle video upload and start processing
    """
    if created and instance.original_file:
        # Set initial status
        instance.status = VideoStatus.UPLOADING
        instance.save(update_fields=['status'])
        
        # In production, this would be handled by a task queue like Celery
        # For now, we'll process synchronously
        try:
            processing_service = VideoProcessingService()
            # This could be moved to a background task
            # processing_service.process_video_variants(instance)
            logger.info(f"Video {instance.id} uploaded and queued for processing")
        except Exception as e:
            logger.error(f"Failed to queue video {instance.id} for processing: {e}")
            instance.status = VideoStatus.ERROR
            instance.error_message = str(e)
            instance.save(update_fields=['status', 'error_message'])


@receiver(pre_delete, sender=Video)
def cleanup_video_files(sender, instance, **kwargs):
    """
    Clean up video files before deleting video record
    """
    try:
        processing_service = VideoProcessingService()
        processing_service.cleanup_video_files(instance)
        logger.info(f"Cleaned up files for video {instance.id}")
    except Exception as e:
        logger.error(f"Failed to cleanup files for video {instance.id}: {e}")


@receiver(post_save, sender=VideoProgress)
def update_video_analytics(sender, instance, created, **kwargs):
    """
    Update video analytics when progress is updated
    """
    if not created:  # Only for updates, not new records
        video = instance.video
        
        # Update video's unique viewers count
        unique_viewers = VideoProgress.objects.filter(video=video).count()
        if video.unique_viewers != unique_viewers:
            video.unique_viewers = unique_viewers
            video.save(update_fields=['unique_viewers'])


@receiver(post_save, sender=VideoAnalytics)
def update_video_view_count(sender, instance, created, **kwargs):
    """
    Update video view count when new analytics record is created
    """
    if created:
        video = instance.video
        video.view_count += 1
        video.save(update_fields=['view_count'])


@receiver(post_delete, sender=VideoProgress)
def update_unique_viewers_on_delete(sender, instance, **kwargs):
    """
    Update unique viewers count when progress record is deleted
    """
    video = instance.video
    unique_viewers = VideoProgress.objects.filter(video=video).count()
    video.unique_viewers = unique_viewers
    video.save(update_fields=['unique_viewers'])
