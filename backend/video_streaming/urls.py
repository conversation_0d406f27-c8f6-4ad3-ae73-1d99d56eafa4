from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'videos', views.VideoViewSet, basename='video')
router.register(r'progress', views.VideoProgressViewSet, basename='videoprogress')
router.register(r'subtitles', views.VideoSubtitleViewSet, basename='videosubtitle')

urlpatterns = [
    # Router URLs
    path('', include(router.urls)),
    
    # Video streaming
    path('stream/<uuid:video_id>/<str:quality>/', views.stream_video, name='stream-video'),
    
    # Bulk operations
    path('bulk-upload/', views.bulk_upload_videos, name='bulk-upload-videos'),
]
