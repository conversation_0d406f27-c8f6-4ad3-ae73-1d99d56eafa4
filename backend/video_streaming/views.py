from rest_framework import generics, status, permissions, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.http import HttpResponse, Http404, FileResponse
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, Avg, Sum
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied
from courses.models import Course
from content_management.models import CourseModule, Lesson
from .models import (
    Video, VideoVariant, VideoProgress, VideoAnalytics,
    VideoSubtitle, VideoStatus, VideoQuality
)
from .serializers import (
    VideoSerializer, VideoCreateSerializer, VideoUpdateSerializer,
    VideoProgressSerializer, VideoProgressUpdateSerializer,
    VideoAnalyticsSerializer, VideoStatsSerializer,
    VideoSubtitleSerializer, VideoSubtitleCreateSerializer,
    BulkVideoUploadSerializer
)
from .services import VideoProcessingService, VideoStreamingService
import os
import mimetypes
import uuid

User = get_user_model()


class VideoViewSet(viewsets.ModelViewSet):
    """ViewSet for video management"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'create':
            return VideoCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return VideoUpdateSerializer
        return VideoSerializer
    
    def get_queryset(self):
        user = self.request.user
        queryset = Video.objects.select_related(
            'course', 'module', 'lesson', 'uploaded_by'
        ).prefetch_related('variants', 'subtitles')
        
        # Filter based on user role and permissions
        if user.role == 'student':
            # Students see videos from their enrolled courses
            enrolled_courses = user.enrollments.filter(
                status='enrolled',
                is_active=True
            ).values_list('course_id', flat=True)
            
            queryset = queryset.filter(
                Q(course_id__in=enrolled_courses) |
                Q(is_public=True, require_authentication=False)
            ).filter(status=VideoStatus.READY)
            
        elif user.role == 'teacher':
            # Teachers see videos from courses they teach
            teaching_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            
            queryset = queryset.filter(
                Q(uploaded_by=user) |
                Q(course_id__in=teaching_courses) |
                Q(is_public=True)
            )
        
        # Filter by course if specified
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        
        # Filter by module if specified
        module_id = self.request.query_params.get('module_id')
        if module_id:
            queryset = queryset.filter(module_id=module_id)
        
        # Filter by lesson if specified
        lesson_id = self.request.query_params.get('lesson_id')
        if lesson_id:
            queryset = queryset.filter(lesson_id=lesson_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter and user.role in ['teacher', 'admin', 'super_admin']:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')
    
    def perform_create(self, serializer):
        video = serializer.save(uploaded_by=self.request.user)
        
        # Start video processing asynchronously
        # In production, this would be handled by a task queue like Celery
        try:
            processing_service = VideoProcessingService()
            processing_service.process_video_variants(video)
        except Exception as e:
            video.status = VideoStatus.ERROR
            video.error_message = str(e)
            video.save()
    
    @action(detail=True, methods=['post'])
    def reprocess(self, request, pk=None):
        """Reprocess video variants"""
        video = self.get_object()
        
        if video.uploaded_by != request.user and request.user.role not in ['admin', 'super_admin']:
            raise PermissionDenied("You don't have permission to reprocess this video")
        
        try:
            processing_service = VideoProcessingService()
            success = processing_service.process_video_variants(video)
            
            if success:
                return Response({'message': 'Video reprocessing started'})
            else:
                return Response(
                    {'error': 'Failed to start video reprocessing'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """Get video statistics"""
        video = self.get_object()
        
        # Check permissions
        if (video.uploaded_by != request.user and 
            request.user.role not in ['teacher', 'admin', 'super_admin']):
            raise PermissionDenied("You don't have permission to view statistics")
        
        streaming_service = VideoStreamingService()
        stats = streaming_service.get_video_analytics(video)
        
        # Add quality distribution
        quality_stats = video.variants.filter(is_ready=True).values(
            'quality'
        ).annotate(
            views=Count('video__analytics__quality_used')
        )
        
        stats['quality_distribution'] = {
            item['quality']: item['views'] for item in quality_stats
        }
        
        # Add viewing patterns (hourly distribution)
        viewing_patterns = VideoAnalytics.objects.filter(
            video=video
        ).extra(
            select={'hour': 'EXTRACT(hour FROM session_started_at)'}
        ).values('hour').annotate(
            count=Count('id')
        ).order_by('hour')
        
        stats['viewing_patterns'] = list(viewing_patterns)
        
        serializer = VideoStatsSerializer(stats)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_progress(self, request, pk=None):
        """Update user's viewing progress"""
        video = self.get_object()
        
        serializer = VideoProgressUpdateSerializer(data=request.data)
        if serializer.is_valid():
            current_time = serializer.validated_data['current_time_seconds']
            quality = serializer.validated_data.get('last_quality_watched', '')
            session_id = request.data.get('session_id', str(uuid.uuid4()))
            
            streaming_service = VideoStreamingService()
            streaming_service.update_watch_progress(
                video, request.user, current_time, session_id
            )
            
            # Get updated progress
            progress = streaming_service.get_user_progress(video, request.user)
            return Response(progress)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """Get user's viewing progress"""
        video = self.get_object()
        
        streaming_service = VideoStreamingService()
        progress = streaming_service.get_user_progress(video, request.user)
        return Response(progress)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def stream_video(request, video_id, quality):
    """Stream video file"""
    try:
        video = get_object_or_404(Video, id=video_id)
        
        # Check permissions
        if video.require_authentication and not request.user.is_authenticated:
            raise PermissionDenied("Authentication required")
        
        if video.status != VideoStatus.READY:
            raise Http404("Video not ready")
        
        # Get video variant
        try:
            variant = video.variants.get(quality=quality, is_ready=True)
        except VideoVariant.DoesNotExist:
            raise Http404("Video quality not available")
        
        # Check if file exists
        if not os.path.exists(variant.file_path):
            raise Http404("Video file not found")
        
        # Update view count
        video.view_count += 1
        video.save(update_fields=['view_count'])
        
        # Determine content type
        content_type, _ = mimetypes.guess_type(variant.file_path)
        if not content_type:
            content_type = 'video/mp4'
        
        # Handle range requests for video seeking
        file_size = os.path.getsize(variant.file_path)
        range_header = request.META.get('HTTP_RANGE')
        
        if range_header:
            # Parse range header
            range_match = range_header.replace('bytes=', '').split('-')
            start = int(range_match[0]) if range_match[0] else 0
            end = int(range_match[1]) if range_match[1] else file_size - 1
            
            # Ensure valid range
            start = max(0, start)
            end = min(file_size - 1, end)
            content_length = end - start + 1
            
            # Create partial response
            response = HttpResponse(
                status=206,  # Partial Content
                content_type=content_type
            )
            response['Content-Range'] = f'bytes {start}-{end}/{file_size}'
            response['Accept-Ranges'] = 'bytes'
            response['Content-Length'] = str(content_length)
            
            # Read and return file chunk
            with open(variant.file_path, 'rb') as f:
                f.seek(start)
                response.write(f.read(content_length))
            
            return response
        else:
            # Return full file
            return FileResponse(
                open(variant.file_path, 'rb'),
                content_type=content_type,
                filename=f"{video.title}.mp4"
            )
            
    except Exception as e:
        raise Http404(f"Error streaming video: {str(e)}")


class VideoProgressViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing video progress"""
    serializer_class = VideoProgressSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        queryset = VideoProgress.objects.select_related('video', 'user')
        
        if user.role == 'student':
            # Students see only their own progress
            queryset = queryset.filter(user=user)
        elif user.role == 'teacher':
            # Teachers see progress for their courses
            teaching_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            queryset = queryset.filter(video__course_id__in=teaching_courses)
        
        # Filter by video if specified
        video_id = self.request.query_params.get('video_id')
        if video_id:
            queryset = queryset.filter(video_id=video_id)
        
        # Filter by user if specified (for teachers/admins)
        user_id = self.request.query_params.get('user_id')
        if user_id and user.role in ['teacher', 'admin', 'super_admin']:
            queryset = queryset.filter(user_id=user_id)
        
        return queryset.order_by('-last_watched_at')


class VideoSubtitleViewSet(viewsets.ModelViewSet):
    """ViewSet for video subtitles"""
    serializer_class = VideoSubtitleSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'create':
            return VideoSubtitleCreateSerializer
        return VideoSubtitleSerializer
    
    def get_queryset(self):
        user = self.request.user
        queryset = VideoSubtitle.objects.select_related('video')
        
        # Filter by video if specified
        video_id = self.request.query_params.get('video_id')
        if video_id:
            queryset = queryset.filter(video_id=video_id)
        
        # Check permissions based on video access
        if user.role == 'student':
            enrolled_courses = user.enrollments.filter(
                status='enrolled',
                is_active=True
            ).values_list('course_id', flat=True)
            
            queryset = queryset.filter(
                Q(video__course_id__in=enrolled_courses) |
                Q(video__is_public=True)
            )
        elif user.role == 'teacher':
            teaching_courses = Course.objects.filter(
                Q(instructor=user) | Q(assistant_instructors=user)
            ).values_list('id', flat=True)
            
            queryset = queryset.filter(
                Q(video__uploaded_by=user) |
                Q(video__course_id__in=teaching_courses) |
                Q(video__is_public=True)
            )
        
        return queryset.order_by('video', 'language_code')


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def bulk_upload_videos(request):
    """Bulk upload multiple videos"""
    serializer = BulkVideoUploadSerializer(data=request.data)
    
    if serializer.is_valid():
        videos_data = serializer.validated_data
        course_id = videos_data['course']
        module_id = videos_data.get('module')
        
        # Verify course access
        try:
            course = Course.objects.get(id=course_id)
            if (request.user.role == 'teacher' and 
                course.instructor != request.user and 
                request.user not in course.assistant_instructors.all()):
                raise PermissionDenied("You don't have permission to upload to this course")
        except Course.DoesNotExist:
            return Response(
                {'error': 'Course not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Verify module access if specified
        module = None
        if module_id:
            try:
                module = CourseModule.objects.get(id=module_id, course=course)
            except CourseModule.DoesNotExist:
                return Response(
                    {'error': 'Module not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        created_videos = []
        errors = []
        
        for video_file in videos_data['videos']:
            try:
                # Create video record
                video = Video.objects.create(
                    title=video_file.name.rsplit('.', 1)[0],  # Remove extension
                    original_file=video_file,
                    course=course,
                    module=module,
                    uploaded_by=request.user,
                    is_public=videos_data['is_public'],
                    allow_download=videos_data['allow_download'],
                    require_authentication=videos_data['require_authentication']
                )
                
                # Start processing
                try:
                    processing_service = VideoProcessingService()
                    processing_service.process_video_variants(video)
                except Exception as e:
                    video.status = VideoStatus.ERROR
                    video.error_message = str(e)
                    video.save()
                
                created_videos.append(video)
                
            except Exception as e:
                errors.append(f"Failed to upload {video_file.name}: {str(e)}")
        
        # Serialize created videos
        video_serializer = VideoSerializer(created_videos, many=True, context={'request': request})
        
        return Response({
            'created_count': len(created_videos),
            'errors': errors,
            'videos': video_serializer.data
        }, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
