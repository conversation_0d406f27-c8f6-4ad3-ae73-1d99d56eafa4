import { useEffect, Suspense, lazy, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { useTranslation } from 'react-i18next';

// Store and i18n
import { store } from './store';
import { useAppDispatch } from './store';
import { setCredentials } from './store/slices/authSlice';
import { securityService } from './services/securityServiceSimple';
import './i18n';

// Essential components (not lazy loaded)
import Layout from './components/common/Layout';
import PublicLayout from './components/common/PublicLayout';
import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import LoginPage from './pages/LoginPage';
import ProtectedRoute from './components/common/ProtectedRoute';
import RoleBasedRoute, { RoleProtectedRoute } from './components/common/RoleBasedRoute';
import LoadingSpinner from './components/common/LoadingSpinner';
import { DashboardCardSkeleton } from './components/common/SkeletonLoader';
import { WebSocketProvider } from './contexts/WebSocketContext';

// Role-specific dashboards - Lazy loaded for better performance
const SuperAdminDashboard = lazy(() => import('./roles/super-admin/Dashboard'));
const AdminDashboard = lazy(() => import('./roles/admin/Dashboard'));
const TeacherDashboard = lazy(() => import('./roles/teacher/Dashboard'));
const StudentDashboard = lazy(() => import('./roles/student/Dashboard'));
const FinanceOfficerDashboard = lazy(() => import('./roles/finance-officer/Dashboard'));
const StaffDashboard = lazy(() => import('./roles/staff/Dashboard'));

// Common pages - Keep critical pages as direct imports
import ProfilePage from './pages/ProfilePage';
import NotFoundPage from './pages/NotFoundPage';

// Lazy load major pages for code splitting
const CoursesPage = lazy(() => import('./pages/CoursesPage'));
const CourseInterfacePage = lazy(() => import('./pages/CourseInterfacePage'));
const CourseDetailPage = lazy(() => import('./pages/CourseDetailPage'));
const StudentDetailPage = lazy(() => import('./pages/StudentDetailPage'));
const AssignmentDetailPage = lazy(() => import('./pages/AssignmentDetailPage'));
const TeacherDetailPage = lazy(() => import('./pages/TeacherDetailPage'));
const ExamDetailPage = lazy(() => import('./pages/ExamDetailPage'));
const DepartmentDetailPage = lazy(() => import('./pages/DepartmentDetailPage'));
const UserDetailPage = lazy(() => import('./pages/UserDetailPage'));
const StudyGroupDetailPage = lazy(() => import('./pages/StudyGroupDetailPage'));
const TaskDetailPage = lazy(() => import('./pages/TaskDetailPage'));
const ReportDetailPage = lazy(() => import('./pages/ReportDetailPage'));
const DocumentDetailPage = lazy(() => import('./pages/DocumentDetailPage'));
const EnrollmentPage = lazy(() => import('./pages/EnrollmentPage'));
const AssignmentsPage = lazy(() => import('./pages/AssignmentsPage'));
const StudentsPage = lazy(() => import('./pages/StudentsPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const StudentCardPage = lazy(() => import('./pages/StudentCardPage'));
const QRVerificationPage = lazy(() => import('./pages/QRVerificationPage'));
const StaffVerificationPage = lazy(() => import('./pages/StaffVerificationPage'));

const SchedulePage = lazy(() => import('./pages/SchedulePage'));
const PaymentsPage = lazy(() => import('./pages/PaymentsPage'));
const GradesPage = lazy(() => import('./pages/GradesPage'));
const ReportsPage = lazy(() => import('./pages/ReportsPage'));
const TeachersPage = lazy(() => import('./pages/TeachersPage'));
const NotificationsPage = lazy(() => import('./pages/NotificationsPage'));
const LibraryPage = lazy(() => import('./pages/LibraryPage'));
const ExamsPage = lazy(() => import('./pages/ExamsPage'));
const AttendancePage = lazy(() => import('./pages/AttendancePage'));
const DepartmentsPage = lazy(() => import('./pages/DepartmentsPage'));
const AcademicCalendarPage = lazy(() => import('./pages/AcademicCalendarPage'));
const UsersPage = lazy(() => import('./pages/UsersPage'));
const SystemPage = lazy(() => import('./pages/SystemPage'));
const TasksPage = lazy(() => import('./pages/TasksPage'));
const DocumentsPage = lazy(() => import('./pages/DocumentsPage'));
const CustomChartsPage = lazy(() => import('./pages/CustomChartsPage'));
const StudyGroupsPage = lazy(() => import('./pages/StudyGroupsPage'));
const InvoicesPage = lazy(() => import('./pages/InvoicesPage'));
const RequestsPage = lazy(() => import('./pages/RequestsPage'));
const MessagingPage = lazy(() => import('./pages/MessagingPage'));
const AdvancedAnalyticsPage = lazy(() => import('./pages/AdvancedAnalyticsPage'));
const AssessmentsPage = lazy(() => import('./pages/AssessmentsPage'));
import TestingPanel from './components/testing/TestingPanel';
import PWAStatus from './components/pwa/PWAStatus';
import SimplePerformanceMonitor from './components/performance/SimplePerformanceMonitor';
import PerformanceOptimizer from './components/performance/PerformanceOptimizer';
import BundleAnalyzer from './components/performance/BundleAnalyzer';

function AppContent() {
  const { i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const [showPerformanceTools, setShowPerformanceTools] = useState(false);

  useEffect(() => {
    // Initialize security service
    const initializeSecurity = async () => {
      try {
        await securityService.initialize();
        console.log('🔐 Security service initialized for web');
      } catch (error) {
        console.error('Security initialization failed:', error);
      }
    };

    initializeSecurity();

    // Set initial direction based on language
    const dir = i18n.language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.dir = dir;
    document.documentElement.lang = i18n.language;
  }, [i18n.language]);

  // Users must authenticate properly through login page



  return (
    <Router>
      <div className="min-h-screen">
        <Routes>
          {/* Public routes with PublicLayout */}
          <Route path="/" element={<PublicLayout />}>
            <Route index element={<HomePage />} />
            <Route path="features" element={<div className="p-8">Features Page Coming Soon</div>} />
            <Route path="about" element={<AboutPage />} />
            <Route path="pricing" element={<div className="p-8">Pricing Page Coming Soon</div>} />
            <Route path="contact" element={<div className="p-8">Contact Page Coming Soon</div>} />
          </Route>

          {/* Auth routes (no layout) */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<div className="p-8">Register Page Coming Soon</div>} />

          {/* Protected routes with authenticated Layout */}
          <Route path="/app" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            {/* Role-based dashboard routes */}
            <Route index element={<Navigate to="/app/dashboard" replace />} />

            <Route path="dashboard" element={
              <Suspense fallback={
                <div className="p-6 space-y-6">
                  <DashboardCardSkeleton />
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <DashboardCardSkeleton />
                    <DashboardCardSkeleton />
                    <DashboardCardSkeleton />
                    <DashboardCardSkeleton />
                  </div>
                </div>
              }>
                <RoleBasedRoute
                  superAdminComponent={<SuperAdminDashboard />}
                  adminComponent={<AdminDashboard />}
                  teacherComponent={<TeacherDashboard />}
                  studentComponent={<StudentDashboard />}
                  financeOfficerComponent={<FinanceOfficerDashboard />}
                  staffComponent={<StaffDashboard />}
                />
              </Suspense>
            } />

            {/* Common routes */}
            <Route path="profile" element={<ProfilePage />} />

            {/* Lazy-loaded routes with Suspense */}
            <Route path="courses" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading courses..." />}>
                <CoursesPage />
              </Suspense>
            } />
            <Route path="courses/:courseId" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading course details..." />}>
                <CourseDetailPage />
              </Suspense>
            } />
            <Route path="course/:courseId" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading course interface..." />}>
                <CourseInterfacePage />
              </Suspense>
            } />
            <Route path="courses/:courseId/content" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'teacher']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading content management..." />}>
                  <div className="p-6">Content Management Page Coming Soon</div>
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="courses/:courseId/forums" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading forums..." />}>
                <div className="p-6">Forums Page Coming Soon</div>
              </Suspense>
            } />
            <Route path="enrollment" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading enrollment..." />}>
                <EnrollmentPage />
              </Suspense>
            } />
            <Route path="assignments" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading assignments..." />}>
                <AssignmentsPage />
              </Suspense>
            } />
            <Route path="assignments/:assignmentId" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading assignment details..." />}>
                <AssignmentDetailPage />
              </Suspense>
            } />
            <Route path="students" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'teacher', 'finance_officer', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading students..." />}>
                  <StudentsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="students/:studentId" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'teacher', 'finance_officer', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading student details..." />}>
                  <StudentDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="student-card" element={
              <RoleProtectedRoute requiredRole="student">
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading student card..." />}>
                  <StudentCardPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="qr-verification" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading QR verification..." />}>
                <QRVerificationPage />
              </Suspense>
            } />
            <Route path="staff-verification" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'teacher', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading staff verification..." />}>
                  <StaffVerificationPage />
                </Suspense>
              </RoleProtectedRoute>
            } />

            <Route path="settings" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading settings..." />}>
                <SettingsPage />
              </Suspense>
            } />
            <Route path="schedule" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading schedule..." />}>
                <SchedulePage />
              </Suspense>
            } />
            <Route path="payments" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'finance_officer']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading payments..." />}>
                  <PaymentsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="grades" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading grades..." />}>
                <GradesPage />
              </Suspense>
            } />
            <Route path="reports" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'finance_officer']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading reports..." />}>
                  <ReportsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="reports/:reportId" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'finance_officer']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading report details..." />}>
                  <ReportDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="teachers" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading teachers..." />}>
                  <TeachersPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="teachers/:teacherId" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading teacher details..." />}>
                  <TeacherDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="notifications" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading notifications..." />}>
                <NotificationsPage />
              </Suspense>
            } />
            <Route path="library" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading library..." />}>
                <LibraryPage />
              </Suspense>
            } />
            <Route path="exams" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading exams..." />}>
                <ExamsPage />
              </Suspense>
            } />
            <Route path="exams/:examId" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading exam details..." />}>
                <ExamDetailPage />
              </Suspense>
            } />
            <Route path="assessments" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading assessments..." />}>
                <AssessmentsPage />
              </Suspense>
            } />
            <Route path="assessments/:assessmentId" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading assessment details..." />}>
                <div className="p-6">Assessment Detail Page Coming Soon</div>
              </Suspense>
            } />
            <Route path="assessments/:assessmentId/take" element={
              <RoleProtectedRoute requiredRole="student">
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading assessment..." />}>
                  <div className="p-6">Take Assessment Page Coming Soon</div>
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="assessments/:assessmentId/edit" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'teacher']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading assessment editor..." />}>
                  <div className="p-6">Edit Assessment Page Coming Soon</div>
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="attendance" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading attendance..." />}>
                <AttendancePage />
              </Suspense>
            } />
            <Route path="departments" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'finance_officer', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading departments..." />}>
                  <DepartmentsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="departments/:departmentId" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'finance_officer', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading department details..." />}>
                  <DepartmentDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="academic-calendar" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading academic calendar..." />}>
                <AcademicCalendarPage />
              </Suspense>
            } />
            <Route path="study-groups" element={
              <RoleProtectedRoute requiredRole="student">
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading study groups..." />}>
                  <StudyGroupsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="study-groups/:groupId" element={
              <RoleProtectedRoute requiredRole="student">
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading study group details..." />}>
                  <StudyGroupDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="users" element={
              <RoleProtectedRoute requiredRole="super_admin">
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading users..." />}>
                  <UsersPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="users/:userId" element={
              <RoleProtectedRoute requiredRole="super_admin">
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading user details..." />}>
                  <UserDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="system" element={
              <RoleProtectedRoute requiredRole="super_admin">
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading system..." />}>
                  <SystemPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="tasks" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading tasks..." />}>
                  <TasksPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="tasks/:taskId" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading task details..." />}>
                  <TaskDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="documents" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading documents..." />}>
                  <DocumentsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="documents/:documentId" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading document details..." />}>
                  <DocumentDetailPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="custom-charts" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'admin', 'teacher']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading custom charts..." />}>
                  <CustomChartsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="invoices" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'finance_officer']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading invoices..." />}>
                  <InvoicesPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="requests" element={
              <RoleProtectedRoute allowedRoles={['super_admin', 'staff']}>
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading requests..." />}>
                  <RequestsPage />
                </Suspense>
              </RoleProtectedRoute>
            } />
            <Route path="messaging" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading messaging..." />}>
                <MessagingPage />
              </Suspense>
            } />
            <Route path="advanced-analytics" element={
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading advanced analytics..." />}>
                <AdvancedAnalyticsPage />
              </Suspense>
            } />

            {/* Role-specific routes will be added here */}

            {/* 404 route */}
            <Route path="*" element={<NotFoundPage />} />
          </Route>
        </Routes>
      </div>

      {/* PWA Status - Temporarily disabled to fix React hooks issue */}
      {/* <div className="fixed bottom-4 right-4 z-50 max-w-sm">
        <PWAStatus />
      </div> */}

      {/* Testing Panel - Only in development */}
      {import.meta.env.DEV && <TestingPanel />}

      {/* Performance Monitor - Simple version without infinite loops */}
      {import.meta.env.DEV && <SimplePerformanceMonitor />}

      {/* Advanced Performance Tools - Development only */}
      {import.meta.env.DEV && (
        <>
          <PerformanceOptimizer
            isVisible={showPerformanceTools}
            onOptimizationSuggestion={(suggestions) => {
              console.log('Performance suggestions:', suggestions);
            }}
          />
          <BundleAnalyzer
            isVisible={showPerformanceTools}
            onOptimizationNeeded={(suggestions) => {
              console.log('Bundle optimization needed:', suggestions);
            }}
          />
          {/* Performance Tools Toggle */}
          <button
            onClick={() => setShowPerformanceTools(!showPerformanceTools)}
            className="fixed bottom-20 right-4 bg-purple-500 text-white p-2 rounded-full shadow-lg hover:bg-purple-600 z-50"
            title="Toggle Performance Tools"
          >
            📊
          </button>
        </>
      )}
    </Router>
  );
}

function App() {
  return (
    <Provider store={store}>
      <WebSocketProvider autoConnect={true}>
        <AppContent />
      </WebSocketProvider>
    </Provider>
  );
}

export default App;
