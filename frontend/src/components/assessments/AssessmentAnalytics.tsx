import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';
import {
  Bar<PERSON>hart3,
  TrendingUp,
  TrendingDown,
  Users,
  Target,
  Clock,
  FileText,
  Download,
  Filter,
  Calendar,
  Award,
  AlertTriangle,
  CheckCircle,
  Eye
} from 'lucide-react';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '../charts';

interface AssessmentStats {
  assessment_id: string;
  assessment_title: string;
  total_attempts: number;
  completed_attempts: number;
  average_score: number;
  highest_score: number;
  lowest_score: number;
  pass_rate: number;
  average_time_minutes: number;
  difficulty_index: number;
  discrimination_index: number;
}

interface QuestionAnalytics {
  question_id: string;
  question_text: string;
  question_type: string;
  correct_percentage: number;
  average_time_seconds: number;
  difficulty_level: string;
  discrimination_index: number;
  response_distribution: { [key: string]: number };
}

interface PerformanceTrend {
  date: string;
  average_score: number;
  completion_rate: number;
  attempt_count: number;
}

const AssessmentAnalytics: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const { token } = useAppSelector((state) => state.auth);
  
  const [assessmentStats, setAssessmentStats] = useState<AssessmentStats[]>([]);
  const [questionAnalytics, setQuestionAnalytics] = useState<QuestionAnalytics[]>([]);
  const [performanceTrends, setPerformanceTrends] = useState<PerformanceTrend[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAssessment, setSelectedAssessment] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('30');
  const [assessments, setAssessments] = useState<any[]>([]);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    if (token) {
      fetchAnalyticsData();
    }
  }, [token, selectedAssessment, dateRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      apiService.setToken(token!);

      const params = new URLSearchParams();
      if (selectedAssessment !== 'all') {
        params.append('assessment', selectedAssessment);
      }
      params.append('days', dateRange);

      const [statsResponse, questionsResponse, trendsResponse, assessmentsResponse] = await Promise.all([
        apiService.request(`/assessments/analytics/stats/?${params.toString()}`),
        apiService.request(`/assessments/analytics/questions/?${params.toString()}`),
        apiService.request(`/assessments/analytics/trends/?${params.toString()}`),
        apiService.request('/assessments/assessments/')
      ]);

      setAssessmentStats(statsResponse.results || statsResponse);
      setQuestionAnalytics(questionsResponse.results || questionsResponse);
      setPerformanceTrends(trendsResponse.results || trendsResponse);
      setAssessments(assessmentsResponse.results || assessmentsResponse);
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.analyticsError')
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleExportAnalytics = () => {
    // Export analytics data
    console.log('Export analytics');
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'hard': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Process data for charts
  const processScoreDistribution = () => {
    const ranges = ['0-20', '21-40', '41-60', '61-80', '81-100'];
    const distribution = ranges.map(range => ({
      name: `${range}%`,
      value: Math.floor(Math.random() * 50) + 10, // Mock data
      color: ['#EF4444', '#F59E0B', '#3B82F6', '#10B981', '#059669'][ranges.indexOf(range)]
    }));
    return distribution;
  };

  const processPerformanceTrends = () => {
    return performanceTrends.map(trend => ({
      name: new Date(trend.date).toLocaleDateString(),
      score: trend.average_score,
      completion: trend.completion_rate
    }));
  };

  const processQuestionDifficulty = () => {
    return questionAnalytics.map(q => ({
      name: q.question_text.substring(0, 30) + '...',
      difficulty: 100 - q.correct_percentage,
      discrimination: q.discrimination_index * 100
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('assessments.analytics')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('assessments.analyticsDescription')}
            </p>
          </div>
          
          <button
            onClick={handleExportAnalytics}
            className="btn btn-primary flex items-center gap-2"
          >
            <Download className="w-5 h-5" />
            {t('assessments.exportAnalytics')}
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="glass-card">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.assessment')}
            </label>
            <select
              value={selectedAssessment}
              onChange={(e) => setSelectedAssessment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">{t('assessments.allAssessments')}</option>
              {assessments.map((assessment) => (
                <option key={assessment.id} value={assessment.id}>
                  {assessment.title}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.dateRange')}
            </label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="7">{t('assessments.last7Days')}</option>
              <option value="30">{t('assessments.last30Days')}</option>
              <option value="90">{t('assessments.last90Days')}</option>
              <option value="365">{t('assessments.lastYear')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-500 text-white">
              <FileText className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.totalAssessments')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {assessmentStats.length}
              </p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-500 text-white">
              <Users className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.totalAttempts')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {assessmentStats.reduce((sum, stat) => sum + stat.total_attempts, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-500 text-white">
              <TrendingUp className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.averageScore')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {assessmentStats.length > 0 
                  ? (assessmentStats.reduce((sum, stat) => sum + stat.average_score, 0) / assessmentStats.length).toFixed(1)
                  : '0'
                }%
              </p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-orange-500 text-white">
              <Target className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.averagePassRate')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {assessmentStats.length > 0 
                  ? (assessmentStats.reduce((sum, stat) => sum + stat.pass_rate, 0) / assessmentStats.length).toFixed(1)
                  : '0'
                }%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="glass-card">
          <PieChart
            data={processScoreDistribution()}
            title={t('assessments.scoreDistribution')}
            height={300}
          />
        </div>

        <div className="glass-card">
          <LineChart
            data={processPerformanceTrends()}
            title={t('assessments.performanceTrends')}
            height={300}
            xKey="name"
            yKey="score"
            color="#3B82F6"
          />
        </div>

        <div className="glass-card lg:col-span-2">
          <BarChart
            data={processQuestionDifficulty()}
            title={t('assessments.questionDifficulty')}
            height={300}
            xKey="name"
            yKey="difficulty"
            color="#8B5CF6"
          />
        </div>
      </div>

      {/* Assessment Performance Table */}
      <div className="glass-card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('assessments.assessmentPerformance')}
          </h2>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  {t('assessments.assessment')}
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  {t('assessments.attempts')}
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  {t('assessments.avgScore')}
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  {t('assessments.passRate')}
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  {t('assessments.avgTime')}
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  {t('assessments.difficulty')}
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  {t('common.actions')}
                </th>
              </tr>
            </thead>
            <tbody>
              {assessmentStats.map((stat) => (
                <tr key={stat.assessment_id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {stat.assessment_title}
                      </p>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                    {stat.total_attempts}
                  </td>
                  <td className="py-3 px-4">
                    <span className={`font-medium ${getPerformanceColor(stat.average_score)}`}>
                      {stat.average_score.toFixed(1)}%
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`font-medium ${getPerformanceColor(stat.pass_rate)}`}>
                      {stat.pass_rate.toFixed(1)}%
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                    {stat.average_time_minutes.toFixed(0)}m
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      stat.difficulty_index > 0.7 ? 'text-red-600 bg-red-100' :
                      stat.difficulty_index > 0.4 ? 'text-yellow-600 bg-yellow-100' :
                      'text-green-600 bg-green-100'
                    }`}>
                      {stat.difficulty_index > 0.7 ? t('assessments.hard') :
                       stat.difficulty_index > 0.4 ? t('assessments.medium') :
                       t('assessments.easy')}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <button
                      onClick={() => console.log('View details:', stat.assessment_id)}
                      className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      title={t('assessments.viewDetails')}
                    >
                      <Eye className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Question Analytics */}
      <div className="glass-card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('assessments.questionAnalytics')}
          </h2>
        </div>

        <div className="space-y-4">
          {questionAnalytics.slice(0, 10).map((question) => (
            <div
              key={question.question_id}
              className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                    {question.question_text}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                    <span>{t(`assessments.types.${question.question_type}`)}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.difficulty_level)}`}>
                      {t(`assessments.${question.difficulty_level}`)}
                    </span>
                  </div>
                </div>

                <div className="text-right">
                  <div className={`text-lg font-bold ${getPerformanceColor(question.correct_percentage)}`}>
                    {question.correct_percentage.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('assessments.correctRate')}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    {t('assessments.avgTime')}:
                  </span>
                  <span className="ml-1 text-gray-900 dark:text-white">
                    {(question.average_time_seconds / 60).toFixed(1)}m
                  </span>
                </div>

                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    {t('assessments.discrimination')}:
                  </span>
                  <span className="ml-1 text-gray-900 dark:text-white">
                    {question.discrimination_index.toFixed(2)}
                  </span>
                </div>

                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    {t('assessments.responses')}:
                  </span>
                  <span className="ml-1 text-gray-900 dark:text-white">
                    {Object.values(question.response_distribution).reduce((a, b) => a + b, 0)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AssessmentAnalytics;
