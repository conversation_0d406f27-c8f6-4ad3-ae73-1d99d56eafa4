import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Clock,
  GraduationCap,
  CheckCircle,
  Plus,
  Trash2
} from 'lucide-react';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import QuestionSelector from './QuestionSelector';

const assessmentSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  title_ar: z.string().max(200, 'Arabic title too long').optional(),
  description: z.string().optional(),
  description_ar: z.string().optional(),
  instructions: z.string().optional(),
  instructions_ar: z.string().optional(),
  course: z.string().min(1, 'Course is required'),
  assessment_type: z.enum(['quiz', 'exam', 'test', 'practice', 'survey']),
  total_points: z.number().min(0.1, 'Total points must be greater than 0'),
  passing_score: z.number().min(0).max(100, 'Passing score must be between 0 and 100'),
  time_limit_minutes: z.number().min(1).optional().nullable(),
  available_from: z.string().min(1, 'Start date is required'),
  available_until: z.string().min(1, 'End date is required'),
  max_attempts: z.number().min(1, 'Max attempts must be at least 1'),
  allow_review: z.boolean().default(true),
  show_correct_answers: z.boolean().default(false),
  show_score_immediately: z.boolean().default(true),
  randomize_questions: z.boolean().default(false),
  questions_per_page: z.number().min(1, 'Questions per page must be at least 1').default(1)
});

type AssessmentFormData = z.infer<typeof assessmentSchema>;

interface Course {
  id: string;
  title: string;
  code: string;
}

interface Question {
  id: string;
  question_text: string;
  question_type: string;
  points: number;
  difficulty_level: string;
}

interface AssessmentCreatorProps {
  onSubmit: (data: AssessmentFormData & { questions: string[] }) => Promise<void>;
  onCancel: () => void;
  courseId?: string;
  initialData?: Partial<AssessmentFormData>;
}

const assessmentTypes = [
  { value: 'quiz', label: 'Quiz' },
  { value: 'exam', label: 'Exam' },
  { value: 'test', label: 'Test' },
  { value: 'practice', label: 'Practice' },
  { value: 'survey', label: 'Survey' }
];

const AssessmentCreator: React.FC<AssessmentCreatorProps> = ({
  onSubmit,
  onCancel,
  courseId,
  initialData
}) => {
  const { t } = useTranslation();
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showQuestionSelector, setShowQuestionSelector] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<AssessmentFormData>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      course: courseId || '',
      assessment_type: 'quiz',
      total_points: 100,
      passing_score: 60,
      max_attempts: 1,
      allow_review: true,
      show_correct_answers: false,
      show_score_immediately: true,
      randomize_questions: false,
      questions_per_page: 1,
      ...initialData
    }
  });

  const selectedCourse = watch('course');

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    if (courseId) {
      setValue('course', courseId);
    }
  }, [courseId, setValue]);

  useEffect(() => {
    // Auto-calculate total points when questions change
    const totalPoints = selectedQuestions.reduce((sum, q) => sum + q.points, 0);
    setValue('total_points', totalPoints);
  }, [selectedQuestions, setValue]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await apiService.request('/courses/');
      setCourses(response.results || response);
    } catch (error) {
      console.error('Failed to fetch courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = async (data: AssessmentFormData) => {
    try {
      setSubmitting(true);
      
      if (selectedQuestions.length === 0) {
        alert(t('assessments.noQuestionsSelected', 'Please select at least one question'));
        return;
      }

      const questionIds = selectedQuestions.map(q => q.id);
      await onSubmit({ ...data, questions: questionIds });
    } catch (error) {
      console.error('Failed to submit assessment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddQuestions = (questions: Question[]) => {
    const newQuestions = questions.filter(
      q => !selectedQuestions.find(sq => sq.id === q.id)
    );
    setSelectedQuestions([...selectedQuestions, ...newQuestions]);
    setShowQuestionSelector(false);
  };

  const handleRemoveQuestion = (questionId: string) => {
    setSelectedQuestions(selectedQuestions.filter(q => q.id !== questionId));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" text={t('common.loading', 'Loading...')} />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="glass-card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('assessments.basicInformation', 'Basic Information')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Title */}
            <div className="md:col-span-2">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.title', 'Title')} *
              </label>
              <input
                type="text"
                id="title"
                {...register('title')}
                className="input-field"
                placeholder={t('assessments.titlePlaceholder', 'Enter assessment title')}
              />
              {errors.title && (
                <p className="text-red-600 text-sm mt-1">{errors.title.message}</p>
              )}
            </div>

            {/* Arabic Title */}
            <div className="md:col-span-2">
              <label htmlFor="title_ar" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.titleArabic', 'Title (Arabic)')}
              </label>
              <input
                type="text"
                id="title_ar"
                {...register('title_ar')}
                className="input-field"
                placeholder={t('assessments.titleArabicPlaceholder', 'Enter Arabic title')}
                dir="rtl"
              />
            </div>

            {/* Course */}
            <div>
              <label htmlFor="course" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.course', 'Course')} *
              </label>
              <select
                id="course"
                {...register('course')}
                className="input-field"
                disabled={!!courseId}
              >
                <option value="">{t('assessments.selectCourse', 'Select a course')}</option>
                {courses.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.code} - {course.title}
                  </option>
                ))}
              </select>
              {errors.course && (
                <p className="text-red-600 text-sm mt-1">{errors.course.message}</p>
              )}
            </div>

            {/* Assessment Type */}
            <div>
              <label htmlFor="assessment_type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.type', 'Type')} *
              </label>
              <select
                id="assessment_type"
                {...register('assessment_type')}
                className="input-field"
              >
                {assessmentTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {t(`assessments.${type.value}`, type.label)}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Description */}
          <div className="mt-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.description', 'Description')}
            </label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="input-field"
              placeholder={t('assessments.descriptionPlaceholder', 'Enter description (optional)')}
            />
          </div>

          {/* Instructions */}
          <div className="mt-4">
            <label htmlFor="instructions" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.instructions', 'Instructions')}
            </label>
            <textarea
              id="instructions"
              {...register('instructions')}
              rows={3}
              className="input-field"
              placeholder={t('assessments.instructionsPlaceholder', 'Enter instructions for students (optional)')}
            />
          </div>
        </div>

        {/* Scoring and Timing */}
        <div className="glass-card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('assessments.scoringAndTiming', 'Scoring & Timing')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="total_points" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.totalPoints', 'Total Points')} *
              </label>
              <input
                type="number"
                id="total_points"
                {...register('total_points', { valueAsNumber: true })}
                step="0.1"
                min="0.1"
                className="input-field"
                readOnly
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {t('assessments.autoCalculated', 'Auto-calculated from selected questions')}
              </p>
            </div>

            <div>
              <label htmlFor="passing_score" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.passingScore', 'Passing Score (%)')} *
              </label>
              <input
                type="number"
                id="passing_score"
                {...register('passing_score', { valueAsNumber: true })}
                min="0"
                max="100"
                className="input-field"
              />
            </div>

            <div>
              <label htmlFor="time_limit_minutes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.timeLimit', 'Time Limit (minutes)')}
              </label>
              <input
                type="number"
                id="time_limit_minutes"
                {...register('time_limit_minutes', { valueAsNumber: true })}
                min="1"
                className="input-field"
                placeholder={t('assessments.noTimeLimit', 'No limit')}
              />
            </div>
          </div>
        </div>

        {/* Availability */}
        <div className="glass-card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('assessments.availability', 'Availability')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="available_from" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.availableFrom', 'Available From')} *
              </label>
              <input
                type="datetime-local"
                id="available_from"
                {...register('available_from')}
                className="input-field"
              />
            </div>

            <div>
              <label htmlFor="available_until" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.availableUntil', 'Available Until')} *
              </label>
              <input
                type="datetime-local"
                id="available_until"
                {...register('available_until')}
                className="input-field"
              />
            </div>

            <div>
              <label htmlFor="max_attempts" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.maxAttempts', 'Max Attempts')} *
              </label>
              <input
                type="number"
                id="max_attempts"
                {...register('max_attempts', { valueAsNumber: true })}
                min="1"
                className="input-field"
              />
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className="glass-card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('assessments.settings', 'Settings')}
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="allow_review"
                {...register('allow_review')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="allow_review" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                {t('assessments.allowReview', 'Allow students to review their answers')}
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="show_correct_answers"
                {...register('show_correct_answers')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="show_correct_answers" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                {t('assessments.showCorrectAnswers', 'Show correct answers after submission')}
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="show_score_immediately"
                {...register('show_score_immediately')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="show_score_immediately" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                {t('assessments.showScoreImmediately', 'Show score immediately after submission')}
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="randomize_questions"
                {...register('randomize_questions')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="randomize_questions" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                {t('assessments.randomizeQuestions', 'Randomize question order for each student')}
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={submitting}
          >
            {t('common.cancel', 'Cancel')}
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={submitting || selectedQuestions.length === 0}
          >
            {submitting ? (
              <div className="flex items-center gap-2">
                <LoadingSpinner size="sm" />
                {t('common.creating', 'Creating...')}
              </div>
            ) : (
              t('assessments.createAssessment', 'Create Assessment')
            )}
          </button>
        </div>
      </form>

      {/* Questions Section */}
      <div className="glass-card p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('assessments.questions', 'Questions')} ({selectedQuestions.length})
          </h3>
          <button
            type="button"
            onClick={() => setShowQuestionSelector(true)}
            className="btn btn-primary flex items-center gap-2"
            disabled={!selectedCourse}
          >
            <Plus className="w-5 h-5" />
            {t('assessments.addQuestions', 'Add Questions')}
          </button>
        </div>

        {selectedQuestions.length === 0 ? (
          <div className="text-center py-8">
            <GraduationCap className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              {t('assessments.noQuestionsAdded', 'No questions added yet')}
            </p>
            {!selectedCourse && (
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                {t('assessments.selectCourseFirst', 'Select a course first to add questions')}
              </p>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {selectedQuestions.map((question, index) => (
              <div key={question.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Q{index + 1}
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {question.question_text.substring(0, 100)}
                      {question.question_text.length > 100 ? '...' : ''}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{t(`assessments.${question.question_type}`, question.question_type)}</span>
                    <span>{question.points} {t('assessments.points', 'points')}</span>
                    <span>{t(`assessments.${question.difficulty_level}`, question.difficulty_level)}</span>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => handleRemoveQuestion(question.id)}
                  className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 ml-4"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Question Selector Modal */}
      {showQuestionSelector && (
        <QuestionSelector
          courseId={selectedCourse}
          onSelect={handleAddQuestions}
          onClose={() => setShowQuestionSelector(false)}
          excludeQuestionIds={selectedQuestions.map(q => q.id)}
        />
      )}
    </div>
  );
};

export default AssessmentCreator;
