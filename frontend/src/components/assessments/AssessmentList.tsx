import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Play, 
  Clock, 
  FileText, 
  Users, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  MoreVertical
} from 'lucide-react';
import { useAppSelector } from '../../store';
import apiService from '../../services/api';

interface Assessment {
  id: string;
  title: string;
  title_ar: string;
  description: string;
  description_ar: string;
  course_name: string;
  course_code: string;
  total_points: number;
  passing_score: number;
  time_limit_minutes: number | null;
  max_attempts: number;
  questions_count: number;
  status: 'draft' | 'published' | 'archived';
  available_from: string | null;
  available_until: string | null;
  created_at: string;
  created_by_name: string;
  attempts_count?: number;
  user_attempts?: Array<{
    id: string;
    attempt_number: number;
    status: string;
    percentage_score: number | null;
    submitted_at: string | null;
  }>;
}

interface AssessmentListProps {
  courseId?: string;
  showCreateButton?: boolean;
  className?: string;
}

const AssessmentList: React.FC<AssessmentListProps> = ({
  courseId,
  showCreateButton = false,
  className = ''
}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  const isRTL = i18n.language === 'ar';
  
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'available' | 'completed' | 'draft'>('all');

  // Fetch assessments
  const fetchAssessments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (courseId) {
        params.append('course_id', courseId);
      }
      
      if (filter !== 'all') {
        params.append('status', filter);
      }

      const data = await apiService.request(`/assessments/assessments/?${params}`, {
        method: 'GET'
      });

      setAssessments(data.results || data);
    } catch (err) {
      setError(err instanceof Error ? err.message : t('assessments.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssessments();
  }, [courseId, filter]);

  // Check if assessment is available
  const isAssessmentAvailable = (assessment: Assessment) => {
    const now = new Date();
    const availableFrom = assessment.available_from ? new Date(assessment.available_from) : null;
    const availableUntil = assessment.available_until ? new Date(assessment.available_until) : null;
    
    if (availableFrom && now < availableFrom) return false;
    if (availableUntil && now > availableUntil) return false;
    
    return assessment.status === 'published';
  };

  // Get user's best attempt
  const getUserBestAttempt = (assessment: Assessment) => {
    if (!assessment.user_attempts || assessment.user_attempts.length === 0) {
      return null;
    }
    
    return assessment.user_attempts.reduce((best, current) => {
      if (!best) return current;
      if (current.percentage_score === null) return best;
      if (best.percentage_score === null) return current;
      return current.percentage_score > best.percentage_score ? current : best;
    });
  };

  // Check if user can take assessment
  const canTakeAssessment = (assessment: Assessment) => {
    if (!isAssessmentAvailable(assessment)) return false;
    if (user?.role !== 'student') return false;
    
    const attemptCount = assessment.user_attempts?.length || 0;
    return attemptCount < assessment.max_attempts;
  };

  // Handle take assessment
  const handleTakeAssessment = (assessmentId: string) => {
    navigate(`/app/assessments/${assessmentId}/take`);
  };

  // Handle edit assessment
  const handleEditAssessment = (assessmentId: string) => {
    navigate(`/app/assessments/${assessmentId}/edit`);
  };

  // Handle view results
  const handleViewResults = (assessmentId: string) => {
    navigate(`/app/assessments/${assessmentId}/results`);
  };

  // Get status badge
  const getStatusBadge = (assessment: Assessment) => {
    const bestAttempt = getUserBestAttempt(assessment);
    
    if (user?.role === 'student') {
      if (!isAssessmentAvailable(assessment)) {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
            {t('assessments.notAvailable')}
          </span>
        );
      }
      
      if (bestAttempt) {
        const passed = bestAttempt.percentage_score !== null && bestAttempt.percentage_score >= assessment.passing_score;
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            passed 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
          }`}>
            {passed ? t('assessments.passed') : t('assessments.failed')}
            {bestAttempt.percentage_score !== null && ` (${Math.round(bestAttempt.percentage_score)}%)`}
          </span>
        );
      }
      
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
          {t('assessments.available')}
        </span>
      );
    }
    
    // For teachers/admins
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
      published: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      archived: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[assessment.status]}`}>
        {t(`assessments.status.${assessment.status}`)}
      </span>
    );
  };

  // Render assessment card
  const renderAssessmentCard = (assessment: Assessment) => {
    const bestAttempt = getUserBestAttempt(assessment);
    const canTake = canTakeAssessment(assessment);
    const isTeacher = user?.role === 'teacher' || user?.role === 'admin' || user?.role === 'super_admin';

    return (
      <div key={assessment.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 truncate">
              {isRTL ? assessment.title_ar || assessment.title : assessment.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {assessment.course_code} - {assessment.course_name}
            </p>
            {(assessment.description || assessment.description_ar) && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                {isRTL ? assessment.description_ar || assessment.description : assessment.description}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2 ml-4">
            {getStatusBadge(assessment)}
            {isTeacher && (
              <div className="relative">
                <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <MoreVertical className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Assessment Details */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <FileText className="w-4 h-4 mr-2" />
            {assessment.questions_count} {t('assessments.questions')}
          </div>
          
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Clock className="w-4 h-4 mr-2" />
            {assessment.time_limit_minutes ? `${assessment.time_limit_minutes} ${t('assessments.minutes')}` : t('assessments.noTimeLimit')}
          </div>
          
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Users className="w-4 h-4 mr-2" />
            {assessment.total_points} {t('assessments.points')}
          </div>
          
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Calendar className="w-4 h-4 mr-2" />
            {new Date(assessment.created_at).toLocaleDateString()}
          </div>
        </div>

        {/* Student Progress */}
        {user?.role === 'student' && assessment.user_attempts && assessment.user_attempts.length > 0 && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {t('assessments.attempts')}: {assessment.user_attempts.length} / {assessment.max_attempts}
              </span>
              {bestAttempt && bestAttempt.percentage_score !== null && (
                <span className="font-medium text-gray-900 dark:text-white">
                  {t('assessments.bestScore')}: {Math.round(bestAttempt.percentage_score)}%
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {t('assessments.createdBy')} {assessment.created_by_name}
          </div>
          
          <div className="flex items-center space-x-2">
            {user?.role === 'student' && canTake && (
              <button
                onClick={() => handleTakeAssessment(assessment.id)}
                className="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
              >
                <Play className="w-4 h-4 mr-1" />
                {t('assessments.takeAssessment')}
              </button>
            )}
            
            {user?.role === 'student' && bestAttempt && (
              <button
                onClick={() => handleViewResults(assessment.id)}
                className="inline-flex items-center px-3 py-1.5 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors"
              >
                <Eye className="w-4 h-4 mr-1" />
                {t('assessments.viewResults')}
              </button>
            )}
            
            {isTeacher && (
              <>
                <button
                  onClick={() => handleEditAssessment(assessment.id)}
                  className="inline-flex items-center px-3 py-1.5 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  {t('common.edit')}
                </button>
                
                <button
                  onClick={() => handleViewResults(assessment.id)}
                  className="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  {t('assessments.viewResults')}
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchAssessments}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          {t('common.retry')}
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          {t('assessments.title')}
        </h2>
        
        {showCreateButton && (
          <button
            onClick={() => navigate('/app/assessments/create')}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <FileText className="w-4 h-4 mr-2" />
            {t('assessments.createAssessment')}
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4 mb-6">
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value as any)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">{t('assessments.allAssessments')}</option>
          {user?.role === 'student' ? (
            <>
              <option value="available">{t('assessments.available')}</option>
              <option value="completed">{t('assessments.completed')}</option>
            </>
          ) : (
            <>
              <option value="draft">{t('assessments.status.draft')}</option>
              <option value="published">{t('assessments.status.published')}</option>
              <option value="archived">{t('assessments.status.archived')}</option>
            </>
          )}
        </select>
      </div>

      {/* Assessment List */}
      {assessments.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('assessments.noAssessments')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('assessments.noAssessmentsDesc')}
          </p>
          {showCreateButton && (
            <button
              onClick={() => navigate('/app/assessments/create')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <FileText className="w-4 h-4 mr-2" />
              {t('assessments.createFirstAssessment')}
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {assessments.map(renderAssessmentCard)}
        </div>
      )}
    </div>
  );
};

export default AssessmentList;
