AssessmentTaking.tsx:461 Uncaught TypeError: Cannot read properties of undefined (reading 'length')
    at AssessmentTaking (AssessmentTaking.tsx:461:41)
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';
import {
  Clock,
  ChevronLeft,
  ChevronRight,
  Flag,
  AlertTriangle,
  CheckCircle,
  Save,
  Send,

} from 'lucide-react';

import apiService from '../../services/api';

interface Question {
  id: string;
  question_text: string;
  question_text_ar: string;
  question_type: 'multiple_choice' | 'true_false' | 'short_answer' | 'essay' | 'fill_blank' | 'matching' | 'ordering';
  points: number;
  question_data: any;
  explanation?: string;
  order_index: number;
}

interface Assessment {
  id: string;
  title: string;
  title_ar: string;
  description: string;
  description_ar: string;
  instructions: string;
  instructions_ar: string;
  total_points: number;
  passing_score: number;
  time_limit_minutes: number | null;
  max_attempts: number;
  allow_review: boolean;
  show_correct_answers: boolean;
  show_score_immediately: boolean;
  randomize_questions: boolean;
  questions: Question[];
  course_name: string;
  course_code: string;
}

interface AssessmentAttempt {
  id: string;
  attempt_number: number;
  started_at: string;
  submitted_at: string | null;
  time_remaining: number;
  answers: Record<string, any>;
  is_submitted: boolean;
  score: number | null;
}

interface AssessmentTakingProps {
  assessmentId: string;
  onComplete?: (attempt: AssessmentAttempt) => void;
  onExit?: () => void;
}

const AssessmentTaking: React.FC<AssessmentTakingProps> = ({
  assessmentId,
  onComplete
}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { token } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  const isRTL = i18n.language === 'ar';

  // State management
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [attempt, setAttempt] = useState<AssessmentAttempt | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'saved' | 'saving' | 'error'>('saved');
  const [showInstructions, setShowInstructions] = useState(true);

  // Refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const autoSaveRef = useRef<NodeJS.Timeout | null>(null);
  const lastSaveRef = useRef<number>(Date.now());

  // Fetch assessment data
  const fetchAssessment = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Set token for API service
      if (token) {
        apiService.setToken(token);
      }

      const data = await apiService.request<Assessment>(`/assessments/assessments/${assessmentId}/`);
      console.log('Assessment data received:', data);
      setAssessment(data);
      console.log('Assessment state set');
    } catch (err) {
      console.error('Failed to fetch assessment:', err);
      const errorMessage = err instanceof Error ? err.message : t('assessments.fetchError');
      setError(errorMessage);

      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: errorMessage
      }));
    } finally {
      console.log('Setting loading to false');
      setLoading(false);
    }
  }, [assessmentId, t, token, dispatch]);

  // Check for existing attempt
  const checkExistingAttempt = useCallback(async () => {
    try {
      if (!token) return;

      apiService.setToken(token);
      const attemptData = await apiService.request<AssessmentAttempt>(`/assessments/assessments/${assessmentId}/attempt/`);
      setAttempt(attemptData);
      setAnswers(attemptData.answers || {});
      setTimeRemaining(attemptData.time_remaining);
      setShowInstructions(false); // Skip instructions if resuming
    } catch (err) {
      // 404 or "No active attempt found" is expected when no existing attempt exists - this is normal
      if (err instanceof Error && (err.message.includes('404') || err.message.includes('No active attempt found'))) {
        console.log('No existing attempt found - will show instructions');
      } else {
        console.error('Error checking existing attempt:', err);
      }
    }
  }, [assessmentId, token]);

  // Start new attempt
  const startAttempt = async () => {
    try {
      if (!token) {
        throw new Error(t('assessments.authRequired'));
      }

      apiService.setToken(token);
      const attemptData = await apiService.request<AssessmentAttempt>(`/assessments/assessments/${assessmentId}/start/`, {
        method: 'POST'
      });

      setAttempt(attemptData);
      setTimeRemaining(attemptData.time_remaining);
      setShowInstructions(false);

      // Start timer if time limit exists
      if (assessment?.time_limit_minutes) {
        startTimer();
      }
    } catch (err) {
      console.error('Failed to start assessment:', err);
      const errorMessage = err instanceof Error ? err.message : t('assessments.startError');
      setError(errorMessage);

      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: errorMessage
      }));
    }
  };

  // Timer management
  const startTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev === null || prev <= 0) {
          // Time's up - auto submit
          handleSubmit(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, []);

  // Auto-save functionality
  const autoSave = useCallback(async () => {
    if (!attempt || Object.keys(answers).length === 0 || !token) return;

    try {
      setAutoSaveStatus('saving');
      apiService.setToken(token);

      await apiService.request(`/assessments/attempts/${attempt.id}/save/`, {
        method: 'POST',
        body: JSON.stringify({ answers })
      });

      setAutoSaveStatus('saved');
      lastSaveRef.current = Date.now();
    } catch (err) {
      setAutoSaveStatus('error');
      console.error('Auto-save failed:', err);
    }
  }, [attempt, answers, token]);

  // Schedule auto-save
  const scheduleAutoSave = useCallback(() => {
    if (autoSaveRef.current) {
      clearTimeout(autoSaveRef.current);
    }

    autoSaveRef.current = setTimeout(() => {
      autoSave();
    }, 5000); // Auto-save every 5 seconds
  }, [autoSave]);

  // Handle answer change
  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
    scheduleAutoSave();
  };

  // Navigation functions
  const goToQuestion = (index: number) => {
    if (index >= 0 && index < (assessment?.questions.length || 0)) {
      setCurrentQuestionIndex(index);
    }
  };

  const goToPreviousQuestion = () => {
    goToQuestion(currentQuestionIndex - 1);
  };

  const goToNextQuestion = () => {
    goToQuestion(currentQuestionIndex + 1);
  };

  // Flag/unflag question
  const toggleQuestionFlag = (questionId: string) => {
    setFlaggedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionId)) {
        newSet.delete(questionId);
      } else {
        newSet.add(questionId);
      }
      return newSet;
    });
  };

  // Submit assessment
  const handleSubmit = async (autoSubmit = false) => {
    if (!attempt || !token) return;

    try {
      setIsSubmitting(true);

      // Final save before submission
      await autoSave();

      apiService.setToken(token);
      const result = await apiService.request<AssessmentAttempt>(`/assessments/attempts/${attempt.id}/submit/`, {
        method: 'POST',
        body: JSON.stringify({
          answers,
          auto_submit: autoSubmit
        })
      });

      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // Update attempt with results
      setAttempt(prev => prev ? { ...prev, ...result } : null);

      // Call completion callback
      onComplete?.(result);

    } catch (err) {
      setError(err instanceof Error ? err.message : t('assessments.submitError'));
    } finally {
      setIsSubmitting(false);
      setShowConfirmSubmit(false);
    }
  };

  // Format time remaining
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Get question completion status
  const getQuestionStatus = (questionId: string) => {
    const hasAnswer = answers[questionId] !== undefined && answers[questionId] !== null && answers[questionId] !== '';
    const isFlagged = flaggedQuestions.has(questionId);

    if (hasAnswer && isFlagged) return 'answered-flagged';
    if (hasAnswer) return 'answered';
    if (isFlagged) return 'flagged';
    return 'unanswered';
  };

  // Calculate progress
  const getProgress = () => {
    if (!assessment) return 0;
    const answeredCount = assessment.questions.filter(q =>
      answers[q.id] !== undefined && answers[q.id] !== null && answers[q.id] !== ''
    ).length;
    return (answeredCount / assessment.questions.length) * 100;
  };

  // Effects
  useEffect(() => {
    console.log('useEffect triggered:', { assessmentId, hasToken: !!token });
    if (assessmentId && token) {
      console.log('Calling fetchAssessment');
      fetchAssessment();
    } else {
      console.log('Conditions not met for fetchAssessment');
    }

    // Cleanup on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }
    };
  }, [assessmentId, token, fetchAssessment]);

  // Separate effect for checking existing attempts after assessment is loaded
  useEffect(() => {
    if (assessment && token && !attempt) {
      checkExistingAttempt();
    }
  }, [assessment, token, attempt, checkExistingAttempt]);

  // Start timer when attempt is loaded
  useEffect(() => {
    if (attempt && assessment?.time_limit_minutes && timeRemaining !== null) {
      startTimer();
    }
  }, [attempt, assessment, timeRemaining, startTimer]);

  // Prevent page refresh/navigation
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (attempt && !attempt.is_submitted) {
        e.preventDefault();
        e.returnValue = t('assessments.confirmLeave');
        return t('assessments.confirmLeave');
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [attempt, t]);

  if (loading && !assessment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading assessment...</p>
          <p className="text-sm text-gray-500 mt-2">
            Debug: assessmentId={assessmentId}, hasToken={!!token}, hasAssessment={!!assessment}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('common.error')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => navigate('/app/assessments')}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            {t('common.goBack')}
          </button>
        </div>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('assessments.notFound')}
          </h2>
          <button
            onClick={() => navigate('/app/assessments')}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            {t('common.goBack')}
          </button>
        </div>
      </div>
    );
  }

  // Show instructions before starting
  if (showInstructions && !attempt) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {isRTL ? assessment.title_ar || assessment.title : assessment.title}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {assessment.course_code} - {assessment.course_name}
              </p>
            </div>

            {/* Assessment Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {assessment.questions.length}
                </div>
                <div className="text-sm text-blue-600 dark:text-blue-400">
                  {t('assessments.questions')}
                </div>
              </div>

              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {assessment.total_points}
                </div>
                <div className="text-sm text-green-600 dark:text-green-400">
                  {t('assessments.totalPoints')}
                </div>
              </div>

              <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {assessment.time_limit_minutes ? `${assessment.time_limit_minutes} ${t('assessments.minutes')}` : t('assessments.noTimeLimit')}
                </div>
                <div className="text-sm text-orange-600 dark:text-orange-400">
                  {t('assessments.timeLimit')}
                </div>
              </div>
            </div>

            {/* Instructions */}
            {(assessment.instructions || assessment.instructions_ar) && (
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {t('assessments.instructions')}
                </h3>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="text-gray-600 dark:text-gray-400">
                    {isRTL ? assessment.instructions_ar || assessment.instructions : assessment.instructions}
                  </p>
                </div>
              </div>
            )}

            {/* Important Notes */}
            <div className="mb-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                {t('assessments.importantNotes')}
              </h4>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• {t('assessments.note1')}</li>
                <li>• {t('assessments.note2')}</li>
                {assessment.time_limit_minutes && (
                  <li>• {t('assessments.note3', { minutes: assessment.time_limit_minutes })}</li>
                )}
                <li>• {t('assessments.note4')}</li>
              </ul>
            </div>

            {/* Start Button */}
            <div className="text-center">
              <button
                onClick={startAttempt}
                className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              >
                {t('assessments.startAssessment')}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show assessment completion
  if (attempt?.is_submitted) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {t('assessments.completed')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {t('assessments.submittedSuccessfully')}
            </p>

            {assessment.show_score_immediately && attempt.score !== null && (
              <div className="mb-6">
                <div className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {attempt.score}%
                </div>
                <div className="text-gray-600 dark:text-gray-400">
                  {attempt.score >= assessment.passing_score ? t('assessments.passed') : t('assessments.failed')}
                </div>
              </div>
            )}

            <button
              onClick={() => navigate('/app/assessments')}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              {t('assessments.backToAssessments')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  const currentQuestion = assessment.questions[currentQuestionIndex];
  const progress = getProgress();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header with timer and progress */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {isRTL ? assessment.title_ar || assessment.title : assessment.title}
              </h1>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {t('assessments.question')} {currentQuestionIndex + 1} {t('common.of')} {assessment.questions.length}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Auto-save status */}
              <div className="flex items-center space-x-2 text-sm">
                <Save className={`w-4 h-4 ${
                  autoSaveStatus === 'saved' ? 'text-green-500' :
                  autoSaveStatus === 'saving' ? 'text-blue-500' : 'text-red-500'
                }`} />
                <span className={`${
                  autoSaveStatus === 'saved' ? 'text-green-600 dark:text-green-400' :
                  autoSaveStatus === 'saving' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400'
                }`}>
                  {t(`assessments.${autoSaveStatus}`)}
                </span>
              </div>

              {/* Timer */}
              {timeRemaining !== null && (
                <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
                  timeRemaining < 300 ? 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400' :
                  timeRemaining < 900 ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400' :
                  'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                }`}>
                  <Clock className="w-4 h-4" />
                  <span className="font-mono font-medium">
                    {formatTime(timeRemaining)}
                  </span>
                </div>
              )}

              {/* Submit button */}
              <button
                onClick={() => setShowConfirmSubmit(true)}
                disabled={isSubmitting}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Send className="w-4 h-4 mr-2" />
                {t('assessments.submit')}
              </button>
            </div>
          </div>

          {/* Progress bar */}
          <div className="pb-4">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {Math.round(progress)}% {t('assessments.completed')}
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Question navigation sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sticky top-32">
              <h3 className="font-medium text-gray-900 dark:text-white mb-4">
                {t('assessments.questionNavigation')}
              </h3>

              <div className="grid grid-cols-5 lg:grid-cols-4 gap-2">
                {assessment.questions.map((question, index) => {
                  const status = getQuestionStatus(question.id);
                  const isCurrent = index === currentQuestionIndex;

                  return (
                    <button
                      key={question.id}
                      onClick={() => goToQuestion(index)}
                      className={`w-10 h-10 rounded-lg text-sm font-medium transition-colors relative ${
                        isCurrent
                          ? 'bg-blue-600 text-white'
                          : status === 'answered'
                          ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/40'
                          : status === 'flagged'
                          ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/40'
                          : status === 'answered-flagged'
                          ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 hover:bg-orange-200 dark:hover:bg-orange-900/40'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {index + 1}
                      {flaggedQuestions.has(question.id) && (
                        <Flag className="w-3 h-3 absolute -top-1 -right-1 text-red-500" />
                      )}
                    </button>
                  );
                })}
              </div>

              {/* Legend */}
              <div className="mt-4 space-y-2 text-xs">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-100 dark:bg-green-900/20 rounded"></div>
                  <span className="text-gray-600 dark:text-gray-400">{t('assessments.answered')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-100 dark:bg-yellow-900/20 rounded"></div>
                  <span className="text-gray-600 dark:text-gray-400">{t('assessments.flagged')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-gray-100 dark:bg-gray-700 rounded"></div>
                  <span className="text-gray-600 dark:text-gray-400">{t('assessments.unanswered')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Question content */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              {/* Question header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {t('assessments.question')} {currentQuestionIndex + 1}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {currentQuestion.points} {t('assessments.points')}
                  </span>
                </div>

                <button
                  onClick={() => toggleQuestionFlag(currentQuestion.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    flaggedQuestions.has(currentQuestion.id)
                      ? 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <Flag className="w-4 h-4" />
                </button>
              </div>

              {/* Question content will be rendered by QuestionRenderer component */}
              <QuestionRenderer
                question={currentQuestion}
                answer={answers[currentQuestion.id]}
                onAnswerChange={(answer) => handleAnswerChange(currentQuestion.id, answer)}
                isRTL={isRTL}
              />

              {/* Navigation buttons */}
              <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={goToPreviousQuestion}
                  disabled={currentQuestionIndex === 0}
                  className="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  {t('assessments.previous')}
                </button>

                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {currentQuestionIndex + 1} / {assessment.questions.length}
                </div>

                <button
                  onClick={goToNextQuestion}
                  disabled={currentQuestionIndex === assessment.questions.length - 1}
                  className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  {t('assessments.next')}
                  <ChevronRight className="w-4 h-4 ml-2" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation modal */}
      {showConfirmSubmit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('assessments.confirmSubmission')}
            </h3>

            <div className="mb-6">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {t('assessments.submissionWarning')}
              </p>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">{t('assessments.answered')}:</span>
                    <span className="font-medium text-gray-900 dark:text-white ml-2">
                      {assessment.questions.filter(q => answers[q.id] !== undefined).length}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">{t('assessments.unanswered')}:</span>
                    <span className="font-medium text-gray-900 dark:text-white ml-2">
                      {assessment.questions.filter(q => answers[q.id] === undefined).length}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-4">
              <button
                onClick={() => setShowConfirmSubmit(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={() => handleSubmit()}
                disabled={isSubmitting}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {isSubmitting ? t('assessments.submitting') : t('assessments.submitFinal')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Question Renderer Component
interface QuestionRendererProps {
  question: Question;
  answer: any;
  onAnswerChange: (answer: any) => void;
  isRTL: boolean;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  answer,
  onAnswerChange,
  isRTL
}) => {
  const { t } = useTranslation();

  const questionText = isRTL ? question.question_text_ar || question.question_text : question.question_text;

  const renderMultipleChoice = () => {
    const options = question.question_data.options || [];
    const selectedAnswers = Array.isArray(answer) ? answer : (answer !== undefined ? [answer] : []);

    return (
      <div className="space-y-3">
        {options.map((option: string, index: number) => (
          <label key={index} className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
            <input
              type="radio"
              name={`question-${question.id}`}
              value={index}
              checked={selectedAnswers.includes(index)}
              onChange={() => onAnswerChange(index)}
              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
            />
            <span className="text-gray-900 dark:text-white">{option}</span>
          </label>
        ))}
      </div>
    );
  };

  const renderTrueFalse = () => {
    return (
      <div className="space-y-3">
        <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
          <input
            type="radio"
            name={`question-${question.id}`}
            value="true"
            checked={answer === true}
            onChange={() => onAnswerChange(true)}
            className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
          />
          <span className="text-gray-900 dark:text-white">{t('assessments.true')}</span>
        </label>
        <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
          <input
            type="radio"
            name={`question-${question.id}`}
            value="false"
            checked={answer === false}
            onChange={() => onAnswerChange(false)}
            className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
          />
          <span className="text-gray-900 dark:text-white">{t('assessments.false')}</span>
        </label>
      </div>
    );
  };

  const renderShortAnswer = () => {
    return (
      <input
        type="text"
        value={answer || ''}
        onChange={(e) => onAnswerChange(e.target.value)}
        placeholder={t('assessments.enterAnswer')}
        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    );
  };

  const renderEssay = () => {
    return (
      <textarea
        value={answer || ''}
        onChange={(e) => onAnswerChange(e.target.value)}
        placeholder={t('assessments.enterEssayAnswer')}
        rows={8}
        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
      />
    );
  };

  return (
    <div className="space-y-6">
      {/* Question text */}
      <div className="prose dark:prose-invert max-w-none">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {questionText}
        </h3>
      </div>

      {/* Answer input based on question type */}
      <div>
        {question.question_type === 'multiple_choice' && renderMultipleChoice()}
        {question.question_type === 'true_false' && renderTrueFalse()}
        {question.question_type === 'short_answer' && renderShortAnswer()}
        {question.question_type === 'essay' && renderEssay()}
        {question.question_type === 'fill_blank' && renderShortAnswer()}
      </div>
    </div>
  );
};

export default AssessmentTaking;