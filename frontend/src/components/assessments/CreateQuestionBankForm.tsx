import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

const questionBankSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  title_ar: z.string().max(200, 'Arabic title too long').optional(),
  description: z.string().optional(),
  description_ar: z.string().optional(),
  course: z.string().min(1, 'Course is required'),
  is_public: z.boolean().default(false),
});

type QuestionBankFormData = z.infer<typeof questionBankSchema>;

interface Course {
  id: string;
  title: string;
  code: string;
}

interface CreateQuestionBankFormProps {
  onSubmit: (data: QuestionBankFormData) => Promise<void>;
  onCancel: () => void;
  courseId?: string;
  initialData?: Partial<QuestionBankFormData>;
}

const CreateQuestionBankForm: React.FC<CreateQuestionBankFormProps> = ({
  onSubmit,
  onCancel,
  courseId,
  initialData
}) => {
  const { t } = useTranslation();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<QuestionBankFormData>({
    resolver: zodResolver(questionBankSchema),
    defaultValues: {
      course: courseId || '',
      is_public: false,
      ...initialData
    }
  });

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    if (courseId) {
      setValue('course', courseId);
    }
  }, [courseId, setValue]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await apiService.request('/courses/');
      setCourses(response.results || response);
    } catch (error) {
      console.error('Failed to fetch courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = async (data: QuestionBankFormData) => {
    try {
      setSubmitting(true);
      await onSubmit(data);
    } catch (error) {
      console.error('Failed to submit form:', error);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" text={t('common.loading', 'Loading...')} />
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Title */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.title', 'Title')} *
        </label>
        <input
          type="text"
          id="title"
          {...register('title')}
          className="input-field"
          placeholder={t('assessments.titlePlaceholder', 'Enter question bank title')}
        />
        {errors.title && (
          <p className="text-red-600 text-sm mt-1">{errors.title.message}</p>
        )}
      </div>

      {/* Arabic Title */}
      <div>
        <label htmlFor="title_ar" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.titleArabic', 'Title (Arabic)')}
        </label>
        <input
          type="text"
          id="title_ar"
          {...register('title_ar')}
          className="input-field"
          placeholder={t('assessments.titleArabicPlaceholder', 'Enter Arabic title')}
          dir="rtl"
        />
        {errors.title_ar && (
          <p className="text-red-600 text-sm mt-1">{errors.title_ar.message}</p>
        )}
      </div>

      {/* Course Selection */}
      <div>
        <label htmlFor="course" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.course', 'Course')} *
        </label>
        <select
          id="course"
          {...register('course')}
          className="input-field"
          disabled={!!courseId}
        >
          <option value="">{t('assessments.selectCourse', 'Select a course')}</option>
          {courses.map((course) => (
            <option key={course.id} value={course.id}>
              {course.code} - {course.title}
            </option>
          ))}
        </select>
        {errors.course && (
          <p className="text-red-600 text-sm mt-1">{errors.course.message}</p>
        )}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.description', 'Description')}
        </label>
        <textarea
          id="description"
          {...register('description')}
          rows={3}
          className="input-field"
          placeholder={t('assessments.descriptionPlaceholder', 'Enter description (optional)')}
        />
        {errors.description && (
          <p className="text-red-600 text-sm mt-1">{errors.description.message}</p>
        )}
      </div>

      {/* Arabic Description */}
      <div>
        <label htmlFor="description_ar" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.descriptionArabic', 'Description (Arabic)')}
        </label>
        <textarea
          id="description_ar"
          {...register('description_ar')}
          rows={3}
          className="input-field"
          placeholder={t('assessments.descriptionArabicPlaceholder', 'Enter Arabic description (optional)')}
          dir="rtl"
        />
        {errors.description_ar && (
          <p className="text-red-600 text-sm mt-1">{errors.description_ar.message}</p>
        )}
      </div>

      {/* Public Setting */}
      <div className="flex items-center">
        <input
          type="checkbox"
          id="is_public"
          {...register('is_public')}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="is_public" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
          {t('assessments.makePublic', 'Make this question bank public')}
        </label>
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400 -mt-2">
        {t('assessments.publicDescription', 'Public question banks can be used by other instructors in the same course')}
      </p>

      {/* Form Actions */}
      <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onCancel}
          className="btn btn-secondary"
          disabled={submitting}
        >
          {t('common.cancel', 'Cancel')}
        </button>
        <button
          type="submit"
          className="btn btn-primary"
          disabled={submitting}
        >
          {submitting ? (
            <div className="flex items-center gap-2">
              <LoadingSpinner size="sm" />
              {t('common.saving', 'Saving...')}
            </div>
          ) : (
            t('common.save', 'Save')
          )}
        </button>
      </div>
    </form>
  );
};

export default CreateQuestionBankForm;
