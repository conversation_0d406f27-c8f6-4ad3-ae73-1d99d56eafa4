import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';
import {
  CheckCircle,
  Clock,
  Edit,
  Save,
  X,
  Eye,
  MessageSquare,
  Award,
  AlertTriangle,
  FileText,
  User,
  Calendar,
  Target,
  BarChart3,
  Filter,
  Search
} from 'lucide-react';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

interface GradingItem {
  id: string;
  student_id: string;
  student_name: string;
  student_email: string;
  assessment_id: string;
  assessment_title: string;
  submitted_at: string;
  status: 'pending' | 'graded' | 'reviewed';
  auto_graded: boolean;
  manually_graded: boolean;
  total_points: number;
  max_points: number;
  percentage_score: number;
  passed: boolean;
  requires_manual_grading: boolean;
  instructor_feedback: string;
  graded_by: string | null;
  graded_at: string | null;
}

interface QuestionResponse {
  id: string;
  question_id: string;
  question_text: string;
  question_type: string;
  student_answer: any;
  correct_answer: any;
  is_correct: boolean;
  points_earned: number;
  max_points: number;
  requires_manual_grading: boolean;
  manually_graded: boolean;
  grader_feedback: string;
  time_spent_seconds: number;
}

const GradingReviewSystem: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const { token } = useAppSelector((state) => state.auth);
  
  const [gradingItems, setGradingItems] = useState<GradingItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<GradingItem | null>(null);
  const [responses, setResponses] = useState<QuestionResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [showGradingModal, setShowGradingModal] = useState(false);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    if (token) {
      fetchGradingItems();
    }
  }, [token, filterStatus]);

  const fetchGradingItems = async () => {
    try {
      setLoading(true);
      apiService.setToken(token!);

      const params = new URLSearchParams();
      if (filterStatus !== 'all') {
        params.append('status', filterStatus);
      }

      const response = await apiService.request(`/assessments/grading/?${params.toString()}`);
      setGradingItems(response.results || response);
    } catch (error) {
      console.error('Failed to fetch grading items:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.gradingError')
      }));
    } finally {
      setLoading(false);
    }
  };

  const fetchAttemptDetails = async (attemptId: string) => {
    try {
      apiService.setToken(token!);
      const response = await apiService.request(`/assessments/attempts/${attemptId}/`);
      setResponses(response.responses || []);
    } catch (error) {
      console.error('Failed to fetch attempt details:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.fetchError')
      }));
    }
  };

  const handleGradeItem = async (item: GradingItem) => {
    setSelectedItem(item);
    await fetchAttemptDetails(item.id);
    setShowGradingModal(true);
  };

  const handleSaveGrade = async (responseId: string, points: number, feedback: string) => {
    try {
      setSaving(true);
      apiService.setToken(token!);

      await apiService.request(`/assessments/responses/${responseId}/grade/`, {
        method: 'POST',
        body: JSON.stringify({
          points_earned: points,
          grader_feedback: feedback
        })
      });

      // Refresh responses
      if (selectedItem) {
        await fetchAttemptDetails(selectedItem.id);
      }

      dispatch(addNotification({
        type: 'success',
        title: t('assessments.success'),
        message: t('assessments.gradeSaved')
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.gradeSaveError')
      }));
    } finally {
      setSaving(false);
    }
  };

  const handleFinalizeGrading = async () => {
    if (!selectedItem) return;

    try {
      setSaving(true);
      apiService.setToken(token!);

      await apiService.request(`/assessments/attempts/${selectedItem.id}/finalize-grading/`, {
        method: 'POST'
      });

      setShowGradingModal(false);
      setSelectedItem(null);
      await fetchGradingItems();

      dispatch(addNotification({
        type: 'success',
        title: t('assessments.success'),
        message: t('assessments.gradingFinalized')
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.finalizeError')
      }));
    } finally {
      setSaving(false);
    }
  };

  const filteredItems = gradingItems.filter(item => {
    const matchesSearch = !searchTerm || 
      item.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.assessment_title.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'graded': return 'text-green-600 bg-green-100';
      case 'reviewed': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('assessments.gradingReview')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('assessments.gradingDescription')}
            </p>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-orange-500 text-white">
              <Clock className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.pendingGrading')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {gradingItems.filter(item => item.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-500 text-white">
              <CheckCircle className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.graded')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {gradingItems.filter(item => item.status === 'graded').length}
              </p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-500 text-white">
              <Award className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.averageScore')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {gradingItems.length > 0 
                  ? (gradingItems.reduce((sum, item) => sum + item.percentage_score, 0) / gradingItems.length).toFixed(1)
                  : '0'
                }%
              </p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-500 text-white">
              <Target className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('assessments.passRate')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {gradingItems.length > 0 
                  ? ((gradingItems.filter(item => item.passed).length / gradingItems.length) * 100).toFixed(1)
                  : '0'
                }%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="glass-card">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.status')}
            </label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">{t('assessments.allStatuses')}</option>
              <option value="pending">{t('assessments.status.pending')}</option>
              <option value="graded">{t('assessments.status.graded')}</option>
              <option value="reviewed">{t('assessments.status.reviewed')}</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.search')}
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder={t('assessments.searchGradingPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Grading Items List */}
      <div className="glass-card">
        <div className="space-y-4">
          {filteredItems.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('assessments.noGradingItems')}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {t('assessments.noGradingItemsDesc')}
              </p>
            </div>
          ) : (
            filteredItems.map((item) => (
              <div
                key={item.id}
                className="p-6 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="flex items-center space-x-2">
                        <User className="w-5 h-5 text-gray-500" />
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {item.student_name}
                        </h3>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                        {t(`assessments.status.${item.status}`)}
                      </span>
                      {item.requires_manual_grading && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium text-orange-600 bg-orange-100">
                          {t('assessments.manualGradingRequired')}
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.assessment')}:
                        </span>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {item.assessment_title}
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.submitted')}:
                        </span>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {new Date(item.submitted_at).toLocaleDateString()}
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.score')}:
                        </span>
                        <p className={`text-sm font-medium ${getScoreColor(item.percentage_score)}`}>
                          {item.percentage_score.toFixed(1)}% ({item.total_points}/{item.max_points})
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.result')}:
                        </span>
                        <p className={`text-sm font-medium ${item.passed ? 'text-green-600' : 'text-red-600'}`}>
                          {item.passed ? t('assessments.passed') : t('assessments.failed')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>
                          {t('assessments.submitted')}: {new Date(item.submitted_at).toLocaleString()}
                        </span>
                      </div>
                      {item.graded_at && (
                        <div className="flex items-center space-x-1">
                          <CheckCircle className="w-4 h-4" />
                          <span>
                            {t('assessments.graded')}: {new Date(item.graded_at).toLocaleString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleGradeItem(item)}
                      className="btn btn-primary flex items-center gap-2"
                    >
                      <Edit className="w-4 h-4" />
                      {item.status === 'pending' ? t('assessments.grade') : t('assessments.review')}
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Grading Modal */}
      {showGradingModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-full overflow-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {t('assessments.gradingFor')} {selectedItem.student_name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {selectedItem.assessment_title}
                  </p>
                </div>
                <button
                  onClick={() => setShowGradingModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <GradingInterface
                responses={responses}
                onSaveGrade={handleSaveGrade}
                onFinalize={handleFinalizeGrading}
                saving={saving}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Grading Interface Component
const GradingInterface: React.FC<{
  responses: QuestionResponse[];
  onSaveGrade: (responseId: string, points: number, feedback: string) => Promise<void>;
  onFinalize: () => Promise<void>;
  saving: boolean;
}> = ({ responses, onSaveGrade, onFinalize, saving }) => {
  const { t } = useTranslation();
  const [editingResponse, setEditingResponse] = useState<string | null>(null);
  const [tempPoints, setTempPoints] = useState<number>(0);
  const [tempFeedback, setTempFeedback] = useState<string>('');

  const handleEditResponse = (response: QuestionResponse) => {
    setEditingResponse(response.id);
    setTempPoints(response.points_earned);
    setTempFeedback(response.grader_feedback || '');
  };

  const handleSaveResponse = async () => {
    if (editingResponse) {
      await onSaveGrade(editingResponse, tempPoints, tempFeedback);
      setEditingResponse(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingResponse(null);
    setTempPoints(0);
    setTempFeedback('');
  };

  return (
    <div className="space-y-6">
      {/* Summary */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600 dark:text-gray-400">
              {t('assessments.totalQuestions')}:
            </span>
            <span className="ml-1 text-gray-900 dark:text-white">
              {responses.length}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-600 dark:text-gray-400">
              {t('assessments.autoGraded')}:
            </span>
            <span className="ml-1 text-gray-900 dark:text-white">
              {responses.filter(r => !r.requires_manual_grading).length}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-600 dark:text-gray-400">
              {t('assessments.manualGrading')}:
            </span>
            <span className="ml-1 text-gray-900 dark:text-white">
              {responses.filter(r => r.requires_manual_grading).length}
            </span>
          </div>
        </div>
      </div>

      {/* Questions */}
      <div className="space-y-4">
        {responses.map((response, index) => (
          <div
            key={response.id}
            className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {t('assessments.question')} {index + 1}: {response.question_text}
                </h4>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {t(`assessments.types.${response.question_type}`)} • {response.max_points} {t('assessments.points')}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {response.requires_manual_grading && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium text-orange-600 bg-orange-100">
                    {t('assessments.manualRequired')}
                  </span>
                )}
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  response.is_correct ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
                }`}>
                  {response.points_earned}/{response.max_points}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.studentAnswer')}:
                </span>
                <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm">
                  {typeof response.student_answer === 'object'
                    ? JSON.stringify(response.student_answer)
                    : response.student_answer?.toString() || t('assessments.noAnswer')
                  }
                </div>
              </div>

              {response.correct_answer && (
                <div>
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {t('assessments.correctAnswer')}:
                  </span>
                  <div className="mt-1 p-2 bg-green-50 dark:bg-green-900 rounded text-sm">
                    {typeof response.correct_answer === 'object'
                      ? JSON.stringify(response.correct_answer)
                      : response.correct_answer?.toString()
                    }
                  </div>
                </div>
              )}
            </div>

            {editingResponse === response.id ? (
              <div className="space-y-3 p-3 bg-blue-50 dark:bg-blue-900 rounded">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {t('assessments.points')}
                    </label>
                    <input
                      type="number"
                      min="0"
                      max={response.max_points}
                      step="0.1"
                      value={tempPoints}
                      onChange={(e) => setTempPoints(parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('assessments.feedback')}
                  </label>
                  <textarea
                    value={tempFeedback}
                    onChange={(e) => setTempFeedback(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder={t('assessments.feedbackPlaceholder')}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleSaveResponse}
                    disabled={saving}
                    className="btn btn-primary flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    {t('common.save')}
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="btn btn-secondary"
                  >
                    {t('common.cancel')}
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {response.grader_feedback && (
                    <div>
                      <span className="font-medium">{t('assessments.feedback')}:</span> {response.grader_feedback}
                    </div>
                  )}
                </div>

                <button
                  onClick={() => handleEditResponse(response)}
                  className="btn btn-secondary flex items-center gap-2"
                >
                  <Edit className="w-4 h-4" />
                  {t('common.edit')}
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Finalize Button */}
      <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={onFinalize}
          disabled={saving}
          className="btn btn-primary flex items-center gap-2"
        >
          <CheckCircle className="w-5 h-5" />
          {t('assessments.finalizeGrading')}
        </button>
      </div>
    </div>
  );
};

export default GradingReviewSystem;
