import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';
import {
  FileText,
  Plus,
  Users,
  BarChart3,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  Download,
  Settings,
  TrendingUp,
  Calendar,
  Target,
  BookOpen,
  Database
} from 'lucide-react';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

interface Assessment {
  id: string;
  title: string;
  title_ar: string;
  course_name: string;
  course_code: string;
  assessment_type: string;
  status: string;
  total_points: number;
  questions_count: number;
  attempts_count: number;
  available_from: string;
  available_until: string;
  created_at: string;
  is_available: boolean;
}

interface DashboardStats {
  total_assessments: number;
  published_assessments: number;
  draft_assessments: number;
  total_attempts: number;
  completed_attempts: number;
  average_score: number;
  pending_grading: number;
}

const InstructorAssessmentDashboard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user, token } = useAppSelector((state) => state.auth);
  
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('overview');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    if (token) {
      fetchDashboardData();
    }
  }, [token]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      apiService.setToken(token!);

      // Fetch assessments data
      const assessmentsResponse = await apiService.request('/assessments/assessments/');
      const assessmentsData = assessmentsResponse.results || assessmentsResponse;

      setAssessments(assessmentsData);

      // Calculate statistics from assessments data
      const calculatedStats: DashboardStats = {
        total_assessments: assessmentsData.length,
        published_assessments: assessmentsData.filter((a: Assessment) => a.status === 'active').length,
        draft_assessments: assessmentsData.filter((a: Assessment) => a.status === 'draft').length,
        total_attempts: assessmentsData.reduce((sum: number, a: Assessment) => sum + (a.attempts_count || 0), 0),
        completed_attempts: 0, // This would need individual assessment statistics
        average_score: 0, // This would need individual assessment statistics
        pending_grading: 0 // This would need individual assessment statistics
      };

      setStats(calculatedStats);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.fetchError')
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAssessment = () => {
    navigate('/assessments?create=true');
  };

  const handleViewAssessment = (assessmentId: string) => {
    navigate(`/assessments/${assessmentId}`);
  };

  const handleEditAssessment = (assessmentId: string) => {
    navigate(`/assessments/${assessmentId}/edit`);
  };

  const handleDeleteAssessment = async (assessmentId: string) => {
    if (!confirm(t('assessments.confirmDelete'))) return;

    try {
      await apiService.request(`/assessments/assessments/${assessmentId}/`, {
        method: 'DELETE'
      });

      setAssessments(prev => prev.filter(a => a.id !== assessmentId));
      dispatch(addNotification({
        type: 'success',
        title: t('assessments.success'),
        message: t('assessments.deleteSuccess')
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.deleteError')
      }));
    }
  };

  const filteredAssessments = assessments.filter(assessment => {
    const matchesStatus = filterStatus === 'all' || assessment.status === filterStatus;
    const matchesSearch = !searchTerm || 
      assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assessment.course_name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'text-green-600 bg-green-100';
      case 'draft': return 'text-yellow-600 bg-yellow-100';
      case 'active': return 'text-blue-600 bg-blue-100';
      case 'closed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('assessments.instructorDashboard')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('assessments.managementDescription')}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/assessments/question-banks')}
              className="btn btn-secondary flex items-center gap-2"
            >
              <Database className="w-5 h-5" />
              {t('assessments.questionBanks')}
            </button>
            
            <button
              onClick={handleCreateAssessment}
              className="btn btn-primary flex items-center gap-2"
            >
              <Plus className="w-5 h-5" />
              {t('assessments.createAssessment')}
            </button>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-500 text-white">
                <FileText className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.totalAssessments')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.total_assessments}
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-500 text-white">
                <CheckCircle className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.publishedAssessments')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.published_assessments}
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-500 text-white">
                <Users className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.totalAttempts')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.total_attempts}
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-orange-500 text-white">
                <TrendingUp className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.averageScore')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.average_score?.toFixed(1) || '0'}%
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="glass-card">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: t('assessments.overview'), icon: BarChart3 },
              { id: 'assessments', label: t('assessments.assessments'), icon: FileText },
              { id: 'monitoring', label: t('assessments.monitoring'), icon: Eye },
              { id: 'analytics', label: t('assessments.analytics'), icon: TrendingUp }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  selectedTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {selectedTab === 'overview' && (
            <OverviewTab stats={stats} assessments={assessments} />
          )}

          {selectedTab === 'assessments' && (
            <AssessmentsTab
              assessments={filteredAssessments}
              filterStatus={filterStatus}
              setFilterStatus={setFilterStatus}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              onView={handleViewAssessment}
              onEdit={handleEditAssessment}
              onDelete={handleDeleteAssessment}
              getStatusColor={getStatusColor}
              isRTL={isRTL}
            />
          )}

          {selectedTab === 'monitoring' && (
            <MonitoringTab />
          )}

          {selectedTab === 'analytics' && (
            <AnalyticsTab />
          )}
        </div>
      </div>
    </div>
  );
};

// Overview Tab Component
const OverviewTab: React.FC<{
  stats: DashboardStats | null;
  assessments: Assessment[];
}> = ({ stats, assessments }) => {
  const { t } = useTranslation();

  const recentAssessments = assessments
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5);

  const upcomingDeadlines = assessments
    .filter(a => new Date(a.available_until) > new Date())
    .sort((a, b) => new Date(a.available_until).getTime() - new Date(b.available_until).getTime())
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Plus className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                {t('assessments.createNew')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('assessments.createNewDesc')}
              </p>
            </div>
          </div>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <Database className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                {t('assessments.manageQuestions')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('assessments.manageQuestionsDesc')}
              </p>
            </div>
          </div>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <BarChart3 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                {t('assessments.viewAnalytics')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('assessments.viewAnalyticsDesc')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Assessments and Upcoming Deadlines */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {t('assessments.recentAssessments')}
          </h3>
          <div className="space-y-3">
            {recentAssessments.map((assessment) => (
              <div
                key={assessment.id}
                className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {assessment.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {assessment.course_code} - {assessment.course_name}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(assessment.status)}`}>
                    {t(`assessments.status.${assessment.status}`)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {t('assessments.upcomingDeadlines')}
          </h3>
          <div className="space-y-3">
            {upcomingDeadlines.map((assessment) => (
              <div
                key={assessment.id}
                className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {assessment.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {t('assessments.dueDate')}: {new Date(assessment.available_until).toLocaleDateString()}
                    </p>
                  </div>
                  <Calendar className="w-5 h-5 text-orange-500" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Assessments Tab Component
const AssessmentsTab: React.FC<{
  assessments: Assessment[];
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  onView: (id: string) => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  getStatusColor: (status: string) => string;
  isRTL: boolean;
}> = ({
  assessments,
  filterStatus,
  setFilterStatus,
  searchTerm,
  setSearchTerm,
  onView,
  onEdit,
  onDelete,
  getStatusColor,
  isRTL
}) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder={t('assessments.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">{t('assessments.allStatuses')}</option>
          <option value="draft">{t('assessments.status.draft')}</option>
          <option value="published">{t('assessments.status.published')}</option>
          <option value="active">{t('assessments.status.active')}</option>
          <option value="closed">{t('assessments.status.closed')}</option>
        </select>
      </div>

      {/* Assessments List */}
      <div className="space-y-4">
        {assessments.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t('assessments.noAssessments')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {t('assessments.noAssessmentsDesc')}
            </p>
          </div>
        ) : (
          assessments.map((assessment) => (
            <div
              key={assessment.id}
              className="p-6 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {isRTL && assessment.title_ar ? assessment.title_ar : assessment.title}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(assessment.status)}`}>
                      {t(`assessments.status.${assessment.status}`)}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div>
                      <span className="font-medium">{t('assessments.course')}:</span> {assessment.course_code} - {assessment.course_name}
                    </div>
                    <div>
                      <span className="font-medium">{t('assessments.type')}:</span> {t(`assessments.types.${assessment.assessment_type}`)}
                    </div>
                    <div>
                      <span className="font-medium">{t('assessments.questions')}:</span> {assessment.questions_count}
                    </div>
                    <div>
                      <span className="font-medium">{t('assessments.points')}:</span> {assessment.total_points}
                    </div>
                    <div>
                      <span className="font-medium">{t('assessments.attempts')}:</span> {assessment.attempts_count}
                    </div>
                    <div>
                      <span className="font-medium">{t('assessments.availability')}:</span>
                      {assessment.is_available ? (
                        <span className="text-green-600 dark:text-green-400 ml-1">
                          {t('assessments.available')}
                        </span>
                      ) : (
                        <span className="text-red-600 dark:text-red-400 ml-1">
                          {t('assessments.unavailable')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => onView(assessment.id)}
                    className="p-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    title={t('common.view')}
                  >
                    <Eye className="w-5 h-5" />
                  </button>

                  <button
                    onClick={() => onEdit(assessment.id)}
                    className="p-2 text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    title={t('common.edit')}
                  >
                    <Edit className="w-5 h-5" />
                  </button>

                  <button
                    onClick={() => onDelete(assessment.id)}
                    className="p-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                    title={t('common.delete')}
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// Monitoring Tab Component
const MonitoringTab: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="text-center py-12">
      <Eye className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {t('assessments.monitoring')}
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        {t('assessments.monitoringComingSoon')}
      </p>
    </div>
  );
};

// Analytics Tab Component
const AnalyticsTab: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="text-center py-12">
      <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {t('assessments.analytics')}
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        {t('assessments.analyticsComingSoon')}
      </p>
    </div>
  );
};

export default InstructorAssessmentDashboard;
