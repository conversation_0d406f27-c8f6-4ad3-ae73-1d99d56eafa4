import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Plus,
  BookOpen,
  GraduationCap,
  Eye,
  Edit,
  Trash2,
  Share
} from 'lucide-react';
import { useAppSelector } from '../../store';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import Modal from '../common/Modal';
import CreateQuestionBankForm from './CreateQuestionBankForm';

interface QuestionBank {
  id: string;
  title: string;
  title_ar: string;
  description: string;
  course_name: string;
  course_code: string;
  created_by_name: string;
  questions_count: number;
  is_public: boolean;
  is_active: boolean;
  created_at: string;
}

interface QuestionBankListProps {
  courseId?: string;
  onSelectBank?: (bank: QuestionBank) => void;
}

const QuestionBankList: React.FC<QuestionBankListProps> = ({ courseId, onSelectBank }) => {
  const { t, i18n } = useTranslation();
  const { user } = useAppSelector((state) => state.auth);
  const [questionBanks, setQuestionBanks] = useState<QuestionBank[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedBank, setSelectedBank] = useState<QuestionBank | null>(null);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    fetchQuestionBanks();
  }, [courseId]);

  const fetchQuestionBanks = async () => {
    try {
      setLoading(true);
      const params = courseId ? { course_id: courseId } : {};
      const response = await apiService.getQuestionBanks(params);
      setQuestionBanks(response.results || response);
    } catch (error) {
      console.error('Failed to fetch question banks:', error);
      setError('Failed to load question banks');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBank = async (bankData: any) => {
    try {
      await apiService.createQuestionBank(bankData);
      setShowCreateModal(false);
      fetchQuestionBanks();
    } catch (error) {
      console.error('Failed to create question bank:', error);
      throw error;
    }
  };

  const handleDeleteBank = async (bankId: string) => {
    if (!confirm(t('assessments.confirmDeleteBank', 'Are you sure you want to delete this question bank?'))) {
      return;
    }

    try {
      await apiService.deleteQuestionBank(bankId);
      fetchQuestionBanks();
    } catch (error) {
      console.error('Failed to delete question bank:', error);
    }
  };

  const handleTogglePublic = async (bank: QuestionBank) => {
    try {
      await apiService.updateQuestionBank(bank.id, { is_public: !bank.is_public });
      fetchQuestionBanks();
    } catch (error) {
      console.error('Failed to update question bank:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" text={t('common.loading', 'Loading...')} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchQuestionBanks}
          className="btn btn-primary"
        >
          {t('common.retry', 'Retry')}
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('assessments.questionBanks', 'Question Banks')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {t('assessments.questionBanksDesc', 'Manage your question banks and reusable questions')}
          </p>
        </div>
        
        {user?.role !== 'student' && (
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn btn-primary flex items-center gap-2"
          >
            <Plus className="w-5 h-5" />
            {t('assessments.createQuestionBank', 'Create Question Bank')}
          </button>
        )}
      </div>

      {/* Question Banks Grid */}
      {questionBanks.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('assessments.noQuestionBanks', 'No Question Banks')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {t('assessments.noQuestionBanksDesc', 'Create your first question bank to get started')}
          </p>
          {user?.role !== 'student' && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn btn-primary"
            >
              {t('assessments.createFirstBank', 'Create First Question Bank')}
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {questionBanks.map((bank) => (
            <div
              key={bank.id}
              className="glass-card p-6 hover:shadow-lg transition-all duration-200 cursor-pointer"
              onClick={() => onSelectBank?.(bank)}
            >
              {/* Header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                    {isRTL && bank.title_ar ? bank.title_ar : bank.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {bank.course_code} - {bank.course_name}
                  </p>
                </div>
                
                {bank.is_public && (
                  <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full">
                    <Share className="w-3 h-3" />
                    {t('assessments.public', 'Public')}
                  </div>
                )}
              </div>

              {/* Description */}
              {bank.description && (
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                  {bank.description}
                </p>
              )}

              {/* Stats */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <GraduationCap className="w-4 h-4" />
                  <span>
                    {bank.questions_count} {t('assessments.questions', 'Questions')}
                  </span>
                </div>
                
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  {t('assessments.createdBy', 'Created by')} {bank.created_by_name}
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex gap-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectBank?.(bank);
                    }}
                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    title={t('common.view', 'View')}
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  
                  {user?.role !== 'student' && (
                    <>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedBank(bank);
                        }}
                        className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                        title={t('common.edit', 'Edit')}
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTogglePublic(bank);
                        }}
                        className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                        title={bank.is_public ? t('assessments.makePrivate', 'Make Private') : t('assessments.makePublic', 'Make Public')}
                      >
                        <Share className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteBank(bank.id);
                        }}
                        className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        title={t('common.delete', 'Delete')}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </>
                  )}
                </div>
                
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  {new Date(bank.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Question Bank Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t('assessments.createQuestionBank', 'Create Question Bank')}
        size="lg"
      >
        <CreateQuestionBankForm
          onSubmit={handleCreateBank}
          onCancel={() => setShowCreateModal(false)}
          courseId={courseId}
        />
      </Modal>
    </div>
  );
};

export default QuestionBankList;
