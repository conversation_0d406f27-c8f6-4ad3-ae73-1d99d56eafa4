import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Plus,
  Trash2,
  Image,
  Volume2
} from 'lucide-react';
import LoadingSpinner from '../common/LoadingSpinner';

const questionTypes = [
  { value: 'multiple_choice', label: 'Multiple Choice' },
  { value: 'true_false', label: 'True/False' },
  { value: 'short_answer', label: 'Short Answer' },
  { value: 'essay', label: 'Essay' },
  { value: 'fill_blank', label: 'Fill in the Blank' },
  { value: 'matching', label: 'Matching' },
  { value: 'ordering', label: 'Ordering' }
];

const difficultyLevels = [
  { value: 'easy', label: 'Easy' },
  { value: 'medium', label: 'Medium' },
  { value: 'hard', label: 'Hard' }
];

const questionSchema = z.object({
  question_text: z.string().min(1, 'Question text is required'),
  question_text_ar: z.string().optional(),
  explanation: z.string().optional(),
  explanation_ar: z.string().optional(),
  question_type: z.enum(['multiple_choice', 'true_false', 'short_answer', 'essay', 'fill_blank', 'matching', 'ordering']),
  difficulty_level: z.enum(['easy', 'medium', 'hard']),
  points: z.number().min(0.1, 'Points must be greater than 0'),
  tags: z.string().optional(),
  category: z.string().optional(),
  learning_objective: z.string().optional(),
  randomize_options: z.boolean().default(true),
  question_data: z.object({}).passthrough()
});

type QuestionFormData = z.infer<typeof questionSchema>;

interface QuestionEditorProps {
  onSubmit: (data: QuestionFormData) => Promise<void>;
  onCancel: () => void;
  initialData?: Partial<QuestionFormData>;
  questionBankId: string;
}

const QuestionEditor: React.FC<QuestionEditorProps> = ({
  onSubmit,
  onCancel,
  initialData,
  questionBankId
}) => {
  const { t } = useTranslation();
  const [submitting, setSubmitting] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [audioFile, setAudioFile] = useState<File | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    control
  } = useForm<QuestionFormData>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      question_type: 'multiple_choice',
      difficulty_level: 'medium',
      points: 1.0,
      randomize_options: true,
      question_data: {},
      ...initialData
    }
  });

  const questionType = watch('question_type');

  // Multiple choice options
  const { fields: options, append: addOption, remove: removeOption } = useFieldArray({
    control,
    name: 'question_data.options' as any
  });

  // Matching pairs
  const { fields: pairs, append: addPair, remove: removePair } = useFieldArray({
    control,
    name: 'question_data.pairs' as any
  });

  useEffect(() => {
    // Initialize question data based on type
    if (questionType === 'multiple_choice' && (!options || options.length === 0)) {
      setValue('question_data.options', ['', '', '', '']);
      setValue('question_data.correct_answers', []);
    } else if (questionType === 'true_false') {
      setValue('question_data.correct_answer', true);
    } else if (questionType === 'short_answer') {
      setValue('question_data.acceptable_answers', ['']);
    } else if (questionType === 'matching' && (!pairs || pairs.length === 0)) {
      setValue('question_data.pairs', [{ left: '', right: '' }, { left: '', right: '' }]);
    }
  }, [questionType, setValue, options, pairs]);

  const handleFormSubmit = async (data: QuestionFormData) => {
    try {
      setSubmitting(true);
      
      // Process question data based on type
      const processedData = { ...data };
      
      if (questionType === 'multiple_choice') {
        // Filter out empty options
        const validOptions = data.question_data.options?.filter((opt: string) => opt.trim()) || [];
        processedData.question_data = {
          ...data.question_data,
          options: validOptions
        };
      }
      
      await onSubmit(processedData);
    } catch (error) {
      console.error('Failed to submit question:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const renderQuestionTypeFields = () => {
    switch (questionType) {
      case 'multiple_choice':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('assessments.options', 'Options')} *
              </label>
              <div className="space-y-2">
                {[0, 1, 2, 3].map((index) => (
                  <div key={index} className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      {...register(`question_data.correct_answers.${index}` as any)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <input
                      type="text"
                      {...register(`question_data.options.${index}` as any)}
                      className="input-field flex-1"
                      placeholder={`${t('assessments.option', 'Option')} ${index + 1}`}
                    />
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {t('assessments.checkCorrectOptions', 'Check the correct option(s)')}
              </p>
            </div>
          </div>
        );

      case 'true_false':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.correctAnswer', 'Correct Answer')} *
            </label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  {...register('question_data.correct_answer' as any)}
                  value="true"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  {t('assessments.true', 'True')}
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  {...register('question_data.correct_answer' as any)}
                  value="false"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  {t('assessments.false', 'False')}
                </span>
              </label>
            </div>
          </div>
        );

      case 'short_answer':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.acceptableAnswers', 'Acceptable Answers')} *
            </label>
            <textarea
              {...register('question_data.acceptable_answers.0' as any)}
              rows={3}
              className="input-field"
              placeholder={t('assessments.acceptableAnswersPlaceholder', 'Enter acceptable answers, one per line')}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {t('assessments.acceptableAnswersHelp', 'Enter one answer per line. Answers are case-insensitive.')}
            </p>
          </div>
        );

      case 'essay':
        return (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              {t('assessments.essayNote', 'Essay questions require manual grading. Students will see a text area to write their response.')}
            </p>
          </div>
        );

      case 'matching':
        return (
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('assessments.matchingPairs', 'Matching Pairs')} *
              </label>
              <button
                type="button"
                onClick={() => addPair({ left: '', right: '' })}
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center gap-1"
              >
                <Plus className="w-4 h-4" />
                {t('assessments.addPair', 'Add Pair')}
              </button>
            </div>
            <div className="space-y-2">
              {pairs.map((pair, index) => (
                <div key={pair.id} className="flex items-center gap-3">
                  <input
                    type="text"
                    {...register(`question_data.pairs.${index}.left` as any)}
                    className="input-field flex-1"
                    placeholder={t('assessments.leftItem', 'Left item')}
                  />
                  <span className="text-gray-400">↔</span>
                  <input
                    type="text"
                    {...register(`question_data.pairs.${index}.right` as any)}
                    className="input-field flex-1"
                    placeholder={t('assessments.rightItem', 'Right item')}
                  />
                  {pairs.length > 2 && (
                    <button
                      type="button"
                      onClick={() => removePair(index)}
                      className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Question Text */}
      <div>
        <label htmlFor="question_text" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.questionText', 'Question Text')} *
        </label>
        <textarea
          id="question_text"
          {...register('question_text')}
          rows={3}
          className="input-field"
          placeholder={t('assessments.questionTextPlaceholder', 'Enter your question here')}
        />
        {errors.question_text && (
          <p className="text-red-600 text-sm mt-1">{errors.question_text.message}</p>
        )}
      </div>

      {/* Arabic Question Text */}
      <div>
        <label htmlFor="question_text_ar" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.questionTextArabic', 'Question Text (Arabic)')}
        </label>
        <textarea
          id="question_text_ar"
          {...register('question_text_ar')}
          rows={3}
          className="input-field"
          placeholder={t('assessments.questionTextArabicPlaceholder', 'Enter Arabic question text')}
          dir="rtl"
        />
      </div>

      {/* Question Type and Difficulty */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label htmlFor="question_type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('assessments.questionType', 'Question Type')} *
          </label>
          <select
            id="question_type"
            {...register('question_type')}
            className="input-field"
          >
            {questionTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {t(`assessments.${type.value}`, type.label)}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="difficulty_level" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('assessments.difficulty', 'Difficulty')} *
          </label>
          <select
            id="difficulty_level"
            {...register('difficulty_level')}
            className="input-field"
          >
            {difficultyLevels.map((level) => (
              <option key={level.value} value={level.value}>
                {t(`assessments.${level.value}`, level.label)}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="points" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('assessments.points', 'Points')} *
          </label>
          <input
            type="number"
            id="points"
            {...register('points', { valueAsNumber: true })}
            step="0.1"
            min="0.1"
            className="input-field"
          />
          {errors.points && (
            <p className="text-red-600 text-sm mt-1">{errors.points.message}</p>
          )}
        </div>
      </div>

      {/* Question Type Specific Fields */}
      {renderQuestionTypeFields()}

      {/* Explanation */}
      <div>
        <label htmlFor="explanation" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('assessments.explanation', 'Explanation')}
        </label>
        <textarea
          id="explanation"
          {...register('explanation')}
          rows={2}
          className="input-field"
          placeholder={t('assessments.explanationPlaceholder', 'Explain the correct answer (optional)')}
        />
      </div>

      {/* Additional Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('assessments.category', 'Category')}
          </label>
          <input
            type="text"
            id="category"
            {...register('category')}
            className="input-field"
            placeholder={t('assessments.categoryPlaceholder', 'e.g., Chapter 1, Midterm')}
          />
        </div>

        <div>
          <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('assessments.tags', 'Tags')}
          </label>
          <input
            type="text"
            id="tags"
            {...register('tags')}
            className="input-field"
            placeholder={t('assessments.tagsPlaceholder', 'Comma-separated tags')}
          />
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onCancel}
          className="btn btn-secondary"
          disabled={submitting}
        >
          {t('common.cancel', 'Cancel')}
        </button>
        <button
          type="submit"
          className="btn btn-primary"
          disabled={submitting}
        >
          {submitting ? (
            <div className="flex items-center gap-2">
              <LoadingSpinner size="sm" />
              {t('common.saving', 'Saving...')}
            </div>
          ) : (
            t('common.save', 'Save Question')
          )}
        </button>
      </div>
    </form>
  );
};

export default QuestionEditor;
