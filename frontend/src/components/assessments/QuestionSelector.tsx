import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  X,
  Search,
  GraduationCap,
  Check
} from 'lucide-react';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
// import Modal from '../common/Modal'; // Temporarily disabled

interface Question {
  id: string;
  question_text: string;
  question_text_ar: string;
  question_type: string;
  difficulty_level: string;
  points: number;
  question_bank_title: string;
  category: string;
  tags: string;
}

interface QuestionBank {
  id: string;
  title: string;
  title_ar: string;
  questions_count: number;
}

interface QuestionSelectorProps {
  courseId: string;
  onSelect: (questions: Question[]) => void;
  onClose: () => void;
  excludeQuestionIds?: string[];
  multiSelect?: boolean;
}

const QuestionSelector: React.FC<QuestionSelectorProps> = ({
  courseId,
  onSelect,
  onClose,
  excludeQuestionIds = [],
  multiSelect = true
}) => {
  const { t, i18n } = useTranslation();
  const [questionBanks, setQuestionBanks] = useState<QuestionBank[]>([]);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [selectedBankId, setSelectedBankId] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('');
  const [filterDifficulty, setFilterDifficulty] = useState<string>('');

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    fetchQuestionBanks();
  }, [courseId]);

  useEffect(() => {
    if (selectedBankId) {
      fetchQuestions();
    }
  }, [selectedBankId]);

  const fetchQuestionBanks = async () => {
    try {
      setLoading(true);
      const response = await apiService.request('/assessments/question-banks/', {
        method: 'GET',
        params: { course_id: courseId }
      });
      const banks = response.results || response;
      setQuestionBanks(banks);
      
      // Auto-select first bank if available
      if (banks.length > 0) {
        setSelectedBankId(banks[0].id);
      }
    } catch (error) {
      console.error('Failed to fetch question banks:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await apiService.request(`/assessments/question-banks/${selectedBankId}/questions/`);
      const allQuestions = response.results || response;
      
      // Filter out excluded questions
      const availableQuestions = allQuestions.filter(
        (q: Question) => !excludeQuestionIds.includes(q.id)
      );
      
      setQuestions(availableQuestions);
    } catch (error) {
      console.error('Failed to fetch questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  const filteredQuestions = questions.filter(question => {
    const matchesSearch = searchTerm === '' || 
      question.question_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (question.question_text_ar && question.question_text_ar.includes(searchTerm)) ||
      (question.category && question.category.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (question.tags && question.tags.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = filterType === '' || question.question_type === filterType;
    const matchesDifficulty = filterDifficulty === '' || question.difficulty_level === filterDifficulty;
    
    return matchesSearch && matchesType && matchesDifficulty;
  });

  const handleQuestionToggle = (question: Question) => {
    if (multiSelect) {
      const isSelected = selectedQuestions.find(q => q.id === question.id);
      if (isSelected) {
        setSelectedQuestions(selectedQuestions.filter(q => q.id !== question.id));
      } else {
        setSelectedQuestions([...selectedQuestions, question]);
      }
    } else {
      setSelectedQuestions([question]);
    }
  };

  const handleSelectAll = () => {
    if (selectedQuestions.length === filteredQuestions.length) {
      setSelectedQuestions([]);
    } else {
      setSelectedQuestions(filteredQuestions);
    }
  };

  const handleConfirmSelection = () => {
    onSelect(selectedQuestions);
  };

  const questionTypes = [
    { value: 'multiple_choice', label: 'Multiple Choice' },
    { value: 'true_false', label: 'True/False' },
    { value: 'short_answer', label: 'Short Answer' },
    { value: 'essay', label: 'Essay' },
    { value: 'fill_blank', label: 'Fill in the Blank' },
    { value: 'matching', label: 'Matching' },
    { value: 'ordering', label: 'Ordering' }
  ];

  const difficultyLevels = [
    { value: 'easy', label: 'Easy' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' }
  ];

  if (loading) {
    return (
      <Modal isOpen={true} onClose={onClose} title={t('assessments.selectQuestions', 'Select Questions')} size="xl">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" text={t('common.loading', 'Loading...')} />
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={true} onClose={onClose} title={t('assessments.selectQuestions', 'Select Questions')} size="xl">
      <div className="space-y-6">
        {/* Question Bank Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('assessments.questionBank', 'Question Bank')}
          </label>
          <select
            value={selectedBankId}
            onChange={(e) => setSelectedBankId(e.target.value)}
            className="input-field"
          >
            <option value="">{t('assessments.selectQuestionBank', 'Select a question bank')}</option>
            {questionBanks.map((bank) => (
              <option key={bank.id} value={bank.id}>
                {isRTL && bank.title_ar ? bank.title_ar : bank.title} ({bank.questions_count} {t('assessments.questions', 'questions')})
              </option>
            ))}
          </select>
        </div>

        {selectedBankId && (
          <>
            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="md:col-span-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder={t('assessments.searchQuestions', 'Search questions...')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input-field pl-10"
                  />
                </div>
              </div>

              {/* Question Type Filter */}
              <div>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="input-field"
                >
                  <option value="">{t('assessments.allTypes', 'All Types')}</option>
                  {questionTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {t(`assessments.${type.value}`, type.label)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Difficulty Filter */}
              <div>
                <select
                  value={filterDifficulty}
                  onChange={(e) => setFilterDifficulty(e.target.value)}
                  className="input-field"
                >
                  <option value="">{t('assessments.allDifficulties', 'All Difficulties')}</option>
                  {difficultyLevels.map((level) => (
                    <option key={level.value} value={level.value}>
                      {t(`assessments.${level.value}`, level.label)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Selection Controls */}
            {multiSelect && filteredQuestions.length > 0 && (
              <div className="flex justify-between items-center">
                <button
                  type="button"
                  onClick={handleSelectAll}
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                >
                  {selectedQuestions.length === filteredQuestions.length
                    ? t('assessments.deselectAll', 'Deselect All')
                    : t('assessments.selectAll', 'Select All')
                  }
                </button>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {selectedQuestions.length} {t('assessments.selected', 'selected')}
                </span>
              </div>
            )}

            {/* Questions List */}
            <div className="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
              {loadingQuestions ? (
                <div className="flex justify-center items-center h-32">
                  <LoadingSpinner size="md" text={t('assessments.loadingQuestions', 'Loading questions...')} />
                </div>
              ) : filteredQuestions.length === 0 ? (
                <div className="text-center py-8">
                  <GraduationCap className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600 dark:text-gray-400">
                    {questions.length === 0
                      ? t('assessments.noQuestionsInBank', 'No questions in this bank')
                      : t('assessments.noQuestionsMatchFilter', 'No questions match your filters')
                    }
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredQuestions.map((question) => {
                    const isSelected = selectedQuestions.find(q => q.id === question.id);
                    
                    return (
                      <div
                        key={question.id}
                        className={`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${
                          isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                        }`}
                        onClick={() => handleQuestionToggle(question)}
                      >
                        <div className="flex items-start gap-3">
                          {/* Checkbox */}
                          <div className="flex-shrink-0 mt-1">
                            <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                              isSelected
                                ? 'bg-blue-600 border-blue-600'
                                : 'border-gray-300 dark:border-gray-600'
                            }`}>
                              {isSelected && <Check className="w-3 h-3 text-white" />}
                            </div>
                          </div>

                          {/* Question Content */}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-900 dark:text-white font-medium mb-2">
                              {isRTL && question.question_text_ar
                                ? question.question_text_ar
                                : question.question_text
                              }
                            </p>
                            
                            <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                              <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {t(`assessments.${question.question_type}`, question.question_type)}
                              </span>
                              <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {t(`assessments.${question.difficulty_level}`, question.difficulty_level)}
                              </span>
                              <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {question.points} {t('assessments.points', 'points')}
                              </span>
                              {question.category && (
                                <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                  {question.category}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onClose}
            className="btn btn-secondary"
          >
            {t('common.cancel', 'Cancel')}
          </button>
          <button
            type="button"
            onClick={handleConfirmSelection}
            className="btn btn-primary"
            disabled={selectedQuestions.length === 0}
          >
            {t('assessments.addSelectedQuestions', 'Add Selected Questions')} ({selectedQuestions.length})
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default QuestionSelector;
