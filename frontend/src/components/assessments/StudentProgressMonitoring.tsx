import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';
import {
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  RefreshCw,
  Download,
  Filter,
  Search,
  BarChart3,
  TrendingUp,
  User,
  Calendar,
  Target,
  FileText
} from 'lucide-react';
import apiService from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

interface StudentAttempt {
  id: string;
  student_id: string;
  student_name: string;
  student_email: string;
  assessment_id: string;
  assessment_title: string;
  status: 'in_progress' | 'completed' | 'graded' | 'expired';
  started_at: string;
  submitted_at: string | null;
  time_spent_minutes: number;
  time_remaining_minutes: number;
  total_points: number;
  max_points: number;
  percentage_score: number | null;
  passed: boolean;
  current_question: number;
  total_questions: number;
  progress_percentage: number;
}

interface MonitoringStats {
  total_students: number;
  active_attempts: number;
  completed_attempts: number;
  average_completion_time: number;
  average_score: number;
  pass_rate: number;
}

const StudentProgressMonitoring: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const { token } = useAppSelector((state) => state.auth);
  
  const [attempts, setAttempts] = useState<StudentAttempt[]>([]);
  const [stats, setStats] = useState<MonitoringStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [assessments, setAssessments] = useState<any[]>([]);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    if (token) {
      fetchMonitoringData();
    }
  }, [token, selectedAssessment, statusFilter]);

  const fetchMonitoringData = async () => {
    try {
      setLoading(true);
      apiService.setToken(token!);

      const params = new URLSearchParams();
      if (selectedAssessment !== 'all') {
        params.append('assessment', selectedAssessment);
      }
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      const [attemptsResponse, statsResponse, assessmentsResponse] = await Promise.all([
        apiService.request(`/assessments/attempts/?${params.toString()}`),
        apiService.request(`/assessments/attempts/statistics/?${params.toString()}`),
        apiService.request('/assessments/assessments/')
      ]);

      setAttempts(attemptsResponse.results || attemptsResponse);
      setStats(statsResponse);
      setAssessments(assessmentsResponse.results || assessmentsResponse);
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('assessments.error'),
        message: t('assessments.monitoringError')
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchMonitoringData();
    setRefreshing(false);
  };

  const handleViewAttempt = (attemptId: string) => {
    // Navigate to attempt details
    console.log('View attempt:', attemptId);
  };

  const handleExportData = () => {
    // Export monitoring data
    console.log('Export data');
  };

  const filteredAttempts = attempts.filter(attempt => {
    const matchesSearch = !searchTerm || 
      attempt.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attempt.student_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attempt.assessment_title.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'graded': return 'text-purple-600 bg-purple-100';
      case 'expired': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-yellow-500';
    if (percentage >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('assessments.studentProgress')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('assessments.monitorStudentProgress')}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="btn btn-secondary flex items-center gap-2"
            >
              <RefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
              {t('common.refresh')}
            </button>
            
            <button
              onClick={handleExportData}
              className="btn btn-primary flex items-center gap-2"
            >
              <Download className="w-5 h-5" />
              {t('common.export')}
            </button>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-500 text-white">
                <Users className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.totalStudents')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.total_students}
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-orange-500 text-white">
                <Clock className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.activeAttempts')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.active_attempts}
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-500 text-white">
                <CheckCircle className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.completedAttempts')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.completed_attempts}
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-500 text-white">
                <TrendingUp className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.averageScore')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.average_score?.toFixed(1) || '0'}%
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-indigo-500 text-white">
                <Target className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.passRate')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.pass_rate?.toFixed(1) || '0'}%
                </p>
              </div>
            </div>
          </div>

          <div className="glass-card">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-teal-500 text-white">
                <Clock className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('assessments.avgCompletionTime')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.average_completion_time?.toFixed(0) || '0'}m
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="glass-card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.assessment')}
            </label>
            <select
              value={selectedAssessment}
              onChange={(e) => setSelectedAssessment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">{t('assessments.allAssessments')}</option>
              {assessments.map((assessment) => (
                <option key={assessment.id} value={assessment.id}>
                  {assessment.title}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.status')}
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">{t('assessments.allStatuses')}</option>
              <option value="in_progress">{t('assessments.status.in_progress')}</option>
              <option value="completed">{t('assessments.status.completed')}</option>
              <option value="graded">{t('assessments.status.graded')}</option>
              <option value="expired">{t('assessments.status.expired')}</option>
            </select>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('assessments.searchStudents')}
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder={t('assessments.searchStudentsPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Student Attempts List */}
      <div className="glass-card">
        <div className="space-y-4">
          {filteredAttempts.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('assessments.noAttempts')}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {t('assessments.noAttemptsDesc')}
              </p>
            </div>
          ) : (
            filteredAttempts.map((attempt) => (
              <div
                key={attempt.id}
                className="p-6 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="flex items-center space-x-2">
                        <User className="w-5 h-5 text-gray-500" />
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {attempt.student_name}
                        </h3>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(attempt.status)}`}>
                        {t(`assessments.status.${attempt.status}`)}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.assessment')}:
                        </span>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {attempt.assessment_title}
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.progress')}:
                        </span>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {attempt.current_question} / {attempt.total_questions} questions
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.timeSpent')}:
                        </span>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {attempt.time_spent_minutes} minutes
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {attempt.status === 'in_progress' ? t('assessments.timeRemaining') : t('assessments.score')}:
                        </span>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {attempt.status === 'in_progress'
                            ? `${attempt.time_remaining_minutes} minutes`
                            : attempt.percentage_score
                              ? `${attempt.percentage_score.toFixed(1)}%`
                              : 'N/A'
                          }
                        </p>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {t('assessments.completionProgress')}
                        </span>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {attempt.progress_percentage.toFixed(0)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(attempt.progress_percentage)}`}
                          style={{ width: `${attempt.progress_percentage}%` }}
                        />
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>
                          {t('assessments.started')}: {new Date(attempt.started_at).toLocaleString()}
                        </span>
                      </div>
                      {attempt.submitted_at && (
                        <div className="flex items-center space-x-1">
                          <CheckCircle className="w-4 h-4" />
                          <span>
                            {t('assessments.submitted')}: {new Date(attempt.submitted_at).toLocaleString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleViewAttempt(attempt.id)}
                      className="p-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      title={t('assessments.viewDetails')}
                    >
                      <Eye className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default StudentProgressMonitoring;
