import React, { useMemo } from 'react';
import { NavLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store';
import {
  LayoutDashboard,
  BookOpen,
  Users,
  FileText,
  Calendar,
  BarChart3,
  Settings,
  GraduationCap,
  Shield,
  UserCheck,
  DollarSign,
  Briefcase,
  Bell,
  Library,
  Building2,
  ClipboardCheck,
  CalendarDays,
  PieChart,
  MessageCircle,
  IdCard,
  QrCode,
  ClipboardList,
  Video,
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAppSelector((state) => state.auth);
  const { sidebarOpen } = useAppSelector((state) => state.ui);

  // Memoize navigation items to prevent unnecessary recalculations
  const navigationItems = useMemo(() => {
    const commonItems = [
      {
        name: t('navigation.dashboard'),
        href: '/app/dashboard',
        icon: LayoutDashboard,
      },
      {
        name: t('navigation.messaging'),
        href: '/app/messaging',
        icon: MessageCircle,
      },
    ];

    const roleSpecificItems = {
      super_admin: [
        { name: t('navigation.users'), href: '/app/users', icon: Users },
        { name: t('navigation.students'), href: '/app/students', icon: Users },
        { name: t('navigation.teachers'), href: '/app/teachers', icon: GraduationCap },
        { name: t('navigation.courses'), href: '/app/courses', icon: BookOpen },
        { name: t('navigation.departments'), href: '/app/departments', icon: Building2 },
        { name: t('navigation.assessments'), href: '/app/assessments', icon: ClipboardList },
        { name: t('navigation.videos'), href: '/app/videos', icon: Video },
        { name: t('navigation.exams'), href: '/app/exams', icon: FileText },
        { name: t('navigation.attendance'), href: '/app/attendance', icon: ClipboardCheck },
        { name: t('navigation.academicCalendar'), href: '/app/academic-calendar', icon: CalendarDays },
        { name: t('navigation.payments'), href: '/app/payments', icon: DollarSign },
        { name: t('navigation.reports'), href: '/app/reports', icon: BarChart3 },
        { name: t('navigation.customCharts'), href: '/app/custom-charts', icon: PieChart },
        { name: t('navigation.library'), href: '/app/library', icon: Library },
        { name: t('navigation.notifications'), href: '/app/notifications', icon: Bell },
        { name: t('navigation.system'), href: '/app/system', icon: Settings },
      ],
      admin: [
        { name: t('navigation.students'), href: '/app/students', icon: Users },
        { name: t('navigation.teachers'), href: '/app/teachers', icon: GraduationCap },
        { name: t('navigation.courses'), href: '/app/courses', icon: BookOpen },
        { name: t('navigation.departments'), href: '/app/departments', icon: Building2 },
        { name: t('navigation.staffVerification'), href: '/app/staff-verification', icon: QrCode },
        { name: t('navigation.assessments'), href: '/app/assessments', icon: ClipboardList },
        { name: t('navigation.videos'), href: '/app/videos', icon: Video },
        { name: t('navigation.exams'), href: '/app/exams', icon: FileText },
        { name: t('navigation.attendance'), href: '/app/attendance', icon: ClipboardCheck },
        { name: t('navigation.academicCalendar'), href: '/app/academic-calendar', icon: CalendarDays },
        { name: t('navigation.reports'), href: '/app/reports', icon: BarChart3 },
        { name: t('navigation.customCharts'), href: '/app/custom-charts', icon: PieChart },
        { name: t('navigation.library'), href: '/app/library', icon: Library },
        { name: t('navigation.notifications'), href: '/app/notifications', icon: Bell },
      ],
      teacher: [
        { name: t('navigation.courses'), href: '/app/courses', icon: BookOpen },
        { name: t('navigation.students'), href: '/app/students', icon: Users },
        { name: t('navigation.assignments'), href: '/app/assignments', icon: FileText },
        { name: t('navigation.assessments'), href: '/app/assessments', icon: ClipboardList },
        { name: t('navigation.videos'), href: '/app/videos', icon: Video },
        { name: t('navigation.exams'), href: '/app/exams', icon: FileText },
        { name: t('navigation.attendance'), href: '/app/attendance', icon: ClipboardCheck },
        { name: t('navigation.schedule'), href: '/app/schedule', icon: Calendar },
        { name: t('navigation.staffVerification'), href: '/app/staff-verification', icon: QrCode },
        { name: t('navigation.academicCalendar'), href: '/app/academic-calendar', icon: CalendarDays },
        { name: t('navigation.grades'), href: '/app/grades', icon: BarChart3 },
        { name: t('navigation.customCharts'), href: '/app/custom-charts', icon: PieChart },
        { name: t('navigation.library'), href: '/app/library', icon: Library },
        { name: t('navigation.notifications'), href: '/app/notifications', icon: Bell },
      ],
      student: [
        { name: t('navigation.courses'), href: '/app/courses', icon: BookOpen },
        { name: t('navigation.assignments'), href: '/app/assignments', icon: FileText },
        { name: t('navigation.assessments'), href: '/app/assessments', icon: ClipboardList },
        { name: t('navigation.videos'), href: '/app/videos', icon: Video },
        { name: t('navigation.exams'), href: '/app/exams', icon: FileText },
        { name: t('navigation.attendance'), href: '/app/attendance', icon: ClipboardCheck },
        { name: t('navigation.schedule'), href: '/app/schedule', icon: Calendar },
        { name: t('navigation.academicCalendar'), href: '/app/academic-calendar', icon: CalendarDays },
        { name: t('navigation.grades'), href: '/app/grades', icon: BarChart3 },
        { name: t('navigation.studentCard'), href: '/app/student-card', icon: IdCard },
        { name: t('navigation.studyGroups'), href: '/app/study-groups', icon: Users },
        { name: t('navigation.library'), href: '/app/library', icon: Library },
        { name: t('navigation.notifications'), href: '/app/notifications', icon: Bell },
      ],
      finance_officer: [
        { name: t('navigation.payments'), href: '/app/payments', icon: DollarSign },
        { name: t('navigation.invoices'), href: '/app/invoices', icon: FileText },
        { name: t('navigation.students'), href: '/app/students', icon: Users },
        { name: t('navigation.departments'), href: '/app/departments', icon: Building2 },
        { name: t('navigation.academicCalendar'), href: '/app/academic-calendar', icon: CalendarDays },
        { name: t('navigation.reports'), href: '/app/reports', icon: BarChart3 },
        { name: t('navigation.notifications'), href: '/app/notifications', icon: Bell },
      ],
      staff: [
        { name: t('navigation.tasks'), href: '/app/tasks', icon: Briefcase },
        { name: t('navigation.documents'), href: '/app/documents', icon: FileText },
        { name: t('navigation.requests'), href: '/app/requests', icon: Users },
        { name: t('navigation.students'), href: '/app/students', icon: Users },
        { name: t('navigation.staffVerification'), href: '/app/staff-verification', icon: QrCode },
        { name: t('navigation.departments'), href: '/app/departments', icon: Building2 },
        { name: t('navigation.academicCalendar'), href: '/app/academic-calendar', icon: CalendarDays },
        { name: t('navigation.library'), href: '/app/library', icon: Library },
        { name: t('navigation.notifications'), href: '/app/notifications', icon: Bell },
      ],
    };

    return [
      ...commonItems,
      ...(roleSpecificItems[user?.role as keyof typeof roleSpecificItems] || []),
    ];
  }, [user?.role, t]);

  // Memoize role icon to prevent unnecessary recalculations
  const RoleIcon = useMemo(() => {
    const iconMap = {
      super_admin: Shield,
      admin: UserCheck,
      teacher: GraduationCap,
      student: BookOpen,
      finance_officer: DollarSign,
      staff: Briefcase,
    };
    return iconMap[user?.role as keyof typeof iconMap] || Users;
  }, [user?.role]);

  return (
    <aside
      className={`fixed left-0 top-0 h-full glass-sidebar transition-all duration-300 z-40 ${
        sidebarOpen ? 'w-64' : 'w-16'
      }`}
      role="complementary"
      aria-label={t('navigation.mainNavigation', 'Main navigation')}
    >
      <div className="flex flex-col h-full">
        {/* Logo/Brand */}
        <div className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-teal-500 to-blue-600 rounded-lg flex items-center justify-center">
              <RoleIcon className="w-5 h-5 text-white" />
            </div>
            {sidebarOpen && (
              <div>
                <h1 className="text-lg font-bold">
                  Itkan
                </h1>
                <p className="text-xs text-gray-600 dark:text-gray-400" dir="rtl">
                  إتقان
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav
          className="flex-1 p-4"
          role="navigation"
          aria-label={t('navigation.primaryNavigation', 'Primary navigation')}
        >
          <ul className="space-y-2" role="list">
            {navigationItems.map((item) => (
              <li key={item.href} role="listitem">
                <NavLink
                  to={item.href}
                  className={({ isActive }) =>
                    `flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-white/20 text-blue-700 dark:text-blue-300 backdrop-blur-sm'
                        : ' hover:bg-white/10 hover:text-gray-900 dark:hover:text-white hover:backdrop-blur-sm'
                    }`
                  }
                  title={!sidebarOpen ? item.name : undefined}
                  aria-label={item.name}
                >
                  <item.icon
                    className="w-5 h-5 flex-shrink-0"
                    aria-hidden="true"
                  />
                  {sidebarOpen && (
                    <span className="font-medium">{item.name}</span>
                  )}
                  {!sidebarOpen && (
                    <span className="sr-only">{item.name}</span>
                  )}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>

        {/* User Info */}
        {sidebarOpen && (
          <div className="p-4">
            <div className="flex items-center space-x-3">
              {user?.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.firstName}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <Users className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-700 dark:text-gray-400 truncate">
                  {user?.email}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default React.memo(Sidebar);
