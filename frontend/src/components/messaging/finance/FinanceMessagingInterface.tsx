import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../../store';
import { addNotification } from '../../../store/slices/uiSlice';
import { apiService } from '../../../services/api';
import {
  Building,
  DollarSign,
  AlertTriangle,
  MessageSquare,
  FileText,
  Users,
  CreditCard,
  Receipt,
  TrendingUp,
  Bell
} from 'lucide-react';

// Components
import TabNavigation from '../shared/TabNavigation';

// Finance-specific tab components
const PaymentNotificationsTab: React.FC<any> = ({ messagingState }) => (
  <div className="p-6">
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-lg font-medium text-gray-900">Payment Notifications</h3>
      <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
        Send Payment Reminder
      </button>
    </div>

    <div className="space-y-4">
      {/* Sample payment notifications */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-yellow-800">Overdue Payment Alert</h4>
            <p className="text-yellow-700 text-sm mt-1">
              Student Elizabeth Brown (STU2024099) has an overdue payment of $1,250 for Fall 2024 tuition.
            </p>
            <div className="flex space-x-2 mt-3">
              <button className="text-yellow-800 text-sm font-medium hover:underline">Send Reminder</button>
              <button className="text-yellow-800 text-sm font-medium hover:underline">View Details</button>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <CreditCard className="w-5 h-5 text-green-600 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-green-800">Payment Received</h4>
            <p className="text-green-700 text-sm mt-1">
              Payment of $2,500 received from Jennifer Al-Rashid (STU2024098) for Spring 2024 tuition.
            </p>
            <div className="flex space-x-2 mt-3">
              <button className="text-green-800 text-sm font-medium hover:underline">Send Confirmation</button>
              <button className="text-green-800 text-sm font-medium hover:underline">Generate Receipt</button>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Receipt className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-blue-800">Invoice Generated</h4>
            <p className="text-blue-700 text-sm mt-1">
              New invoice #INV-2024-0156 generated for Omar Smith (STU2024096) - Amount: $3,200.
            </p>
            <div className="flex space-x-2 mt-3">
              <button className="text-blue-800 text-sm font-medium hover:underline">Send Invoice</button>
              <button className="text-blue-800 text-sm font-medium hover:underline">View Invoice</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

const FinancialMessagesTab: React.FC<any> = () => (
  <div className="p-6">
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-lg font-medium text-gray-900">Financial Messages</h3>
      <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
        New Message
      </button>
    </div>

    <div className="space-y-4">
      {/* Sample financial messages */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-100 p-2 rounded-full">
              <MessageSquare className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Payment Plan Request</h4>
              <p className="text-gray-600 text-sm">From: Elizabeth Brown (<EMAIL>)</p>
              <p className="text-gray-700 text-sm mt-2">
                I would like to request a payment plan for my remaining tuition balance...
              </p>
            </div>
          </div>
          <span className="text-xs text-gray-500">2 hours ago</span>
        </div>
        <div className="flex space-x-2 mt-4">
          <button className="bg-emerald-600 text-white px-3 py-1 rounded text-sm hover:bg-emerald-700">Reply</button>
          <button className="text-gray-600 px-3 py-1 rounded text-sm hover:bg-gray-100">Archive</button>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="bg-yellow-100 p-2 rounded-full">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Scholarship Verification</h4>
              <p className="text-gray-600 text-sm">From: Jennifer Al-Rashid (<EMAIL>)</p>
              <p className="text-gray-700 text-sm mt-2">
                I need verification of my scholarship status for external documentation...
              </p>
            </div>
          </div>
          <span className="text-xs text-gray-500">1 day ago</span>
        </div>
        <div className="flex space-x-2 mt-4">
          <button className="bg-emerald-600 text-white px-3 py-1 rounded text-sm hover:bg-emerald-700">Reply</button>
          <button className="text-gray-600 px-3 py-1 rounded text-sm hover:bg-gray-100">Archive</button>
        </div>
      </div>
    </div>
  </div>
);

const FinancialReportsTab: React.FC<any> = () => (
  <div className="p-6">
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-lg font-medium text-gray-900">Financial Reports & Communications</h3>
      <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
        Generate Report
      </button>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Quick Stats */}
      <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-lg font-semibold">Monthly Collections</h4>
            <p className="text-2xl font-bold mt-2">$425,000</p>
            <p className="text-emerald-100 text-sm">85% of target</p>
          </div>
          <TrendingUp className="w-8 h-8 text-emerald-200" />
        </div>
      </div>

      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-lg font-semibold">Pending Payments</h4>
            <p className="text-2xl font-bold mt-2">$125,000</p>
            <p className="text-blue-100 text-sm">23 students</p>
          </div>
          <DollarSign className="w-8 h-8 text-blue-200" />
        </div>
      </div>
    </div>

    <div className="mt-6 space-y-4">
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Recent Financial Communications</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <span className="text-sm text-gray-700">Monthly financial summary sent to administration</span>
            <span className="text-xs text-gray-500">Today</span>
          </div>
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <span className="text-sm text-gray-700">Payment reminder batch sent to 15 students</span>
            <span className="text-xs text-gray-500">Yesterday</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <span className="text-sm text-gray-700">Scholarship disbursement notifications sent</span>
            <span className="text-xs text-gray-500">2 days ago</span>
          </div>
        </div>
      </div>
    </div>
  </div>
);

const StudentAccountsTab: React.FC<any> = () => (
  <div className="p-6">
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-lg font-medium text-gray-900">Student Account Communications</h3>
      <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
        Bulk Message
      </button>
    </div>

    <div className="space-y-4">
      {/* Account status communications */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-4">Account Status Updates</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <div>
                <p className="font-medium text-red-800">Account Hold - Elizabeth Brown</p>
                <p className="text-red-600 text-sm">Outstanding balance: $1,250</p>
              </div>
            </div>
            <button className="text-red-800 text-sm font-medium hover:underline">Contact</button>
          </div>

          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Bell className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">Payment Due Soon - Omar Smith</p>
                <p className="text-yellow-600 text-sm">Due in 3 days: $2,100</p>
              </div>
            </div>
            <button className="text-yellow-800 text-sm font-medium hover:underline">Remind</button>
          </div>

          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <CreditCard className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Account Current - Jennifer Al-Rashid</p>
                <p className="text-green-600 text-sm">All payments up to date</p>
              </div>
            </div>
            <button className="text-green-800 text-sm font-medium hover:underline">View</button>
          </div>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-4">Communication Templates</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
            <h5 className="font-medium text-gray-900">Payment Reminder</h5>
            <p className="text-gray-600 text-sm mt-1">Standard payment due reminder template</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
            <h5 className="font-medium text-gray-900">Account Hold Notice</h5>
            <p className="text-gray-600 text-sm mt-1">Account hold notification template</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
            <h5 className="font-medium text-gray-900">Payment Confirmation</h5>
            <p className="text-gray-600 text-sm mt-1">Payment received confirmation template</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
            <h5 className="font-medium text-gray-900">Scholarship Notice</h5>
            <p className="text-gray-600 text-sm mt-1">Scholarship award notification template</p>
          </button>
        </div>
      </div>
    </div>
  </div>
);

// Local type definitions
interface TabConfig {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  component: React.ComponentType<any>;
  badge?: number;
  color: string;
}

interface MessagingState {
  activeTab: string;
  loading: boolean;
  paymentNotifications: any[];
  financialMessages: any[];
  reports: any[];
  studentAccounts: any[];
}

/**
 * Finance Officer Messaging Interface
 * Provides comprehensive financial communication tools
 */
const FinanceMessagingInterface: React.FC = () => {
  const { t } = useTranslation();
  const { token } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  // State management
  const [activeTab, setActiveTab] = useState('notifications');
  const [messagingState, setMessagingState] = useState<MessagingState>({
    activeTab: 'notifications',
    loading: true,
    paymentNotifications: [],
    financialMessages: [],
    reports: [],
    studentAccounts: []
  });

  // Tab configuration
  const tabs: TabConfig[] = [
    {
      id: 'notifications',
      label: t('messaging.paymentNotifications', 'Payment Notifications'),
      icon: Bell,
      component: PaymentNotificationsTab,
      badge: 3, // Sample badge count
      color: 'red'
    },
    {
      id: 'messages',
      label: t('messaging.financialMessages', 'Financial Messages'),
      icon: MessageSquare,
      component: FinancialMessagesTab,
      badge: 2, // Sample badge count
      color: 'blue'
    },
    {
      id: 'reports',
      label: t('messaging.reports', 'Reports & Communications'),
      icon: FileText,
      component: FinancialReportsTab,
      color: 'green'
    },
    {
      id: 'accounts',
      label: t('messaging.studentAccounts', 'Student Accounts'),
      icon: Users,
      component: StudentAccountsTab,
      badge: 5, // Sample badge count
      color: 'purple'
    }
  ];

  // Data fetching
  useEffect(() => {
    if (token) {
      fetchFinanceData();
    }
  }, [token]);

  const fetchFinanceData = async () => {
    try {
      setMessagingState(prev => ({ ...prev, loading: true }));
      apiService.setToken(token);

      // Fetch financial data in parallel
      const [
        paymentsResponse,
        conversationsResponse,
        studentsResponse
      ] = await Promise.all([
        apiService.getPayments(),
        apiService.getConversations(),
        apiService.getStudents()
      ]);

      setMessagingState(prev => ({
        ...prev,
        paymentNotifications: (paymentsResponse as any).results || paymentsResponse,
        financialMessages: (conversationsResponse as any).results || conversationsResponse,
        studentAccounts: (studentsResponse as any).results || studentsResponse,
        loading: false
      }));

    } catch (error) {
      console.error('Failed to fetch finance data:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('messaging.error', 'Error'),
        message: t('messaging.fetchDataError', 'Failed to fetch messaging data')
      }));
      setMessagingState(prev => ({ ...prev, loading: false }));
    }
  };

  // Get current tab component
  const getCurrentTabComponent = () => {
    const currentTab = tabs.find(tab => tab.id === activeTab);
    if (!currentTab) return null;

    const TabComponent = currentTab.component;

    return (
      <TabComponent
        messagingState={messagingState}
        setMessagingState={setMessagingState}
        onRefresh={fetchFinanceData}
      />
    );
  };

  if (messagingState.loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Header */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-emerald-100 p-3 rounded-full">
                <Building className="w-8 h-8 text-emerald-600" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {t('messaging.financeCommunications', 'Financial Communications')}
                </h1>
                <p className="text-gray-600 mt-1">
                  {t('messaging.financeDesc', 'Manage payment notifications and financial communications')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-8"
        />

        {/* Tab Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 min-h-[calc(100vh-16rem)]">
          {getCurrentTabComponent()}
        </div>
      </div>
    </div>
  );
};

export default FinanceMessagingInterface;
