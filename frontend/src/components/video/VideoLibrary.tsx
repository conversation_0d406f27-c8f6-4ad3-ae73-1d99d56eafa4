import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Search, 
  Filter, 
  Grid, 
  List, 
  Play, 
  Clock, 
  Eye, 
  Download,
  MoreVertical,
  Edit,
  Trash2,
  Upload,
  Plus
} from 'lucide-react';
import VideoPlayer from './VideoPlayer';
import VideoUpload from './VideoUpload';

interface Video {
  id: string;
  title: string;
  title_ar: string;
  description: string;
  description_ar: string;
  thumbnail: string;
  duration_seconds: number;
  duration_formatted: string;
  file_size_formatted: string;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  course_name: string;
  course_code: string;
  module_name: string;
  lesson_name: string;
  uploaded_by_name: string;
  view_count: number;
  unique_viewers: number;
  created_at: string;
  streaming_urls: Record<string, any>;
  user_progress: {
    current_time: number;
    completion_percentage: number;
    is_completed: boolean;
  } | null;
  allow_download: boolean;
}

interface VideoLibraryProps {
  courseId?: string;
  moduleId?: string;
  lessonId?: string;
  showUpload?: boolean;
  showFilters?: boolean;
  viewMode?: 'grid' | 'list';
  className?: string;
}

const VideoLibrary: React.FC<VideoLibraryProps> = ({
  courseId,
  moduleId,
  lessonId,
  showUpload = false,
  showFilters = true,
  viewMode: initialViewMode = 'grid',
  className = ''
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(initialViewMode);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);

  // Fetch videos
  const fetchVideos = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (courseId) params.append('course_id', courseId);
      if (moduleId) params.append('module_id', moduleId);
      if (lessonId) params.append('lesson_id', lessonId);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await fetch(`/api/video-streaming/videos/?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(t('video.library.fetchError'));
      }

      const data = await response.json();
      setVideos(data.results || data);
    } catch (err) {
      setError(err instanceof Error ? err.message : t('video.library.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideos();
  }, [courseId, moduleId, lessonId, statusFilter]);

  // Filter videos based on search term
  const filteredVideos = videos.filter(video => {
    const searchLower = searchTerm.toLowerCase();
    return (
      video.title.toLowerCase().includes(searchLower) ||
      video.title_ar.toLowerCase().includes(searchLower) ||
      video.description.toLowerCase().includes(searchLower) ||
      video.course_name.toLowerCase().includes(searchLower) ||
      video.uploaded_by_name.toLowerCase().includes(searchLower)
    );
  });

  // Handle video selection
  const handleVideoSelect = (video: Video) => {
    setSelectedVideo(video);
    setShowVideoModal(true);
  };

  // Handle progress update
  const handleProgressUpdate = async (currentTime: number, duration: number) => {
    if (!selectedVideo) return;

    try {
      await fetch(`/api/video-streaming/videos/${selectedVideo.id}/update_progress/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          current_time_seconds: Math.floor(currentTime),
          session_id: `session_${Date.now()}`
        })
      });
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  };

  // Handle upload complete
  const handleUploadComplete = (uploadedVideos: any[]) => {
    setShowUploadModal(false);
    fetchVideos(); // Refresh the list
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    const badges = {
      uploading: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      processing: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      ready: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status as keyof typeof badges] || badges.ready}`}>
        {t(`video.status.${status}`)}
      </span>
    );
  };

  // Render video card
  const renderVideoCard = (video: Video) => (
    <div
      key={video.id}
      className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
      onClick={() => handleVideoSelect(video)}
    >
      {/* Thumbnail */}
      <div className="relative aspect-video bg-gray-100 dark:bg-gray-700">
        {video.thumbnail ? (
          <img
            src={video.thumbnail}
            alt={isRTL ? video.title_ar || video.title : video.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Play className="w-12 h-12 text-gray-400" />
          </div>
        )}
        
        {/* Duration overlay */}
        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
          {video.duration_formatted}
        </div>
        
        {/* Progress bar */}
        {video.user_progress && video.user_progress.completion_percentage > 0 && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/30">
            <div
              className="h-full bg-blue-500"
              style={{ width: `${video.user_progress.completion_percentage}%` }}
            />
          </div>
        )}
        
        {/* Status badge */}
        <div className="absolute top-2 left-2">
          {getStatusBadge(video.status)}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
          {isRTL ? video.title_ar || video.title : video.title}
        </h3>
        
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
          <span>{video.uploaded_by_name}</span>
          <span>{new Date(video.created_at).toLocaleDateString()}</span>
        </div>
        
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center">
            <Eye className="w-4 h-4 mr-1" />
            {video.view_count}
          </div>
          <div className="flex items-center">
            <Clock className="w-4 h-4 mr-1" />
            {video.duration_formatted}
          </div>
        </div>
        
        {video.course_name && (
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {video.course_code} - {video.course_name}
            {video.module_name && ` / ${video.module_name}`}
          </div>
        )}
      </div>
    </div>
  );

  // Render video list item
  const renderVideoListItem = (video: Video) => (
    <div
      key={video.id}
      className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-lg transition-shadow cursor-pointer"
      onClick={() => handleVideoSelect(video)}
    >
      <div className="flex items-center space-x-4">
        {/* Thumbnail */}
        <div className="relative w-32 h-20 bg-gray-100 dark:bg-gray-700 rounded overflow-hidden flex-shrink-0">
          {video.thumbnail ? (
            <img
              src={video.thumbnail}
              alt={isRTL ? video.title_ar || video.title : video.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Play className="w-6 h-6 text-gray-400" />
            </div>
          )}
          
          <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 py-0.5 rounded">
            {video.duration_formatted}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 dark:text-white mb-1 truncate">
                {isRTL ? video.title_ar || video.title : video.title}
              </h3>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                {isRTL ? video.description_ar || video.description : video.description}
              </p>
              
              <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span>{video.uploaded_by_name}</span>
                <span>{new Date(video.created_at).toLocaleDateString()}</span>
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  {video.view_count}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 ml-4">
              {getStatusBadge(video.status)}
              <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <MoreVertical className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* Progress bar */}
          {video.user_progress && video.user_progress.completion_percentage > 0 && (
            <div className="mt-2">
              <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div
                  className="h-full bg-blue-500 rounded-full"
                  style={{ width: `${video.user_progress.completion_percentage}%` }}
                />
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {Math.round(video.user_progress.completion_percentage)}% {t('video.completed')}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchVideos}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          {t('common.retry')}
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          {t('video.library.title')}
        </h2>
        
        {showUpload && (
          <button
            onClick={() => setShowUploadModal(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Upload className="w-4 h-4 mr-2" />
            {t('video.upload.title')}
          </button>
        )}
      </div>

      {/* Filters and Search */}
      {showFilters && (
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={t('video.library.searchPlaceholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">{t('video.library.allStatuses')}</option>
            <option value="ready">{t('video.status.ready')}</option>
            <option value="processing">{t('video.status.processing')}</option>
            <option value="uploading">{t('video.status.uploading')}</option>
            <option value="error">{t('video.status.error')}</option>
          </select>

          {/* View Mode Toggle */}
          <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400'}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400'}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Videos */}
      {filteredVideos.length === 0 ? (
        <div className="text-center py-12">
          <Play className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('video.library.noVideos')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('video.library.noVideosDescription')}
          </p>
          {showUpload && (
            <button
              onClick={() => setShowUploadModal(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              {t('video.upload.uploadFirst')}
            </button>
          )}
        </div>
      ) : (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {filteredVideos.map(video => 
            viewMode === 'grid' ? renderVideoCard(video) : renderVideoListItem(video)
          )}
        </div>
      )}

      {/* Video Player Modal */}
      {showVideoModal && selectedVideo && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-full overflow-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {isRTL ? selectedVideo.title_ar || selectedVideo.title : selectedVideo.title}
                </h3>
                <button
                  onClick={() => setShowVideoModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <VideoPlayer
                videoId={selectedVideo.id}
                title={isRTL ? selectedVideo.title_ar || selectedVideo.title : selectedVideo.title}
                streamingUrls={selectedVideo.streaming_urls}
                thumbnail={selectedVideo.thumbnail}
                onProgressUpdate={handleProgressUpdate}
                initialTime={selectedVideo.user_progress?.current_time || 0}
                className="w-full h-96"
              />
              
              {selectedVideo.description && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    {t('video.description')}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    {isRTL ? selectedVideo.description_ar || selectedVideo.description : selectedVideo.description}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-full overflow-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {t('video.upload.title')}
                </h3>
                <button
                  onClick={() => setShowUploadModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <VideoUpload
                courseId={courseId || ''}
                moduleId={moduleId}
                lessonId={lessonId}
                onUploadComplete={handleUploadComplete}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoLibrary;
