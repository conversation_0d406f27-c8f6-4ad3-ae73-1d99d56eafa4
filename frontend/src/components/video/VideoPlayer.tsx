import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Settings,
  SkipBack,
  SkipForward,
  Loader
} from 'lucide-react';

interface VideoPlayerProps {
  videoId: string;
  title: string;
  streamingUrls: Record<string, {
    url: string;
    resolution: string;
    bitrate: number;
    file_size: number;
  }>;
  subtitles?: Array<{
    id: string;
    language_code: string;
    language_name: string;
    subtitle_file: string;
    is_default: boolean;
  }>;
  thumbnail?: string;
  onProgressUpdate?: (currentTime: number, duration: number) => void;
  initialTime?: number;
  autoPlay?: boolean;
  className?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoId,
  title,
  streamingUrls,
  subtitles = [],
  thumbnail,
  onProgressUpdate,
  initialTime = 0,
  autoPlay = false,
  className = ''
}) => {
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  
  // Player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [selectedQuality, setSelectedQuality] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [selectedSubtitle, setSelectedSubtitle] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Auto-hide controls
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  const resetControlsTimeout = useCallback(() => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    setShowControls(true);
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false);
      }
    }, 3000);
  }, [isPlaying]);

  // Initialize video
  useEffect(() => {
    if (Object.keys(streamingUrls).length > 0 && !selectedQuality) {
      // Select best available quality
      const qualities = Object.keys(streamingUrls).sort((a, b) => {
        const aHeight = parseInt(a.replace('p', ''));
        const bHeight = parseInt(b.replace('p', ''));
        return bHeight - aHeight; // Descending order
      });
      setSelectedQuality(qualities[0]);
    }
  }, [streamingUrls, selectedQuality]);

  // Set initial subtitle
  useEffect(() => {
    if (subtitles.length > 0 && !selectedSubtitle) {
      const defaultSubtitle = subtitles.find(sub => sub.is_default);
      if (defaultSubtitle) {
        setSelectedSubtitle(defaultSubtitle.id);
      }
    }
  }, [subtitles, selectedSubtitle]);

  // Video event handlers
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setIsLoading(false);
      
      // Set initial time if provided
      if (initialTime > 0) {
        videoRef.current.currentTime = initialTime;
        setCurrentTime(initialTime);
      }
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime;
      setCurrentTime(current);
      
      // Report progress
      if (onProgressUpdate && duration > 0) {
        onProgressUpdate(current, duration);
      }
    }
  };

  const handlePlay = () => {
    setIsPlaying(true);
    resetControlsTimeout();
  };

  const handlePause = () => {
    setIsPlaying(false);
    setShowControls(true);
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
  };

  const handleError = () => {
    setError(t('video.playbackError'));
    setIsLoading(false);
  };

  // Control handlers
  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setIsMuted(newVolume === 0);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      if (isMuted) {
        videoRef.current.volume = volume;
        setIsMuted(false);
      } else {
        videoRef.current.volume = 0;
        setIsMuted(true);
      }
    }
  };

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (progressRef.current && videoRef.current && duration > 0) {
      const rect = progressRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const newTime = (clickX / rect.width) * duration;
      
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const skip = (seconds: number) => {
    if (videoRef.current) {
      const newTime = Math.max(0, Math.min(duration, currentTime + seconds));
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const changeQuality = (quality: string) => {
    if (videoRef.current && streamingUrls[quality]) {
      const currentTime = videoRef.current.currentTime;
      const wasPlaying = !videoRef.current.paused;
      
      setSelectedQuality(quality);
      setIsLoading(true);
      
      // Change source and restore position
      videoRef.current.src = streamingUrls[quality].url;
      videoRef.current.currentTime = currentTime;
      
      if (wasPlaying) {
        videoRef.current.play();
      }
    }
    setShowSettings(false);
  };

  const changeSubtitle = (subtitleId: string) => {
    setSelectedSubtitle(subtitleId);
    
    // Update video track
    if (videoRef.current) {
      const tracks = videoRef.current.textTracks;
      for (let i = 0; i < tracks.length; i++) {
        tracks[i].mode = tracks[i].id === subtitleId ? 'showing' : 'hidden';
      }
    }
    setShowSettings(false);
  };

  // Format time
  const formatTime = (time: number) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target === videoRef.current || containerRef.current?.contains(e.target as Node)) {
        switch (e.code) {
          case 'Space':
            e.preventDefault();
            togglePlayPause();
            break;
          case 'ArrowLeft':
            e.preventDefault();
            skip(-10);
            break;
          case 'ArrowRight':
            e.preventDefault();
            skip(10);
            break;
          case 'ArrowUp':
            e.preventDefault();
            handleVolumeChange(Math.min(1, volume + 0.1));
            break;
          case 'ArrowDown':
            e.preventDefault();
            handleVolumeChange(Math.max(0, volume - 0.1));
            break;
          case 'KeyM':
            e.preventDefault();
            toggleMute();
            break;
          case 'KeyF':
            e.preventDefault();
            toggleFullscreen();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [volume, togglePlayPause, skip, handleVolumeChange, toggleMute, toggleFullscreen]);

  // Mouse movement for controls
  useEffect(() => {
    const handleMouseMove = () => {
      resetControlsTimeout();
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      return () => container.removeEventListener('mousemove', handleMouseMove);
    }
  }, [resetControlsTimeout]);

  if (error) {
    return (
      <div className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}>
        <div className="flex items-center justify-center h-64 text-white">
          <div className="text-center">
            <div className="text-red-400 mb-2">{error}</div>
            <button
              onClick={() => {
                setError(null);
                setIsLoading(true);
                if (videoRef.current) {
                  videoRef.current.load();
                }
              }}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
            >
              {t('video.retry')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`relative bg-gray-900 rounded-lg overflow-hidden group ${className}`}
      tabIndex={0}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        className="w-full h-full"
        poster={thumbnail}
        preload="metadata"
        onLoadedMetadata={handleLoadedMetadata}
        onTimeUpdate={handleTimeUpdate}
        onPlay={handlePlay}
        onPause={handlePause}
        onError={handleError}
        onLoadStart={() => setIsLoading(true)}
        onCanPlay={() => setIsLoading(false)}
        src={selectedQuality ? streamingUrls[selectedQuality]?.url : ''}
        autoPlay={autoPlay}
      >
        {/* Subtitles */}
        {subtitles.map((subtitle) => (
          <track
            key={subtitle.id}
            id={subtitle.id}
            kind="subtitles"
            src={subtitle.subtitle_file}
            srcLang={subtitle.language_code}
            label={subtitle.language_name}
            default={subtitle.is_default}
          />
        ))}
      </video>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <Loader className="w-8 h-8 text-white animate-spin" />
        </div>
      )}

      {/* Controls Overlay */}
      <div
        className={`absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {/* Center Play Button */}
        {!isPlaying && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <button
              onClick={togglePlayPause}
              className="w-16 h-16 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors backdrop-blur-sm"
            >
              <Play className="w-8 h-8 text-white ml-1" />
            </button>
          </div>
        )}

        {/* Bottom Controls */}
        <div className="absolute bottom-0 left-0 right-0 p-4">
          {/* Progress Bar */}
          <div
            ref={progressRef}
            className="w-full h-2 bg-white/20 rounded-full cursor-pointer mb-4 group/progress"
            onClick={handleSeek}
          >
            <div
              className="h-full bg-blue-500 rounded-full relative group-hover/progress:bg-blue-400 transition-colors"
              style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
            >
              <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-blue-500 rounded-full opacity-0 group-hover/progress:opacity-100 transition-opacity" />
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Play/Pause */}
              <button
                onClick={togglePlayPause}
                className="text-white hover:text-blue-400 transition-colors"
              >
                {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
              </button>

              {/* Skip Buttons */}
              <button
                onClick={() => skip(-10)}
                className="text-white hover:text-blue-400 transition-colors"
              >
                <SkipBack className="w-5 h-5" />
              </button>
              <button
                onClick={() => skip(10)}
                className="text-white hover:text-blue-400 transition-colors"
              >
                <SkipForward className="w-5 h-5" />
              </button>

              {/* Volume */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  {isMuted || volume === 0 ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={isMuted ? 0 : volume}
                  onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                  className="w-20 h-1 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* Time */}
              <div className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Settings */}
              <div className="relative">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  <Settings className="w-5 h-5" />
                </button>

                {/* Settings Menu */}
                {showSettings && (
                  <div className="absolute bottom-8 right-0 bg-black/90 backdrop-blur-sm rounded-lg p-4 min-w-48">
                    {/* Quality Settings */}
                    <div className="mb-4">
                      <div className="text-white text-sm font-medium mb-2">{t('video.quality')}</div>
                      {Object.entries(streamingUrls).map(([quality, info]) => (
                        <button
                          key={quality}
                          onClick={() => changeQuality(quality)}
                          className={`block w-full text-left px-2 py-1 text-sm rounded transition-colors ${
                            selectedQuality === quality
                              ? 'bg-blue-600 text-white'
                              : 'text-gray-300 hover:bg-white/10'
                          }`}
                        >
                          {quality} ({info.resolution})
                        </button>
                      ))}
                    </div>

                    {/* Subtitle Settings */}
                    {subtitles.length > 0 && (
                      <div>
                        <div className="text-white text-sm font-medium mb-2">{t('video.subtitles')}</div>
                        <button
                          onClick={() => changeSubtitle('')}
                          className={`block w-full text-left px-2 py-1 text-sm rounded transition-colors ${
                            selectedSubtitle === ''
                              ? 'bg-blue-600 text-white'
                              : 'text-gray-300 hover:bg-white/10'
                          }`}
                        >
                          {t('video.noSubtitles')}
                        </button>
                        {subtitles.map((subtitle) => (
                          <button
                            key={subtitle.id}
                            onClick={() => changeSubtitle(subtitle.id)}
                            className={`block w-full text-left px-2 py-1 text-sm rounded transition-colors ${
                              selectedSubtitle === subtitle.id
                                ? 'bg-blue-600 text-white'
                                : 'text-gray-300 hover:bg-white/10'
                            }`}
                          >
                            {subtitle.language_name}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Fullscreen */}
              <button
                onClick={toggleFullscreen}
                className="text-white hover:text-blue-400 transition-colors"
              >
                <Maximize className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
