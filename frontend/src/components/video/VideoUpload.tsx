import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';
import { apiService } from '../../services/api';
import {
  Upload,
  X,
  FileVideo,
  AlertCircle,
  CheckCircle,
  Loader,
  Plus
} from 'lucide-react';

interface VideoFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  videoId?: string;
}

interface VideoUploadProps {
  courseId: string;
  moduleId?: string;
  lessonId?: string;
  onUploadComplete?: (videos: any[]) => void;
  onUploadStart?: () => void;
  maxFiles?: number;
  className?: string;
}

const VideoUpload: React.FC<VideoUploadProps> = ({
  courseId,
  moduleId,
  lessonId,
  onUploadComplete,
  onUploadStart,
  maxFiles = 10,
  className = ''
}) => {
  const { t } = useTranslation();
  const { token } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [videos, setVideos] = useState<VideoFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // File validation
  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/mkv', 'video/webm', 'video/flv'];
    if (!allowedTypes.includes(file.type)) {
      return t('video.upload.invalidFormat');
    }

    // Check file size (2GB max)
    const maxSize = 2 * 1024 * 1024 * 1024; // 2GB
    if (file.size > maxSize) {
      return t('video.upload.fileTooLarge');
    }

    return null;
  };

  // Add files to upload queue
  const addFiles = (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newVideos: VideoFile[] = [];

    for (const file of fileArray) {
      // Check if we've reached max files
      if (videos.length + newVideos.length >= maxFiles) {
        break;
      }

      // Validate file
      const error = validateFile(file);
      if (error) {
        // Show error for invalid files
        newVideos.push({
          file,
          id: Math.random().toString(36).substr(2, 9),
          progress: 0,
          status: 'error',
          error
        });
        continue;
      }

      newVideos.push({
        file,
        id: Math.random().toString(36).substr(2, 9),
        progress: 0,
        status: 'pending'
      });
    }

    setVideos(prev => [...prev, ...newVideos]);
  };

  // Handle file input change
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      addFiles(e.target.files);
    }
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (e.dataTransfer.files) {
      addFiles(e.dataTransfer.files);
    }
  };

  // Remove video from queue
  const removeVideo = (id: string) => {
    setVideos(prev => prev.filter(video => video.id !== id));
  };

  // Upload single video
  const uploadVideo = async (video: VideoFile): Promise<void> => {
    if (!token) {
      throw new Error(t('video.upload.authRequired'));
    }

    const formData = new FormData();
    formData.append('title', video.file.name.replace(/\.[^/.]+$/, '')); // Remove extension
    formData.append('original_file', video.file);
    formData.append('course', courseId);

    if (moduleId) {
      formData.append('module', moduleId);
    }
    if (lessonId) {
      formData.append('lesson', lessonId);
    }

    try {
      // Set token for API service
      apiService.setToken(token);

      // Use fetch directly for FormData uploads (apiService doesn't handle FormData well)
      const response = await fetch('http://localhost:8000/api/video-streaming/videos/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || t('video.upload.uploadFailed'));
      }

      const result = await response.json();

      // Update video status
      setVideos(prev => prev.map(v =>
        v.id === video.id
          ? { ...v, status: 'processing', progress: 100, videoId: result.id }
          : v
      ));

      return result;
    } catch (error) {
      console.error('Video upload failed:', error);
      const errorMessage = error instanceof Error ? error.message : t('video.upload.uploadFailed');

      // Update video with error
      setVideos(prev => prev.map(v =>
        v.id === video.id
          ? { ...v, status: 'error', error: errorMessage }
          : v
      ));

      dispatch(addNotification({
        type: 'error',
        title: t('video.upload.error'),
        message: errorMessage
      }));

      throw error;
    }
  };

  // Upload all pending videos
  const uploadAllVideos = async () => {
    const pendingVideos = videos.filter(v => v.status === 'pending');
    if (pendingVideos.length === 0) return;

    setIsUploading(true);
    onUploadStart?.();

    const uploadPromises = pendingVideos.map(async (video) => {
      // Update status to uploading
      setVideos(prev => prev.map(v => 
        v.id === video.id ? { ...v, status: 'uploading' as const } : v
      ));

      try {
        return await uploadVideo(video);
      } catch (error) {
        console.error('Upload failed for video:', video.file.name, error);
        return null;
      }
    });

    try {
      const results = await Promise.allSettled(uploadPromises);
      const successfulUploads = results
        .filter((result): result is PromiseFulfilledResult<any> => 
          result.status === 'fulfilled' && result.value !== null
        )
        .map(result => result.value);

      if (successfulUploads.length > 0) {
        onUploadComplete?.(successfulUploads);
      }

      // Mark completed videos
      setVideos(prev => prev.map(v => 
        v.status === 'uploading' && !v.error ? { ...v, status: 'completed' as const } : v
      ));

    } finally {
      setIsUploading(false);
    }
  };

  // Clear all videos
  const clearAll = () => {
    setVideos([]);
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Get status icon
  const getStatusIcon = (status: VideoFile['status']) => {
    switch (status) {
      case 'pending':
        return <FileVideo className="w-5 h-5 text-gray-400" />;
      case 'uploading':
      case 'processing':
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <FileVideo className="w-5 h-5 text-gray-400" />;
    }
  };

  // Get status text
  const getStatusText = (video: VideoFile) => {
    switch (video.status) {
      case 'pending':
        return t('video.upload.pending');
      case 'uploading':
        return t('video.upload.uploading');
      case 'processing':
        return t('video.upload.processing');
      case 'completed':
        return t('video.upload.completed');
      case 'error':
        return video.error || t('video.upload.error');
      default:
        return '';
    }
  };

  const pendingCount = videos.filter(v => v.status === 'pending').length;
  const completedCount = videos.filter(v => v.status === 'completed').length;
  const errorCount = videos.filter(v => v.status === 'error').length;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragOver
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t('video.upload.title')}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          {t('video.upload.description')}
        </p>
        
        <button
          onClick={() => fileInputRef.current?.click()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          {t('video.upload.selectFiles')}
        </button>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="video/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
          {t('video.upload.supportedFormats')}: MP4, AVI, MOV, MKV, WebM, FLV
          <br />
          {t('video.upload.maxSize')}: 2GB {t('video.upload.perFile')}
        </p>
      </div>

      {/* Video Queue */}
      {videos.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                {t('video.upload.queue')} ({videos.length})
              </h4>
              <div className="flex items-center space-x-4">
                {pendingCount > 0 && (
                  <button
                    onClick={uploadAllVideos}
                    disabled={isUploading}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                  >
                    {isUploading ? (
                      <>
                        <Loader className="w-4 h-4 mr-2 animate-spin" />
                        {t('video.upload.uploading')}
                      </>
                    ) : (
                      t('video.upload.uploadAll')
                    )}
                  </button>
                )}
                <button
                  onClick={clearAll}
                  disabled={isUploading}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                >
                  {t('video.upload.clearAll')}
                </button>
              </div>
            </div>
            
            {/* Status Summary */}
            <div className="flex items-center space-x-6 mt-2 text-sm">
              {pendingCount > 0 && (
                <span className="text-gray-600 dark:text-gray-400">
                  {pendingCount} {t('video.upload.pending')}
                </span>
              )}
              {completedCount > 0 && (
                <span className="text-green-600">
                  {completedCount} {t('video.upload.completed')}
                </span>
              )}
              {errorCount > 0 && (
                <span className="text-red-600">
                  {errorCount} {t('video.upload.failed')}
                </span>
              )}
            </div>
          </div>

          {/* Video List */}
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {videos.map((video) => (
              <div key={video.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    {getStatusIcon(video.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {video.file.name}
                      </p>
                      <div className="flex items-center space-x-4 mt-1">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {formatFileSize(video.file.size)}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {getStatusText(video)}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => removeVideo(video.id)}
                    disabled={video.status === 'uploading'}
                    className="p-1 text-gray-400 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
                
                {/* Progress Bar */}
                {(video.status === 'uploading' || video.status === 'processing') && (
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${video.progress}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoUpload;
