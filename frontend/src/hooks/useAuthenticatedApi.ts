import React, { useEffect } from 'react';
import { useAppSelector } from '../store';
import { apiService } from '../services/api';

/**
 * Custom hook that ensures API service has authentication token set
 * This hook automatically sets the token whenever it changes in the auth store
 */
export const useAuthenticatedApi = () => {
  const { token } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (token) {
      apiService.setToken(token);
      console.log('🔑 API token updated from auth store');
    } else {
      // Try to get token from localStorage as fallback
      const storedToken = localStorage.getItem('token');
      if (storedToken) {
        apiService.setToken(storedToken);
        console.log('🔑 API token set from localStorage');
      }
    }
  }, [token]);

  return { token, isAuthenticated: !!token };
};


