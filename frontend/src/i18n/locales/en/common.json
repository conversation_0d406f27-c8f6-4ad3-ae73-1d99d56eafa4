{"app": {"name": "University Management & Learning System", "shortName": "UMLS", "description": "Comprehensive university management system with Arabic support"}, "navigation": {"dashboard": "Dashboard", "courses": "Courses", "assignments": "Assignments", "assessments": "Assessments", "students": "Students", "teachers": "Teachers", "reports": "Reports", "settings": "Settings", "profile": "Profile", "schedule": "Schedule", "grades": "Grades", "payments": "Payments", "invoices": "Invoices", "library": "Library", "messaging": "Messages", "notifications": "Notifications", "exams": "<PERSON><PERSON>", "attendance": "Attendance", "departments": "Departments", "academicCalendar": "Academic Calendar", "studyGroups": "Study Groups", "users": "Users", "system": "System", "tasks": "Tasks", "documents": "Documents", "requests": "Requests", "customCharts": "Custom Charts", "studentCard": "Student ID Card", "staffVerification": "Staff Verification", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember Me", "loginButton": "Sign In", "registerButton": "Create Account", "loginSuccess": "Login successful", "loginError": "Invalid credentials", "registerSuccess": "Account created successfully", "registerError": "Registration failed", "demoCredentials": "Demo Credentials:", "loginWelcome": "Welcome back! Please sign in to your account."}, "roles": {"super_admin": "Super Admin", "admin": "Admin", "teacher": "Teacher", "student": "Student", "parent": "Parent", "staff": "Staff", "finance_officer": "Finance Officer"}, "dashboard": {"welcome": "Welcome back", "overview": "Overview", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "statistics": "Statistics", "notifications": "Notifications", "adminDescription": "Administrator Dash<PERSON>", "totalStudents": "Total Students", "activeCourses": "Active Courses", "pendingApplications": "Pending Applications", "enrollmentRate": "Enrollment Rate", "administrativeOverview": "Administrative Overview", "adminOverviewDescription": "University administration tools and analytics will be available here.", "enrolledCourses": "Enrolled Courses", "assignments": "Assignments", "upcomingExams": "Upcoming Exams", "gpa": "Current GPA", "studentWelcomeMessage": "Ready to continue your learning journey?", "viewCourses": "View Courses", "submitAssignment": "Submit Assignment", "viewSchedule": "View Schedule", "studyGroups": "Study Groups", "upcomingDeadlines": "Upcoming Deadlines", "mathAssignment": "Mathematics Assignment #3", "dueInDays": "Due in 2 days", "hoursAgo": "2 hours ago", "physicsQuiz": "Physics Quiz Result", "gradeResult": "Grade: A- (87%)", "dayAgo": "1 day ago", "scheduleUpdate": "Class Schedule Update", "labMoved": "Chemistry lab moved to Room 205", "daysAgo": "2 days ago", "mathAssignmentShort": "Math Assignment", "urgent": "<PERSON><PERSON>", "physicsLabReport": "Physics Lab Report", "dueInFiveDays": "Due in 5 days", "soon": "Soon", "historyEssay": "History Essay", "dueInWeek": "Due in 1 week", "onTrack": "On Track", "teacherDescription": "Teacher Dashboard", "myCourses": "My Courses", "assignmentsToGrade": "Assignments to Grade", "classesToday": "Classes Today", "teachingOverview": "Teaching Overview", "teachingOverviewDescription": "Course management, grading, and student interaction tools will be available here.", "superAdminDescription": "Super Administrator Dashboard", "totalUsers": "Total Users", "systemHealth": "System Health", "excellent": "Excellent", "activeSessions": "Active Sessions", "systemOverview": "System Overview", "systemOverviewDescription": "Complete system administration and monitoring capabilities will be available here.", "staffDescription": "Staff Dashboard", "staffOverviewDescription": "Administrative support tools and task management will be available here.", "vsLastMonth": "vs last month", "securityAlerts": "Security Alerts", "errorLoading": "Error Loading Dashboard"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "search": "Search", "filter": "Filter", "sort": "Sort", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "upload": "Upload", "download": "Download", "print": "Print", "export": "Export", "import": "Import", "refresh": "Refresh", "actions": "Actions", "viewAll": "View All", "markAsRead": "<PERSON> <PERSON>", "selectAll": "Select All", "share": "Share", "hideQR": "Hide QR", "showQR": "Show QR", "exportImage": "Export Image", "exportPDF": "Export PDF", "verify": "Verify", "show": "Show", "hide": "<PERSON>de"}, "forms": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "invalidPhone": "Please enter a valid phone number", "invalidDate": "Please enter a valid date", "fileTooLarge": "File size is too large", "invalidFileType": "Invalid file type"}, "language": {"english": "English", "arabic": "العربية", "switchLanguage": "Switch Language"}, "courses": {"studentDescription": "View your enrolled courses and progress", "teacherDescription": "Manage your courses and students", "adminDescription": "Manage all university courses", "addCourse": "Add Course", "searchPlaceholder": "Search courses, codes, or instructors...", "active": "Active", "upcoming": "Upcoming", "completed": "Completed", "students": "students", "credits": "credits", "enter": "Enter", "noCourses": "No courses found", "noSearchResults": "Try adjusting your search terms", "noCoursesDescription": "No courses available at the moment"}, "assignments": {"studentDescription": "View and submit your assignments", "teacherDescription": "Manage course assignments and grades", "create": "Create Assignment", "filterByStatus": "Filter by Status", "filterByCourse": "Filter by Course", "pending": "Pending", "submitted": "Submitted", "graded": "Graded", "overdue": "Overdue", "due": "Due", "points": "points", "submittedAt": "Submitted", "grade": "Grade", "submit": "Submit", "viewSubmission": "View Submission", "viewSubmissions": "View Submissions", "noAssignments": "No assignments found", "noAssignmentsDescription": "No assignments match your current filters", "homework": "Homework", "project": "Project", "quiz": "Quiz", "exam": "Exam", "editAssignment": "Edit Assignment", "createAssignment": "Create Assignment", "submitAssignment": "Submit Assignment", "editSubmission": "Edit Submission", "takeExam": "Take <PERSON>am", "viewDetails": "View Details", "allowed": "Allowed", "lateSubmission": "Late Submission", "maxPoints": "Max Points", "attachments": "Attachments"}, "students": {"teacherDescription": "View and manage your course students", "adminDescription": "Manage all university students", "addStudent": "Add Student", "searchPlaceholder": "Search students...", "allStatuses": "All Statuses", "active": "Active", "inactive": "Inactive", "graduated": "Graduated", "suspended": "Suspended", "allYears": "All Years", "year1": "Year 1", "year2": "Year 2", "year3": "Year 3", "year4": "Year 4", "year": "Year", "gpa": "GPA", "courses": "courses", "noStudents": "No students found", "noSearchResults": "Try adjusting your search terms", "noStudentsDescription": "No students match your current filters", "idCard": "Student ID Card", "cardDescription": "Your official university identification card", "cardInformation": "Card Information", "fullName": "Full Name", "studentId": "Student ID", "department": "Department", "email": "Email", "phone": "Phone", "enrollmentDate": "Enrollment Date", "howToUse": "How to Use Your Student Card", "usage1": "Present this card for identification on campus", "usage2": "Use the QR code for quick digital verification", "usage3": "Export as PDF or image for digital use", "usage4": "Print for physical card backup"}, "qr": {"verification": "QR Code Verification", "description": "Verify student ID cards using QR codes", "enterCode": "Enter QR Code", "useDemoCode": "Use Demo QR Code", "validCard": "Valid Student Card", "invalidCard": "Invalid Card", "accessGranted": "Access Granted - Valid Student Card", "accessDenied": "Access Denied - Invalid Card", "invalidReason": "Invalid or expired card", "howToUse": "How to Use", "instruction1": "Scan the QR code from a student ID card", "instruction2": "Enter the QR code text in the input field above", "instruction3": "Click Verify to check the card validity", "instruction4": "Valid cards will show student information"}, "staff": {"verification": "Staff Verification System", "description": "Verify student ID cards and manage access control", "authorized": "Authorized Staff Member"}, "verification": {"scanner": "QR Code Scanner", "quickTest": "Quick Test Codes:", "accessGranted": "ACCESS GRANTED", "accessDenied": "ACCESS DENIED", "validStudent": "Valid Student - Access Granted", "invalidCard": "Invalid Card - Access Denied", "cardExpires": "Card expires", "defaultError": "Card verification failed", "recentLog": "Recent Verifications", "noLogs": "No verifications yet", "instructions": "Instructions", "step1": "Scan QR code from student ID card", "step2": "Enter code in the input field", "step3": "Click Verify to check validity", "step4": "Grant or deny access based on result", "todayStats": "Today's Stats", "totalChecks": "Total Checks", "validCards": "Valid Cards", "invalidCards": "Invalid Cards", "noPermission": "You do not have permission to verify student cards."}, "access": {"denied": "Access Denied"}, "card": {"issued": "Card Issued", "expires": "Card Expires"}, "student": {"name": "Name", "studentId": "Student ID", "email": "Email", "department": "Department"}, "teachers": {"description": "Manage faculty members and teaching staff", "addTeacher": "Add Teacher", "searchPlaceholder": "Search teachers...", "allDepartments": "All Departments", "allStatuses": "All Statuses", "active": "Active", "inactive": "Inactive", "onLeave": "On Leave", "title": "Title", "department": "Department", "specialization": "Specialization", "experience": "Experience", "years": "years", "joinedOn": "Joined", "courses": "Courses", "students": "Students", "totalTeachers": "Total Teachers", "activeTeachers": "Active Teachers", "totalCourses": "Total Courses", "totalStudents": "Total Students", "noTeachers": "No teachers found", "noSearchResults": "Try adjusting your search terms", "noTeachersDescription": "No teachers match your current filters"}, "payments": {"description": "Manage student payments and billing", "addPayment": "Add Payment", "export": "Export", "searchPlaceholder": "Search payments...", "allStatuses": "All Statuses", "completed": "Completed", "pending": "Pending", "overdue": "Overdue", "cancelled": "Cancelled", "allTypes": "All Types", "tuition": "Tuition", "labFee": "Lab Fee", "libraryFee": "Library Fee", "registration": "Registration", "other": "Other", "student": "Student", "amount": "Amount", "type": "Type", "status": "Status", "dueDate": "Due Date", "totalCollected": "Total Collected", "totalPending": "Total Pending", "totalOverdue": "Total Overdue", "thisMonth": "This Month", "noPayments": "No payments found", "noSearchResults": "Try adjusting your search terms", "noPaymentsDescription": "No payments match your current filters"}, "grades": {"exportSuccess": "Transcript Exported", "exportSuccessMessage": "Your transcript has been downloaded successfully", "exportError": "Export Failed", "exporting": "Exporting..."}, "reports": {"description": "Generate and download comprehensive reports", "customReport": "Custom Report", "category": "Category", "allCategories": "All Categories", "student": "Student", "course": "Course", "financial": "Financial", "administrative": "Administrative", "type": "Type", "allTypes": "All Types", "academic": "Academic", "enrollment": "Enrollment", "attendance": "Attendance", "performance": "Performance", "totalReports": "Total Reports", "generatedToday": "Generated Today", "scheduledReports": "Scheduled Reports", "customReports": "Custom Reports", "frequency": "Frequency", "lastGenerated": "Last Generated", "generate": "Generate", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "semester": "<PERSON><PERSON><PERSON>", "annual": "Annual", "recentActivity": "Recent Report Activity", "noReports": "No reports found", "noReportsDescription": "No reports match your current filters", "accessDenied": "Access Denied", "accessDeniedDescription": "You do not have permission to view reports"}, "notifications": {"title": "Notifications", "description": "Stay updated with important announcements and alerts", "markAsRead": "<PERSON> <PERSON>", "delete": "Delete", "filterByType": "Filter by Type", "filterByStatus": "Filter by Status", "allTypes": "All Types", "info": "Info", "warning": "Warning", "success": "Success", "error": "Error", "announcement": "Announcement", "allStatuses": "All", "unread": "Unread", "read": "Read", "selectAll": "Select All", "total": "Total", "thisWeek": "This Week", "urgent": "<PERSON><PERSON>", "justNow": "Just now", "hoursAgo": "hours ago", "daysAgo": "days ago", "academic": "Academic", "system": "System", "personal": "Personal", "noNotifications": "No notifications found", "noNotificationsDescription": "You're all caught up! No notifications match your current filters."}, "messaging": {"title": "Messages", "description": "Send and receive messages with other users", "messages": "Messages", "conversations": "Conversations", "newConversation": "New Conversation", "startConversation": "Start a conversation", "selectConversation": "Select a conversation", "selectConversationDescription": "Choose a conversation from the sidebar to start messaging", "directMessage": "Direct Message", "groupChat": "Group Chat", "courseDiscussion": "Course Discussion", "participants": "participants", "typeMessage": "Type a message...", "sendMessage": "Send Message", "searchConversations": "Search conversations...", "noConversations": "No conversations yet", "unread": "unread", "edited": "edited", "replyingTo": "Replying to", "filesSelected": "files selected", "addParticipant": "Add Participant", "removeParticipant": "Remove Participant", "leaveConversation": "Leave Conversation", "conversationSettings": "Conversation Settings", "markAsRead": "<PERSON> as read", "deleteMessage": "Delete message", "editMessage": "Edit message", "replyToMessage": "Reply to message", "pinMessage": "Pin message", "unpinMessage": "Unpin message", "forwardMessage": "Forward message", "copyMessage": "Copy message", "downloadFile": "Download file", "viewFile": "View file", "fileAttachment": "File attachment", "imageAttachment": "Image attachment", "voiceMessage": "Voice message", "videoCall": "Video call", "audioCall": "Audio call", "shareScreen": "Share screen", "mute": "Mute", "unmute": "Unmute", "online": "Online", "offline": "Offline", "away": "Away", "busy": "Busy", "lastSeen": "Last seen", "typing": "typing...", "isTyping": "is typing...", "areTyping": "are typing...", "sendError": "Send Failed", "sendErrorMessage": "Failed to send message", "fetchConversationsError": "Failed to fetch conversations", "fetchMessagesError": "Failed to fetch messages", "error": "Error", "success": "Success", "messageDeleted": "Message deleted", "messageSent": "Message sent", "conversationCreated": "Conversation created", "participantAdded": "Participant added", "participantRemoved": "Participant removed", "leftConversation": "You left the conversation", "joinedConversation": "joined the conversation", "addedToConversation": "was added to the conversation", "removedFromConversation": "was removed from the conversation", "conversationTitleChanged": "Conversation title changed", "attachFile": "Attach file", "attachImage": "Attach image", "attachDocument": "Attach document", "recordVoice": "Record voice message", "takePhoto": "Take photo", "chooseFromGallery": "Choose from gallery", "maxFileSize": "Maximum file size: 10MB", "supportedFormats": "Supported formats", "fileTooLarge": "File is too large", "invalidFileType": "Invalid file type", "uploadFailed": "Upload failed", "uploadSuccess": "Upload successful", "downloading": "Downloading...", "downloadFailed": "Download failed", "downloadSuccess": "Download complete", "selectParticipants": "Select participants to start messaging", "conversationType": "Conversation Type", "groupTitle": "Group Title", "enterGroupTitle": "Enter group title...", "searchUsers": "Search Users", "searchUsersPlaceholder": "Search by name or username...", "selectedParticipants": "Selected Participants", "availableUsers": "Available Users", "noUsersFound": "No users found", "createConversation": "Create Conversation", "createConversationError": "Failed to create conversation", "startNewConversation": "Start new conversation", "new": "New", "startConversationDescription": "Start a conversation with your classmates or teachers"}, "library": {"title": "Library", "description": "Browse and manage library resources", "addBook": "Add Book", "searchPlaceholder": "Search books, authors, ISBN...", "allCategories": "All Categories", "allStatuses": "All Statuses", "available": "Available", "borrowed": "Borrowed", "reserved": "Reserved", "maintenance": "Maintenance", "allLanguages": "All Languages", "english": "English", "arabic": "Arabic", "both": "Both", "totalBooks": "Total Books", "category": "Category", "publisher": "Publisher", "year": "Year", "pages": "Pages", "location": "Location", "borrowedBy": "Borrowed by", "dueDate": "Due", "reservedBy": "Reserved by", "borrow": "Borrow", "noBooks": "No books found", "noSearchResults": "Try adjusting your search terms", "noBooksDescription": "No books match your current filters"}, "schedule": {"studentDescription": "View your class schedule and important dates", "teacherDescription": "Manage your teaching schedule and events", "addEvent": "Add Event", "today": "Today", "week": "Week", "month": "Month", "upcomingEvents": "Upcoming Events", "monthViewComingSoon": "Month View Coming Soon", "monthViewDescription": "Month view is currently under development", "class": "Class", "meeting": "Meeting", "event": "Event"}, "settings": {"profile": "Profile", "notifications": "Notifications", "language": "Language & Region", "security": "Security", "system": "System", "description": "Manage your account settings and preferences", "profileSettings": "Profile Settings", "notificationSettings": "Notification Settings", "languageSettings": "Language & Region Settings", "securitySettings": "Security Settings", "systemSettings": "System Settings", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive notifications via email", "smsNotifications": "SMS Notifications", "smsNotificationsDesc": "Receive notifications via SMS", "pushNotifications": "Push Notifications", "pushNotificationsDesc": "Receive push notifications in browser", "preferredLanguage": "Preferred Language", "theme": "Theme", "light": "Light", "dark": "Dark", "auto": "Auto", "changePassword": "Change Password", "changePasswordDesc": "Update your password to keep your account secure", "changePasswordButton": "Change Password", "twoFactor": "Two-Factor Authentication", "twoFactorDesc": "Add an extra layer of security to your account", "enableTwoFactor": "Enable 2FA", "systemMaintenance": "System Maintenance", "systemMaintenanceDesc": "Manage system-wide settings and maintenance", "accessSystemSettings": "Access System Settings"}, "profile": {"firstName": "First Name", "lastName": "Last Name", "firstNameAr": "First Name (Arabic)", "lastNameAr": "Last Name (Arabic)", "email": "Email", "phone": "Phone"}, "finance": {"welcomeMessage": "Manage university finances and student payments", "totalRevenue": "Total Revenue", "pendingPayments": "Pending Payments", "paidStudents": "Paid Students", "overduePayments": "Overdue Payments", "recentTransactions": "Recent Transactions", "processPayment": "Process Payment", "generateInvoice": "Generate Invoice", "viewReports": "View Financial Reports", "manageStudentAccounts": "Manage Student Accounts", "paymentSummary": "Payment Summary", "collectedToday": "Collected Today", "pendingToday": "Pending Today", "overdueTotal": "Total Overdue", "monthlyTarget": "Monthly Collection Target", "target": "Target", "collected": "Collected", "completed": "completed"}, "exams": {"studentDescription": "View your upcoming exams and results", "teacherDescription": "Manage exams and assessments", "createExam": "Create Exam", "filterByStatus": "Filter by Status", "filterByType": "Filter by Type", "scheduled": "Scheduled", "ongoing": "Ongoing", "completed": "Completed", "cancelled": "Cancelled", "midterm": "Midterm", "final": "Final", "quiz": "Quiz", "practical": "Practical", "oral": "Oral", "listView": "List View", "calendarView": "Calendar View", "totalExams": "Total Exams", "totalStudents": "Total Students", "students": "students", "marks": "marks", "instructions": "Instructions", "viewDetails": "View Details", "noExams": "No exams found", "noExamsDescription": "No exams match your current filters"}, "attendance": {"studentDescription": "Track your class attendance and participation", "teacherDescription": "Manage student attendance and track participation", "takeAttendance": "Take Attendance", "selectDate": "Select Date", "selectCourse": "Select Course", "dailyView": "Daily View", "summaryView": "Summary View", "totalClasses": "Total Classes", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused", "attendanceRate": "Attendance Rate", "totalStudents": "Total Students", "myAttendance": "My Attendance Record", "dailyAttendance": "Daily Attendance", "attendanceWarnings": "Attendance Warnings", "noRecords": "No attendance records found", "noRecordsDescription": "No attendance records for the selected date and course"}, "departments": {"description": "Manage university departments and academic units", "addDepartment": "Add Department", "searchPlaceholder": "Search departments...", "allFaculties": "All Faculties", "exportData": "Export Data", "totalDepartments": "Total Departments", "totalTeachers": "Total Teachers", "totalStudents": "Total Students", "totalCourses": "Total Courses", "headOfDepartment": "Head of Department", "teachers": "Teachers", "students": "Students", "courses": "Courses", "location": "Location", "budget": "Annual Budget", "established": "Established", "active": "Active", "facultyOverview": "Faculty Overview", "departments": "departments", "noDepartments": "No departments found", "noSearchResults": "Try adjusting your search terms", "noDepartmentsDescription": "No departments match your current filters"}, "calendar": {"description": "View important academic dates and university events", "export": "Export Calendar", "addEvent": "Add Event", "month": "Month", "year": "Year", "filterByType": "Filter by Type", "monthView": "Month View", "listView": "List View", "totalEvents": "Total Events", "exams": "<PERSON><PERSON>", "holidays": "Holidays", "deadlines": "Deadlines", "eventsFor": "Events for", "semester": "<PERSON><PERSON><PERSON>", "exam": "Exam", "holiday": "Holiday", "registration": "Registration", "graduation": "Graduation", "orientation": "Orientation", "deadline": "Deadline", "audience": "Audience", "priority": "Priority", "all": "All", "students": "Students", "faculty": "Faculty", "staff": "Staff", "high": "High", "medium": "Medium", "low": "Low", "upcomingImportant": "Upcoming Important Events", "noEvents": "No events found", "noEventsDescription": "No events scheduled for the selected month and filters", "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}}, "users": {"description": "Manage system users and their permissions", "addUser": "Add User", "searchPlaceholder": "Search users...", "allRoles": "All Roles", "allStatuses": "All Statuses", "exportUsers": "Export Users", "totalUsers": "Total Users", "activeUsers": "Active Users", "students": "Students", "faculty": "Faculty", "user": "User", "role": "Role", "department": "Department", "status": "Status", "lastLogin": "Last Login", "active": "Active", "inactive": "Inactive", "suspended": "Suspended", "pending": "Pending", "neverLoggedIn": "Never logged in", "justNow": "Just now", "hoursAgo": "hours ago", "yesterday": "Yesterday", "noUsers": "No users found", "noSearchResults": "Try adjusting your search terms", "noUsersDescription": "No users match your current filters"}, "system": {"description": "System administration and monitoring", "exportLogs": "Export Logs", "backup": "Create Backup", "overview": "Overview", "database": "Database", "security": "Security", "notifications": "Notifications", "maintenance": "Maintenance", "accessDenied": "Access Denied", "accessDeniedDescription": "You do not have permission to access system settings", "systemInformation": "System Information", "serverVersion": "Server Version", "databaseVersion": "Database Version", "environment": "Environment", "serverSpecs": "Server Specifications", "storage": "Storage", "lastBackup": "Last Backup", "databaseManagement": "Database Management", "databaseBackup": "Database Backup", "backupDescription": "Create a full backup of the database", "createBackup": "Create Backup", "databaseOptimize": "Optimize Database", "optimizeDescription": "Optimize database performance", "optimize": "Optimize", "databaseRestore": "Restore Database", "restoreDescription": "Restore from a backup file", "restore": "Rest<PERSON>", "databaseMaintenance": "Maintenance Mode", "maintenanceDescription": "Enable maintenance mode", "enableMaintenance": "Enable", "securitySettings": "Security Settings", "twoFactorAuth": "Two-Factor Authentication", "twoFactorDescription": "Require 2FA for admin accounts", "sessionTimeout": "Session Timeout", "sessionTimeoutDescription": "Auto-logout after inactivity", "passwordPolicy": "Password Policy", "passwordPolicyDescription": "Enforce strong passwords", "notificationSettings": "Notification Settings", "emailNotifications": "Email Notifications", "emailNotificationsDescription": "Send system alerts via email", "emailSettings": "<PERSON><PERSON>s", "smtpServer": "SMTP Server", "smtpPort": "SMTP Port", "maintenanceTasks": "Maintenance Tasks", "clearCache": "<PERSON>ache", "clearCacheDescription": "Clear application cache", "updateSystem": "Update System", "updateSystemDescription": "Check for system updates", "cleanupLogs": "Cleanup Logs", "cleanupLogsDescription": "Remove old log files", "restartSystem": "Restart System", "restartSystemDescription": "Restart the application server"}, "tasks": {"description": "Manage and track administrative tasks", "addTask": "Add Task", "searchPlaceholder": "Search tasks...", "allStatuses": "All Statuses", "allPriorities": "All Priorities", "allCategories": "All Categories", "exportTasks": "Export", "totalTasks": "Total Tasks", "pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>", "administrative": "Administrative", "maintenance": "Maintenance", "academic": "Academic", "financial": "Financial", "other": "Other", "assignedTo": "Assigned to", "dueDate": "Due", "estimatedHours": "Estimated", "actualHours": "Actual", "overdue": "Overdue", "dueToday": "Due Today", "dueTomorrow": "Due Tomorrow", "daysLeft": "days left", "noTasks": "No tasks found", "noSearchResults": "Try adjusting your search terms", "noTasksDescription": "No tasks match your current filters"}, "documents": {"description": "Manage and organize university documents", "downloadAll": "Download All", "uploadDocument": "Upload Document", "searchPlaceholder": "Search documents...", "allCategories": "All Categories", "allTypes": "All Types", "advancedFilter": "Advanced Filter", "gridView": "Grid", "listView": "List", "totalDocuments": "Total Documents", "publicDocuments": "Public Documents", "totalDownloads": "Total Downloads", "totalSize": "Total Size", "academic": "Academic", "administrative": "Administrative", "financial": "Financial", "legal": "Legal", "studentRecords": "Student Records", "other": "Other", "name": "Name", "category": "Category", "size": "Size", "uploaded": "Uploaded", "downloads": "Downloads", "public": "Public", "private": "Private", "noDocuments": "No documents found", "noSearchResults": "Try adjusting your search terms", "noDocumentsDescription": "No documents match your current filters"}, "charts": {"count": "Count", "percentage": "Percentage", "usersByRole": "Users by Role", "usersByStatus": "Users by Status", "usersByDepartment": "Users by Department", "userRegistrationTrend": "User Registration Trend", "studentsByYear": "Students by Year", "studentsByStatus": "Students by Status", "studentsByDepartment": "Students by Department", "studentGPADistribution": "Student GPA Distribution", "enrollmentTrend": "Enrollment Trend", "courseCompletionRates": "Course Completion Rates", "departmentEnrollment": "Department Enrollment", "gradeDistribution": "Grade Distribution", "assignmentSubmissions": "Assignment Submissions", "courseEnrollment": "Course Enrollment", "gpaProgress": "GPA Progress", "currentGrades": "Current Grades", "attendanceRate": "Attendance Rate", "revenuetrend": "Revenue Trend", "paymentStatus": "Payment Status", "paymentMethods": "Payment Methods", "taskCompletion": "Task Completion Rate", "taskCategories": "Task Categories", "requestStatus": "Request Status", "systemActivity": "System Activity", "systemRevenue": "System Revenue", "systemUsage": "System Usage by Department", "systemHealth": "System Health", "noData": "No data available", "loading": "Loading chart data...", "total": "Total", "average": "Average", "uptime": "Uptime", "confirmDelete": "Are you sure you want to delete this chart?", "templates": "Templates", "createChart": "Create Chart", "noCharts": "No Custom Charts Yet", "noChartsDescription": "Create your first custom chart to get started with data visualization", "browseTemplates": "Browse Templates", "editChart": "Edit Chart", "createNewChart": "Create New Chart", "backToCharts": "Back to Charts"}, "studyGroups": {"title": "Study Groups", "description": "Join or create study groups to collaborate with your peers", "create": "Create Group", "join": "Join", "leave": "Leave", "manage": "Manage", "chat": "Cha<PERSON>", "private": "Private", "totalGroups": "Total Groups", "joinedGroups": "Joined Groups", "createdGroups": "Created Groups", "totalMembers": "Total Members", "allCourses": "All Courses", "allGroups": "All Groups", "searchPlaceholder": "Search study groups...", "noGroups": "No study groups found", "noGroupsDescription": "No study groups match your current filters", "createFirst": "Create the first group", "createdBy": "Created by", "full": "Full", "createGroup": "Create Study Group", "createFormPlaceholder": "Study group creation form will be implemented here.", "editGroup": "Edit Study Group", "groupName": "Group Name", "groupNamePlaceholder": "Enter group name", "maxMembers": "Max Members", "invalidMaxMembers": "Max members must be between 2 and 50", "courseCode": "Course Code", "courseName": "Course Name", "courseNamePlaceholder": "Introduction to Programming", "subject": "Subject", "subjectPlaceholder": "Programming", "meetingTime": "Meeting Time", "meetingTimePlaceholder": "Wednesdays 4:00 PM", "meetingLocation": "Meeting Location", "meetingLocationPlaceholder": "Library Study Room 3", "tags": "Tags", "addTagPlaceholder": "Add a tag", "addTag": "Add", "makePublic": "Make this group public (anyone can join)", "publicGroupNote": "Private groups require approval to join", "createSuccess": "Study Group Created", "createSuccessMessage": "Your study group has been created successfully", "createError": "Creation Failed", "updateSuccess": "Study Group Updated", "updateSuccessMessage": "Your study group has been updated successfully", "updateError": "Update Failed", "joinSuccess": "Joined Study Group", "joinSuccessMessage": "You have successfully joined the study group", "joinError": "Join Failed", "leaveSuccess": "Left Study Group", "leaveSuccessMessage": "You have successfully left the study group", "leaveError": "Leave Failed", "course": "Course", "coursePlaceholder": "Course name", "descriptionPlaceholder": "Describe your study group", "tagsPlaceholder": "e.g., programming, homework, exam-prep (comma separated)"}, "quickActions": {"submitAssignment": "Submit Assignment", "submitAssignmentDesc": "Upload your completed assignment", "viewGrades": "View Grades", "viewGradesDesc": "Check your latest grades and GPA", "joinStudyGroup": "Join Study Group", "joinStudyGroupDesc": "Find and join study groups", "checkSchedule": "Check Schedule", "checkScheduleDesc": "View your class schedule", "createAssignment": "Create Assignment", "createAssignmentDesc": "Create a new assignment for students", "gradeSubmissions": "Grade Submissions", "gradeSubmissionsDesc": "Review and grade student submissions", "viewAttendance": "View Attendance", "viewAttendanceDesc": "Check student attendance records", "sendAnnouncement": "Send Announcement", "sendAnnouncementDesc": "Send announcement to students", "manageUsers": "Manage Users", "manageUsersDesc": "Add, edit, or remove users", "viewReports": "View Reports", "viewReportsDesc": "Generate and view system reports", "manageCourses": "Manage Courses", "manageCoursesDesc": "Add or modify course information", "systemSettings": "System Settings", "systemSettingsDesc": "Configure system preferences", "processPayments": "Process Payments", "processPaymentsDesc": "Handle student payment transactions", "generateInvoices": "Generate Invoices", "generateInvoicesDesc": "Create invoices for students", "searchLibrary": "Search Library", "searchLibraryDesc": "Find books and resources", "sendMessage": "Send Message", "sendMessageDesc": "Send message to other users", "viewMore": "View more actions", "noActions": "No quick actions available for your role"}, "meetings": {"requestMeeting": "Request Faculty Meeting", "requestMeetingDesc": "Schedule a meeting with your instructor or advisor", "selectFaculty": "Select Faculty Member", "chooseFaculty": "Choose faculty member", "facultyRequired": "Please select a faculty member", "meetingType": "Meeting Type", "officeHours": "Office Hours", "academicAdvising": "Academic Advising", "courseDiscussion": "Course Discussion", "researchConsultation": "Research Consultation", "careerGuidance": "Career Guidance", "other": "Other", "priority": "Priority", "lowPriority": "Low", "normalPriority": "Normal", "highPriority": "High", "urgentPriority": "<PERSON><PERSON>", "subject": "Subject", "subjectRequired": "Subject is required", "subjectPlaceholder": "Brief description of the meeting purpose", "description": "Description", "descriptionRequired": "Description is required", "descriptionPlaceholder": "Provide details about what you would like to discuss...", "relatedCourse": "Related Course (Optional)", "noCourse": "No specific course", "preferredDate": "Preferred Date", "dateRequired": "Preferred date is required", "pastDateError": "Date cannot be in the past", "preferredTime": "Preferred Time", "timeRequired": "Preferred time is required", "duration": "Duration", "minutes": "minutes", "locationPreference": "Location Preference", "locationPlaceholder": "e.g., Your office, Online, Library", "recurringMeeting": "This is a recurring meeting request", "sendRequest": "Send Request", "sending": "Sending...", "requestSent": "Meeting Request Sent", "requestSentMessage": "Your meeting request has been sent successfully", "submitError": "Failed to submit meeting request", "submitFailed": "Submit Failed", "myRequests": "My Meeting Requests", "incomingRequests": "Meeting Requests", "requests": "requests", "status": "Status", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "type": "Type", "urgent": "<PERSON><PERSON>", "high": "High", "normal": "Normal", "low": "Low", "searchPlaceholder": "Search requests...", "noRequests": "No Meeting Requests", "noRequestsStudent": "You haven't made any meeting requests yet.", "noRequestsFaculty": "No meeting requests have been received.", "with": "With", "from": "From", "overdue": "Overdue", "approve": "Approve", "reject": "Reject", "cancel": "Cancel", "confirmCancel": "Are you sure you want to cancel this meeting?", "cancelSuccess": "Meeting has been cancelled successfully", "cancelError": "Failed to cancel meeting", "cancelFailed": "Cancel Failed", "updated": "Meeting Updated", "updateSuccess": "Meeting request has been updated successfully", "updateError": "Failed to update meeting request", "updateFailed": "Update Failed", "approveMeeting": "Approve Meeting", "rejectMeeting": "Reject Meeting", "approvedDate": "Approved Date", "approvedTime": "Approved Time", "location": "Location", "rejectionReason": "Rejection Reason", "rejectionPlaceholder": "Please provide a reason for rejection...", "additionalNotes": "Additional Notes", "notesPlaceholder": "Any additional notes or instructions...", "approvedDetails": "Approved Meeting Details", "date": "Date", "time": "Time", "fetchError": "Failed to load meeting requests", "loadingQuestions": "Loading Exam <PERSON>", "pleaseWait": "Please wait while we prepare your exam...", "noQuestions": "No Questions Available", "noQuestionsDesc": "This exam does not have any questions configured yet."}, "security": {"score": "Security Score", "threats": "Security Threats", "settings": "Security Settings", "settingsDesc": "Manage your account security and privacy", "securityFeatures": "Security Features", "quickActions": "Quick Actions", "biometricAuth": "Biometric Authentication", "biometricAuthDesc": "Use fingerprint or face recognition for secure access", "requestSigning": "Request Signing", "requestSigningDesc": "Sign API requests to prevent tampering", "deviceFingerprinting": "Device Fingerprinting", "deviceFingerprintingDesc": "Verify device identity for enhanced security", "dataEncryption": "Data Encryption", "dataEncryptionDesc": "Encrypt sensitive data stored locally", "autoLogout": "Auto Logout", "autoLogoutDesc": "Automatically logout after inactivity", "changePassword": "Change Password", "changePasswordDesc": "Update your account password", "clearData": "Clear Security Data", "clearDataWarning": "This will clear all stored security data. Continue?", "clear": "Clear", "securityAudit": "Security Audit", "noThreats": "No security threats detected", "threatsFound": "potential threats found", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor", "error": "Security Error", "toggleError": "Failed to update security setting", "notAvailable": "Not available on this device"}, "validation": {"invalidEmail": "Invalid email format", "suspiciousContent": "Suspicious content detected", "emailTooLong": "Email address too long", "passwordTooShort": "Password must be at least 8 characters", "passwordNeedsUppercase": "Password needs uppercase letter", "passwordNeedsLowercase": "Password needs lowercase letter", "passwordNeedsNumber": "Password needs a number", "passwordNeedsSpecial": "Password needs special character", "passwordSequential": "Avoid sequential characters", "passwordCommon": "Password is too common", "nameTooShort": "Name must be at least 2 characters", "nameTooLong": "Name must be less than 50 characters", "nameInvalidChars": "Name contains invalid characters", "invalidPhone": "Invalid phone number format", "invalidStudentId": "Invalid student ID format", "invalidAmount": "Invalid amount format", "negativeAmount": "Amount cannot be negative", "amountTooLarge": "Amount exceeds maximum limit", "fieldRequired": "This field is required"}, "content": {"title": "Content Management", "description": "Organize your course into modules and lessons", "loading": "Loading course content...", "loadError": "<PERSON><PERSON>", "loadErrorMessage": "Failed to load course content", "createError": "Creation Error", "createErrorMessage": "Failed to create module", "moduleCreated": "Module Created", "moduleCreatedMessage": "Mo<PERSON>le created successfully", "noModules": "No modules yet", "createFirstModule": "Create your first module to get started", "createModule": "Create Mo<PERSON>le", "newModule": "New Module", "newModuleDescription": "Module description", "titlePlaceholder": "Enter module title", "titleArabicPlaceholder": "Enter Arabic title", "titleRequired": "Title is required", "descriptionPlaceholder": "Enter module description", "descriptionArabicPlaceholder": "Enter Arabic description", "descriptionRequired": "Description is required", "estimatedDuration": "Duration (minutes)", "durationRequired": "Duration must be at least 1 minute", "status": "Status", "draft": "Draft", "published": "Published", "required": "Required module", "creating": "Creating...", "createModuleDescription": "Add a new learning module to your course"}, "forums": {"title": "Discussion Forums", "description": "Engage with your classmates and instructor", "loading": "Loading forum discussions...", "loadError": "<PERSON><PERSON>", "loadErrorMessage": "Failed to load forum data", "createError": "Creation Error", "createErrorMessage": "Failed to create topic", "topicCreated": "Topic Created", "topicCreatedMessage": "Topic created successfully", "noForum": "No Forum", "noForumMessage": "Forum not loaded yet", "createTopic": "Create New Topic", "createTopicDescription": "Start a new discussion or ask a question", "newTopic": "New Discussion Topic", "newTopicDescription": "Topic description", "titlePlaceholder": "Enter topic title", "titleArabicPlaceholder": "Enter Arabic title", "titleRequired": "Title is required", "descriptionPlaceholder": "Describe your topic or question in detail", "descriptionArabicPlaceholder": "Enter Arabic description", "descriptionRequired": "Description is required", "qaQuestion": "This is a Q&A question", "tags": "Tags", "customTagPlaceholder": "Add custom tag and press Enter", "creating": "Creating..."}, "home": {"hero": {"tagline": "Mastery in Education Technology", "title": "The Complete University Management Ecosystem", "subtitle": "Transform your educational institution with our comprehensive platform featuring web dashboards, native mobile applications, advanced analytics, and full Arabic localization. Trusted by universities worldwide.", "getStarted": "Access Live Demo", "watchDemo": "Watch Platform Tour", "trusted": "Trusted by leading educational institutions across the Middle East and beyond", "web": {"title": "Advanced Web Platform", "desc": "Comprehensive admin dashboard with role-based access, real-time analytics, and complete university management tools"}, "mobile": {"title": "Native Mobile Apps", "desc": "iOS and Android applications with offline support, push notifications, and full feature parity with web platform"}, "arabic": {"title": "Arabic Excellence", "desc": "Complete RTL support, Arabic typography, cultural localization, and seamless bilingual experience"}, "stats": {"students": "Active Students", "universities": "Universities", "countries": "Countries", "uptime": "Uptime SLA"}}, "tech": {"title": "Technical Specifications", "subtitle": "Built with modern technology stack for scalability, security, and performance"}, "features": {"title": "Complete University Management Platform", "subtitle": "Everything your university needs: Web dashboard, mobile apps, role-based access, and comprehensive academic management", "content": {"title": "Course Management", "desc": "Complete course creation, content delivery, and student enrollment management"}, "messaging": {"title": "Real-time Messaging", "desc": "Instant messaging, announcements, and role-based communication systems"}, "analytics": {"title": "Advanced Analytics", "desc": "Comprehensive dashboards, performance tracking, and detailed reporting"}, "scheduling": {"title": "Academic Calendar", "desc": "Schedule management, exam planning, and academic event coordination"}, "grading": {"title": "Assessment & Grading", "desc": "Automated grading, transcript generation, and academic progress tracking"}, "mobile": {"title": "Mobile Applications", "desc": "Native iOS & Android apps with full feature parity and offline support"}, "roles": {"title": "Role-based Access", "desc": "Multi-role system: Students, Teachers, Admins, Finance Officers, and Staff"}, "multilingual": {"title": "Arabic & English", "desc": "Full RTL support, bilingual interface, and cultural localization"}, "security": {"title": "Enterprise Security", "desc": "Advanced authentication, data encryption, and compliance standards"}}, "howItWorks": {"title": "How Itkan Works", "subtitle": "Deploy complete university management system in three simple steps", "step1": {"title": "Setup & Configuration", "desc": "Install Itkan platform, configure university settings, and set up initial admin accounts"}, "step2": {"title": "User Management", "desc": "Create user accounts, assign roles, and configure permissions for all stakeholders"}, "step3": {"title": "Go Live", "desc": "Launch courses, enable mobile apps, and start managing your university digitally"}}, "about": {"title": "About Itkan (إتقان)", "description": "It<PERSON>, meaning \"mastery\" in Arabic, represents our commitment to educational excellence. We provide a comprehensive university management system that combines modern web technology with native mobile applications.", "mission": "Our mission is to empower educational institutions with cutting-edge technology that enhances learning experiences, streamlines administrative processes, and fosters academic success.", "platform": {"title": "Dual Platform Architecture", "desc": "Responsive web dashboard for administrators and native mobile apps for students and faculty"}, "roles": {"title": "Comprehensive Role Management", "desc": "Five distinct user roles with customized interfaces: Students, Teachers, Admins, Finance Officers, and Staff"}, "bilingual": {"title": "True Bilingual Support", "desc": "Complete Arabic and English interface with RTL support, Arabic fonts, and cultural localization"}, "communication": {"title": "Real-time Communication", "desc": "Instant messaging, announcements, notifications, and role-based communication channels"}}, "testimonials": {"title": "Trusted by Educational Institutions", "subtitle": "Universities and colleges worldwide rely on Itkan for comprehensive academic management"}, "cta": {"title": "Ready to Transform Your University?", "subtitle": "Join educational institutions worldwide using Itkan for complete university management. Web dashboard, mobile apps, role-based access, and full Arabic support included.", "getStarted": "Access Dashboard", "downloadApp": "Download Mobile Apps"}}, "nav": {"home": "Home", "features": "Features", "about": "About", "pricing": "Pricing", "contact": "Contact"}, "footer": {"description": "Empowering education through innovative technology. Building the future of learning management systems.", "quickLinks": "Quick Links", "resources": {"documentation": "Documentation", "tutorials": "Tutorials", "support": "Support Center", "community": "Community", "api": "API Reference", "status": "System Status"}, "contact": "Contact Us", "newsletter": "Stay Updated", "subscribe": "Subscribe", "emailPlaceholder": "Enter your email", "address": "123 Education Street, Tech City, TC 12345", "rights": "All rights reserved.", "madeWith": "Made with", "forEducation": "for education", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>", "secure": "Secure", "links": {"about": "About Us", "features": "Features", "pricing": "Pricing", "blog": "Blog", "careers": "Careers", "contact": "Contact"}, "stats": {"students": "Active Students", "courses": "Courses Created", "institutions": "Institutions", "countries": "Countries"}}, "assessments": {"title": "Title", "description": "Description", "createAssessment": "Create Assessment", "createFirstAssessment": "Create First Assessment", "noAssessments": "No Assessments", "noAssessmentsDesc": "Create your first assessment to get started", "questionBanks": "Question Banks", "createQuestionBank": "Create Question Bank", "createFirstBank": "Create First Question Bank", "noQuestionBanks": "No Question Banks", "noQuestionBanksDesc": "Create your first question bank to get started", "questions": "Questions", "addQuestions": "Add Questions", "selectQuestions": "Select Questions", "noQuestionsAdded": "No questions added yet", "selectCourseFirst": "Select a course first to add questions", "basicInformation": "Basic Information", "scoringAndTiming": "Scoring & Timing", "availability": "Availability", "settings": "Settings", "titleArabic": "Title (Arabic)", "titlePlaceholder": "Enter assessment title", "titleArabicPlaceholder": "Enter Arabic title", "descriptionArabic": "Description (Arabic)", "descriptionPlaceholder": "Enter description (optional)", "descriptionArabicPlaceholder": "Enter Arabic description (optional)", "instructions": "Instructions", "instructionsArabic": "Instructions (Arabic)", "instructionsPlaceholder": "Enter instructions for students (optional)", "course": "Course", "selectCourse": "Select a course", "type": "Type", "quiz": "Quiz", "exam": "Exam", "test": "Test", "practice": "Practice", "survey": "Survey", "totalPoints": "Total Points", "autoCalculated": "Auto-calculated from selected questions", "passingScore": "Passing Score (%)", "timeLimit": "Time Limit (minutes)", "noTimeLimit": "No limit", "availableFrom": "Available From", "availableUntil": "Available Until", "maxAttempts": "Max Attempts", "allowReview": "Allow students to review their answers", "showCorrectAnswers": "Show correct answers after submission", "showScoreImmediately": "Show score immediately after submission", "randomizeQuestions": "Randomize question order for each student", "questionText": "Question Text", "questionTextArabic": "Question Text (Arabic)", "questionTextPlaceholder": "Enter your question here", "questionTextArabicPlaceholder": "Enter Arabic question text", "questionType": "Question Type", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "points": "Points", "multiple_choice": "Multiple Choice", "true_false": "True/False", "short_answer": "Short Answer", "essay": "Essay", "fill_blank": "Fill in the Blank", "matching": "Matching", "ordering": "Ordering", "easy": "Easy", "medium": "Medium", "hard": "Hard", "options": "Options", "option": "Option", "checkCorrectOptions": "Check the correct option(s)", "correctAnswer": "Correct Answer", "true": "True", "false": "False", "acceptableAnswers": "Acceptable Answers", "acceptableAnswersPlaceholder": "Enter acceptable answers, one per line", "acceptableAnswersHelp": "Enter one answer per line. Answers are case-insensitive.", "essayNote": "Essay questions require manual grading. Students will see a text area to write their response.", "matchingPairs": "Matching Pairs", "addPair": "Add Pair", "leftItem": "Left item", "rightItem": "Right item", "explanation": "Explanation", "explanationPlaceholder": "Explain the correct answer (optional)", "category": "Category", "categoryPlaceholder": "e.g., Chapter 1, Midterm", "tags": "Tags", "tagsPlaceholder": "Comma-separated tags", "makePublic": "Make this question bank public", "makePrivate": "Make Private", "publicDescription": "Public question banks can be used by other instructors in the same course", "public": "Public", "createdBy": "Created by", "confirmDelete": "Are you sure you want to delete this assessment?", "confirmDeleteBank": "Are you sure you want to delete this question bank?", "publish": "Publish", "takeAssessment": "Take Assessment", "attempts": "Attempts", "minutes": "min", "status": {"draft": "Draft", "published": "Published", "active": "Active", "closed": "Closed", "archived": "Archived"}, "questionBank": "Question Bank", "selectQuestionBank": "Select a question bank", "searchQuestions": "Search questions...", "allTypes": "All Types", "allDifficulties": "All Difficulties", "selectAll": "Select All", "deselectAll": "Deselect All", "selected": "selected", "loadingQuestions": "Loading questions...", "noQuestionsInBank": "No questions in this bank", "noQuestionsMatchFilter": "No questions match your filters", "addSelectedQuestions": "Add Selected Questions"}}