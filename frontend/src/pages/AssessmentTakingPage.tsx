import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import AssessmentTaking from '../components/assessments/AssessmentTaking';

const AssessmentTakingPage: React.FC = () => {
  const { assessmentId } = useParams<{ assessmentId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();

  if (!assessmentId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('assessments.invalidAssessment')}
          </h2>
          <button
            onClick={() => navigate('/app/assessments')}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            {t('common.goBack')}
          </button>
        </div>
      </div>
    );
  }

  const handleComplete = (attempt: any) => {
    // Navigate to results page or back to assessments
    navigate('/app/assessments', {
      state: { 
        message: t('assessments.submittedSuccessfully'),
        type: 'success'
      }
    });
  };

  const handleExit = () => {
    navigate('/app/assessments');
  };

  return (
    <AssessmentTaking
      assessmentId={assessmentId}
      onComplete={handleComplete}
      onExit={handleExit}
    />
  );
};

export default AssessmentTakingPage;
