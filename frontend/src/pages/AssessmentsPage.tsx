import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  PlusIcon, 
  AcademicCapIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { useAppSelector } from '../store';
import apiService from '../services/api';
import LoadingSpinner from '../components/common/LoadingSpinner';
import Modal from '../components/common/Modal';
import AssessmentCreator from '../components/assessments/AssessmentCreator';
import QuestionBankList from '../components/assessments/QuestionBankList';

interface Assessment {
  id: string;
  title: string;
  title_ar: string;
  description: string;
  assessment_type: string;
  status: string;
  total_points: number;
  passing_score: number;
  time_limit_minutes: number | null;
  available_from: string;
  available_until: string;
  max_attempts: number;
  questions_count: number;
  attempts_count: number;
  is_available: boolean;
  created_at: string;
  course_name: string;
  course_code: string;
}

const AssessmentsPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { courseId } = useParams();
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showQuestionBanks, setShowQuestionBanks] = useState(false);
  const [activeTab, setActiveTab] = useState<'assessments' | 'questions'>('assessments');

  const isRTL = i18n.language === 'ar';
  const isTeacher = user?.role === 'teacher' || user?.role === 'admin';

  useEffect(() => {
    fetchAssessments();
  }, [courseId]);

  const fetchAssessments = async () => {
    try {
      setLoading(true);
      const params = courseId ? { course_id: courseId } : {};
      const response = await apiService.request('/assessments/assessments/', {
        method: 'GET',
        params
      });
      setAssessments(response.results || response);
    } catch (error) {
      console.error('Failed to fetch assessments:', error);
      setError('Failed to load assessments');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAssessment = async (data: any) => {
    try {
      // Create the assessment
      const assessmentResponse = await apiService.request('/assessments/assessments/', {
        method: 'POST',
        body: JSON.stringify({
          title: data.title,
          title_ar: data.title_ar,
          description: data.description,
          description_ar: data.description_ar,
          instructions: data.instructions,
          instructions_ar: data.instructions_ar,
          course: data.course,
          assessment_type: data.assessment_type,
          total_points: data.total_points,
          passing_score: data.passing_score,
          time_limit_minutes: data.time_limit_minutes,
          available_from: data.available_from,
          available_until: data.available_until,
          max_attempts: data.max_attempts,
          allow_review: data.allow_review,
          show_correct_answers: data.show_correct_answers,
          show_score_immediately: data.show_score_immediately,
          randomize_questions: data.randomize_questions,
          questions_per_page: data.questions_per_page
        })
      });

      // Add questions to the assessment
      if (data.questions && data.questions.length > 0) {
        await apiService.request(`/assessments/assessments/${assessmentResponse.id}/add_questions/`, {
          method: 'POST',
          body: JSON.stringify({ question_ids: data.questions })
        });
      }

      setShowCreateModal(false);
      fetchAssessments();
    } catch (error) {
      console.error('Failed to create assessment:', error);
      throw error;
    }
  };

  const handleDeleteAssessment = async (assessmentId: string) => {
    if (!confirm(t('assessments.confirmDelete', 'Are you sure you want to delete this assessment?'))) {
      return;
    }

    try {
      await apiService.request(`/assessments/assessments/${assessmentId}/`, {
        method: 'DELETE'
      });
      fetchAssessments();
    } catch (error) {
      console.error('Failed to delete assessment:', error);
    }
  };

  const handlePublishAssessment = async (assessmentId: string) => {
    try {
      await apiService.request(`/assessments/assessments/${assessmentId}/publish/`, {
        method: 'POST'
      });
      fetchAssessments();
    } catch (error) {
      console.error('Failed to publish assessment:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
      case 'active':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30';
      case 'draft':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/30';
      case 'closed':
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30';
      case 'archived':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'quiz':
        return <AcademicCapIcon className="w-5 h-5" />;
      case 'exam':
        return <ClockIcon className="w-5 h-5" />;
      default:
        return <AcademicCapIcon className="w-5 h-5" />;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" text={t('common.loading', 'Loading...')} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchAssessments}
          className="btn btn-primary"
        >
          {t('common.retry', 'Retry')}
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('assessments.title', 'Assessments')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {t('assessments.description', 'Create and manage quizzes, exams, and assessments')}
          </p>
        </div>
        
        {isTeacher && (
          <div className="flex gap-3">
            <button
              onClick={() => setShowQuestionBanks(true)}
              className="btn btn-secondary flex items-center gap-2"
            >
              <AcademicCapIcon className="w-5 h-5" />
              {t('assessments.questionBanks', 'Question Banks')}
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn btn-primary flex items-center gap-2"
            >
              <PlusIcon className="w-5 h-5" />
              {t('assessments.createAssessment', 'Create Assessment')}
            </button>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('assessments')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'assessments'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            {t('assessments.assessments', 'Assessments')}
          </button>
          {isTeacher && (
            <button
              onClick={() => setActiveTab('questions')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'questions'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              {t('assessments.questionBanks', 'Question Banks')}
            </button>
          )}
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'assessments' ? (
        <div>
          {/* Assessments Grid */}
          {assessments.length === 0 ? (
            <div className="text-center py-12">
              <AcademicCapIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('assessments.noAssessments', 'No Assessments')}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {t('assessments.noAssessmentsDesc', 'Create your first assessment to get started')}
              </p>
              {isTeacher && (
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="btn btn-primary"
                >
                  {t('assessments.createFirstAssessment', 'Create First Assessment')}
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {assessments.map((assessment) => (
                <div
                  key={assessment.id}
                  className="glass-card p-6 hover:shadow-lg transition-all duration-200"
                >
                  {/* Header */}
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center gap-3">
                      {getTypeIcon(assessment.assessment_type)}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {isRTL && assessment.title_ar ? assessment.title_ar : assessment.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {assessment.course_code} - {assessment.course_name}
                        </p>
                      </div>
                    </div>
                    
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(assessment.status)}`}>
                      {t(`assessments.status.${assessment.status}`, assessment.status)}
                    </div>
                  </div>

                  {/* Description */}
                  {assessment.description && (
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                      {assessment.description}
                    </p>
                  )}

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <AcademicCapIcon className="w-4 h-4" />
                      <span>{assessment.questions_count} {t('assessments.questions', 'Questions')}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <ChartBarIcon className="w-4 h-4" />
                      <span>{assessment.total_points} {t('assessments.points', 'Points')}</span>
                    </div>
                    {assessment.time_limit_minutes && (
                      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <ClockIcon className="w-4 h-4" />
                        <span>{assessment.time_limit_minutes} {t('assessments.minutes', 'min')}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <span>{assessment.attempts_count} {t('assessments.attempts', 'Attempts')}</span>
                    </div>
                  </div>

                  {/* Availability */}
                  <div className="text-xs text-gray-500 dark:text-gray-500 mb-4">
                    <div>
                      {t('assessments.availableFrom', 'Available from')}: {new Date(assessment.available_from).toLocaleString()}
                    </div>
                    <div>
                      {t('assessments.availableUntil', 'Until')}: {new Date(assessment.available_until).toLocaleString()}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex gap-2">
                      <button
                        onClick={() => navigate(`/assessments/${assessment.id}`)}
                        className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                        title={t('common.view', 'View')}
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      
                      {isTeacher && (
                        <>
                          <button
                            onClick={() => navigate(`/assessments/${assessment.id}/edit`)}
                            className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                            title={t('common.edit', 'Edit')}
                          >
                            <PencilIcon className="w-4 h-4" />
                          </button>
                          
                          {assessment.status === 'draft' && (
                            <button
                              onClick={() => handlePublishAssessment(assessment.id)}
                              className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                              title={t('assessments.publish', 'Publish')}
                            >
                              <CheckCircleIcon className="w-4 h-4" />
                            </button>
                          )}
                          
                          <button
                            onClick={() => handleDeleteAssessment(assessment.id)}
                            className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                            title={t('common.delete', 'Delete')}
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                    
                    {assessment.is_available && user?.role === 'student' && (
                      <button
                        onClick={() => navigate(`/assessments/${assessment.id}/take`)}
                        className="btn btn-primary btn-sm"
                      >
                        {t('assessments.takeAssessment', 'Take Assessment')}
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <QuestionBankList courseId={courseId} />
      )}

      {/* Create Assessment Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t('assessments.createAssessment', 'Create Assessment')}
        size="xl"
      >
        <AssessmentCreator
          onSubmit={handleCreateAssessment}
          onCancel={() => setShowCreateModal(false)}
          courseId={courseId}
        />
      </Modal>

      {/* Question Banks Modal */}
      <Modal
        isOpen={showQuestionBanks}
        onClose={() => setShowQuestionBanks(false)}
        title={t('assessments.questionBanks', 'Question Banks')}
        size="xl"
      >
        <QuestionBankList courseId={courseId} />
      </Modal>
    </div>
  );
};

export default AssessmentsPage;
