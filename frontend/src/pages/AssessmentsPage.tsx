import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useAppSelector } from '../store';
import AssessmentList from '../components/assessments/AssessmentList';
import AssessmentCreator from '../components/assessments/AssessmentCreator';
import QuestionBankList from '../components/assessments/QuestionBankList';
import {
  FileText,
  Plus,
  Database,
  BarChart3
} from 'lucide-react';

const AssessmentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { courseId } = useParams();
  const { user } = useAppSelector((state) => state.auth);

  const [activeTab, setActiveTab] = useState('assessments');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showQuestionBanks, setShowQuestionBanks] = useState(false);

  // Check if user can create assessments
  const canCreate = user?.role === 'teacher' || user?.role === 'admin' || user?.role === 'super_admin';

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {t('assessments.title')}
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  {user?.role === 'student'
                    ? t('assessments.studentDescription')
                    : t('assessments.teacherDescription')
                  }
                </p>
              </div>

              {canCreate && (
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setShowQuestionBanks(true)}
                    className="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                  >
                    <Database className="w-4 h-4 mr-2" />
                    {t('assessments.questionBanks')}
                  </button>

                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {t('assessments.createAssessment')}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Simple tab navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('assessments')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'assessments'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <FileText className="w-4 h-4 mr-2 inline" />
              {t('assessments.title')}
            </button>

            {canCreate && (
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <BarChart3 className="w-4 h-4 mr-2 inline" />
                {t('assessments.analytics')}
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'assessments' && (
          <AssessmentList
            courseId={courseId}
            showCreateButton={canCreate}
            className="w-full"
          />
        )}

        {activeTab === 'analytics' && canCreate && (
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
            <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t('assessments.analytics')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {t('assessments.analyticsComingSoon')}
            </p>
          </div>
        )}
      </div>

      {/* Create Assessment Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-full overflow-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {t('assessments.createAssessment')}
                </h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6">
              <AssessmentCreator
                onSuccess={() => {
                  setShowCreateModal(false);
                  // Refresh assessments list
                  window.location.reload();
                }}
                onCancel={() => setShowCreateModal(false)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Question Banks Modal */}
      {showQuestionBanks && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-full overflow-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {t('assessments.questionBanks')}
                </h3>
                <button
                  onClick={() => setShowQuestionBanks(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6">
              <QuestionBankList />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssessmentsPage;
