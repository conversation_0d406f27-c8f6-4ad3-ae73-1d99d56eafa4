import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../store';
import { addNotification } from '../store/slices/uiSlice';
import { apiService } from '../services/api';
import { useAuthenticatedApi } from '../hooks/useAuthenticatedApi';
import ContentManager from '../components/content/ContentManager';
import ForumManager from '../components/forums/ForumManager';
import {
  BookOpen,
  Users,
  Calendar,
  MapPin,
  Clock,
  User,
  FileText,
  Download,
  Edit,
  UserPlus,
  UserMinus,
  BarChart3,
  MessageSquare,
  Star,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  Video,
  Layers,
  Forum,
  Plus
} from 'lucide-react';

interface Course {
  id: string;
  name: string;
  nameAr?: string;
  code: string;
  description: string;
  descriptionAr?: string;
  instructor: string;
  instructorId: string;
  department: string;
  credits: number;
  semester: string;
  schedule: string;
  location: string;
  capacity: number;
  enrolled: number;
  status: 'active' | 'inactive' | 'completed';
  startDate: string;
  endDate: string;
  prerequisites?: string[];
  materials?: string[];
  syllabus?: string;
  rating?: number;
  totalRatings?: number;
}

interface Enrollment {
  id: string;
  studentId: string;
  studentName: string;
  enrollmentDate: string;
  status: 'active' | 'dropped' | 'completed';
  grade?: string;
  attendance: number;
}

interface Assignment {
  id: string;
  title: string;
  dueDate: string;
  status: 'pending' | 'submitted' | 'graded';
  grade?: number;
}

interface Announcement {
  id: string;
  title: string;
  content: string;
  date: string;
  author: string;
}

const CourseDetailPage: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { user, token } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  // Ensure API authentication is set up
  useAuthenticatedApi();

  const [course, setCourse] = useState<Course | null>(null);
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'students' | 'assignments' | 'announcements' | 'materials' | 'forums'>('overview');
  const [isEnrolled, setIsEnrolled] = useState(false);

  const canManageCourse = user?.role === 'teacher' || user?.role === 'admin' || user?.role === 'super_admin';
  const isStudent = user?.role === 'student';

  useEffect(() => {
    if (courseId) {
      fetchCourseDetails();
    }
  }, [courseId]);

  const fetchCourseDetails = async () => {
    try {
      setLoading(true);

      // Fetch course data from API
      const courseData = await apiService.getCourse(parseInt(courseId || '0'));

      // Transform API data to match component interface
      const transformedCourse: Course = {
        id: courseData.id?.toString() || courseId || '1',
        name: courseData.title || courseData.name || 'Course Title',
        nameAr: courseData.title_ar || courseData.nameAr || 'عنوان المقرر',
        code: courseData.code || 'COURSE101',
        description: courseData.description || 'Course description',
        descriptionAr: courseData.description_ar || courseData.descriptionAr || 'وصف المقرر',
        instructor: courseData.instructor_name || courseData.instructor || 'Instructor',
        instructorId: courseData.instructor?.toString() || 'instructor1',
        department: courseData.department_name || courseData.department || 'Department',
        credits: courseData.credit_hours || courseData.credits || 3,
        semester: courseData.semester || 'Fall 2024',
        schedule: courseData.schedule || 'TBD',
        location: courseData.location || 'TBD',
        capacity: courseData.max_students || courseData.capacity || 30,
        enrolled: courseData.enrolled_students_count || courseData.enrolled || 0,
        status: courseData.is_active ? 'active' : 'inactive',
        startDate: courseData.start_date || courseData.startDate || '2024-09-01',
        endDate: courseData.end_date || courseData.endDate || '2024-12-15',
        prerequisites: courseData.prerequisites_names || courseData.prerequisites || [],
        materials: courseData.materials || ['Course Materials'],
        syllabus: courseData.syllabus || '',
        rating: courseData.rating || 0,
        totalRatings: courseData.totalRatings || 0
      };

      // Fetch additional course data
      let enrollmentsData: Enrollment[] = [];
      let assignmentsData: Assignment[] = [];
      let announcementsData: Announcement[] = [];

      try {
        // Try to fetch enrollments if user has permission
        if (canManageCourse) {
          const enrollmentsResponse = await apiService.getCourseEnrollments(parseInt(courseId || '0'));
          enrollmentsData = enrollmentsResponse.map((enrollment: any) => ({
            id: enrollment.id?.toString() || '',
            studentId: enrollment.student?.toString() || '',
            studentName: enrollment.student_name || 'Student',
            enrollmentDate: enrollment.enrollment_date || enrollment.created_at || '',
            status: enrollment.status || 'active',
            grade: enrollment.grade || '',
            attendance: enrollment.attendance || 0
          }));
        }
      } catch (error) {
        console.log('Could not fetch enrollments:', error);
      }

      try {
        // Try to fetch assignments
        const assignmentsResponse = await apiService.getAssignments({ course: courseId });
        assignmentsData = assignmentsResponse.results?.map((assignment: any) => ({
          id: assignment.id?.toString() || '',
          title: assignment.title || 'Assignment',
          dueDate: assignment.due_date || assignment.dueDate || '',
          status: assignment.status || 'pending',
          grade: assignment.grade || undefined
        })) || [];
      } catch (error) {
        console.log('Could not fetch assignments:', error);
      }

      try {
        // Try to fetch announcements
        const announcementsResponse = await apiService.getAnnouncements({ course: courseId });
        announcementsData = announcementsResponse.results?.map((announcement: any) => ({
          id: announcement.id?.toString() || '',
          title: announcement.title || 'Announcement',
          content: announcement.content || '',
          date: announcement.created_at || announcement.date || '',
          author: announcement.created_by?.display_name || announcement.author || 'Instructor'
        })) || [];
      } catch (error) {
        console.log('Could not fetch announcements:', error);
      }

      setCourse(transformedCourse);
      setEnrollments(enrollmentsData);
      setAssignments(assignmentsData);
      setAnnouncements(announcementsData);
      setIsEnrolled(isStudent && enrollmentsData.some(e => e.studentId === user?.id?.toString()));

    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: t('courses.error', 'Error'),
        message: t('courses.fetchError', 'Failed to load course details')
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsEnrolled(true);
      setCourse(prev => prev ? { ...prev, enrolled: prev.enrolled + 1 } : null);
      
      dispatch(addNotification({
        type: 'success',
        title: t('courses.enrolled', 'Enrolled Successfully'),
        message: t('courses.enrolledMessage', 'You have been enrolled in this course')
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: t('courses.enrollError', 'Enrollment Failed'),
        message: t('courses.enrollErrorMessage', 'Failed to enroll in course')
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleDrop = async () => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsEnrolled(false);
      setCourse(prev => prev ? { ...prev, enrolled: prev.enrolled - 1 } : null);
      
      dispatch(addNotification({
        type: 'success',
        title: t('courses.dropped', 'Dropped Successfully'),
        message: t('courses.droppedMessage', 'You have been dropped from this course')
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: t('courses.dropError', 'Drop Failed'),
        message: t('courses.dropErrorMessage', 'Failed to drop course')
      }));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Course Not Found</h2>
        <button onClick={() => navigate('/courses')} className="btn-primary">
          Back to Courses
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <button
              onClick={() => navigate('/courses')}
              className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
            </button>
            <div className="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center">
              <BookOpen className="w-8 h-8 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{course.name}</h1>
                <span className="text-lg text-gray-600">({course.code})</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  course.status === 'active' ? 'bg-green-100 text-green-800' :
                  course.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {course.status}
                </span>
              </div>
              <p className="text-gray-600 mb-4">{course.description}</p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4 text-gray-500" />
                  <span>{course.instructor}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-gray-500" />
                  <span>{course.enrolled}/{course.capacity} students</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span>{course.schedule}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span>{course.location}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex space-x-2">
            {isStudent && (
              isEnrolled ? (
                <button
                  onClick={handleDrop}
                  disabled={loading}
                  className="btn-secondary flex items-center space-x-2"
                >
                  <UserMinus className="w-4 h-4" />
                  <span>Drop Course</span>
                </button>
              ) : (
                <button
                  onClick={handleEnroll}
                  disabled={loading || course.enrolled >= course.capacity}
                  className="btn-primary flex items-center space-x-2"
                >
                  <UserPlus className="w-4 h-4" />
                  <span>Enroll</span>
                </button>
              )
            )}
            {canManageCourse && (
              <button className="btn-primary flex items-center space-x-2">
                <Edit className="w-4 h-4" />
                <span>Edit Course</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: BookOpen },
            { id: 'content', label: 'Content', icon: Layers },
            { id: 'forums', label: 'Forums', icon: MessageSquare },
            { id: 'students', label: 'Students', icon: Users },
            { id: 'assignments', label: 'Assignments', icon: FileText },
            { id: 'announcements', label: 'Announcements', icon: MessageSquare },
            { id: 'materials', label: 'Materials', icon: Download }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <div className="glass-card">
                <h3 className="text-lg font-semibold mb-4">Course Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    <p className="text-gray-900">{course.department}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Credits</label>
                    <p className="text-gray-900">{course.credits}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Semester</label>
                    <p className="text-gray-900">{course.semester}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Prerequisites</label>
                    <div className="flex flex-wrap gap-2">
                      {course.prerequisites?.map((prereq, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                          {prereq}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-6">
              <div className="glass-card">
                <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Enrollment</span>
                    <span className="font-medium">{course.enrolled}/{course.capacity}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Rating</span>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="font-medium">{course.rating}</span>
                      <span className="text-gray-500">({course.totalRatings})</span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status</span>
                    <span className="font-medium capitalize">{course.status}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'content' && (
          <div className="glass-card">
            <ContentManager courseId={courseId || ''} canEdit={canManageCourse} />
          </div>
        )}

        {activeTab === 'forums' && (
          <div className="glass-card">
            <ForumManager courseId={courseId || ''} canModerate={canManageCourse} />
          </div>
        )}

        {activeTab === 'students' && (
          <div className="glass-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Enrolled Students ({enrollments.length})</h3>
              {canManageCourse && (
                <button className="btn-primary flex items-center space-x-2">
                  <UserPlus className="w-4 h-4" />
                  <span>Add Student</span>
                </button>
              )}
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Enrollment Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Attendance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Grade
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {enrollments.map((enrollment) => (
                    <tr key={enrollment.id} className="hover:">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <User className="w-4 h-4 text-white" />
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">
                              {enrollment.studentName}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(enrollment.enrollmentDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {enrollment.attendance}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {enrollment.grade || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          enrollment.status === 'active' ? 'bg-green-100 text-green-800' :
                          enrollment.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {enrollment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'assignments' && (
          <div className="glass-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Assignments ({assignments.length})</h3>
              {canManageCourse && (
                <button className="btn-primary flex items-center space-x-2">
                  <FileText className="w-4 h-4" />
                  <span>Create Assignment</span>
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              {assignments.map((assignment) => (
                <div key={assignment.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{assignment.title}</h4>
                      <p className="text-sm text-gray-600">Due: {new Date(assignment.dueDate).toLocaleDateString()}</p>
                    </div>
                    <div className="flex items-center space-x-3">
                      {assignment.grade && (
                        <span className="text-sm font-medium text-green-600">
                          Grade: {assignment.grade}%
                        </span>
                      )}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        assignment.status === 'graded' ? 'bg-green-100 text-green-800' :
                        assignment.status === 'submitted' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {assignment.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'announcements' && (
          <div className="glass-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Announcements ({announcements.length})</h3>
              {canManageCourse && (
                <button className="btn-primary flex items-center space-x-2">
                  <MessageSquare className="w-4 h-4" />
                  <span>New Announcement</span>
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              {announcements.map((announcement) => (
                <div key={announcement.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{announcement.title}</h4>
                    <span className="text-sm text-gray-500">
                      {new Date(announcement.date).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-gray-700 mb-2">{announcement.content}</p>
                  <p className="text-sm text-gray-500">By {announcement.author}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'materials' && (
          <div className="glass-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Course Materials</h3>
              {canManageCourse && (
                <button className="btn-primary flex items-center space-x-2">
                  <Download className="w-4 h-4" />
                  <span>Upload Material</span>
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <FileText className="w-8 h-8 text-red-500" />
                    <div>
                      <h4 className="font-medium text-gray-900">Course Syllabus</h4>
                      <p className="text-sm text-gray-600">PDF • 2.5 MB</p>
                    </div>
                  </div>
                  <button className="btn-secondary flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>Download</span>
                  </button>
                </div>
              </div>
              
              {course.materials?.map((material, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FileText className="w-8 h-8 text-blue-500" />
                      <div>
                        <h4 className="font-medium text-gray-900">{material}</h4>
                        <p className="text-sm text-gray-600">Required material</p>
                      </div>
                    </div>
                    <button className="btn-secondary flex items-center space-x-2">
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseDetailPage;
