// frontend/src/pages/HomePage.tsx
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import {
  BookOpen,
  Users,
  GraduationCap,
  BarChart3,
  MessageSquare,
  Calendar,
  Award,
  Globe,
  Shield,
  Zap,
  ArrowRight,
  Play,
  CheckCircle,
  Star,
  Download,
  Smartphone,
  Monitor,
  Rocket
} from 'lucide-react';

const HomePage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center py-12 lg:py-20">
        {/* Clean Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-white to-teal-50/50 dark:from-slate-900 dark:via-blue-950 dark:to-slate-900"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.2),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(16,185,129,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(16,185,129,0.2),transparent_50%)]"></div>
        </div>

        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-12 py-20">

            {/* Hero Content - Single Grid */}
            {/* Logo and Brand */}
            <div className="flex items-center justify-center space-x-4 animate-fade-in">
              <div className="relative">
                <div className="w-20 h-20 glass-card rounded-3xl flex items-center justify-center shadow-xl">
                  <svg viewBox="0 0 100 100" className="w-12 h-12">
                    <defs>
                      <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#3B82F6" />
                        <stop offset="50%" stopColor="#10B981" />
                        <stop offset="100%" stopColor="#8B5CF6" />
                      </linearGradient>
                    </defs>
                    <path d="M20 80 L50 20 L80 80 L70 80 L60 60 L40 60 L30 80 Z M45 45 L55 45 L50 35 Z" fill="url(#logoGradient)"/>
                    <circle cx="85" cy="25" r="8" fill="url(#logoGradient)"/>
                  </svg>
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full animate-pulse flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
              <div className="text-left">
                <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white">
                  Itkan
                </h1>
                <p className="text-xl lg:text-2xl text-gray-600 dark:text-gray-300" dir="rtl">
                  إتقان
                </p>
                <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  University Management System
                </p>
              </div>
            </div>

            {/* Main Headline */}
            <div className="animate-slide-up">
              <h2 className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-8 leading-tight">
                <span className="text-gray-900 dark:text-white">Complete</span>
                <br />
                <span className="bg-gradient-to-r from-blue-600 via-teal-500 to-purple-600 bg-clip-text text-transparent">
                  University
                </span>
                <br />
                <span className="text-gray-900 dark:text-white">Management</span>
              </h2>

              <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 dark:text-gray-300 mb-12 leading-relaxed max-w-4xl mx-auto">
                Professional LMS platform with web dashboard and native mobile apps.
                Built for universities with role-based access, real-time messaging, and full Arabic support.
              </p>
            </div>

            {/* Feature Grid */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12 animate-scale-in">
              {[
                { icon: Monitor, title: 'Web Dashboard', desc: 'Complete Admin Interface', color: 'blue' },
                { icon: Smartphone, title: 'Mobile Apps', desc: 'iOS & Android Native', color: 'teal' },
                { icon: Users, title: '5 User Roles', desc: 'Role-based Access', color: 'purple' },
                { icon: Globe, title: 'Bilingual', desc: 'Arabic & English RTL', color: 'green' }
              ].map((feature, index) => (
                <div key={index} className="glass-card p-6 hover:scale-105 transition-all duration-300" style={{animationDelay: `${index * 0.1}s`}}>
                  <div className={`w-14 h-14 bg-${feature.color}-100 dark:bg-${feature.color}-900/30 rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <feature.icon className={`w-7 h-7 text-${feature.color}-600`} />
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-lg">{feature.title}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{feature.desc}</p>
                </div>
              ))}
            </div>

            {/* Dashboard Preview */}
            <div className="glass-card p-8 mb-12 animate-fade-in max-w-4xl mx-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Live Dashboard Preview</h3>
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-6">
                <div className="glass-card p-4 text-center">
                  <div className="text-3xl font-bold text-blue-500 mb-2">1,234</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Active Students</div>
                </div>
                <div className="glass-card p-4 text-center">
                  <div className="text-3xl font-bold text-teal-500 mb-2">56</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Live Courses</div>
                </div>
                <div className="glass-card p-4 text-center">
                  <div className="text-3xl font-bold text-purple-500 mb-2">89%</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Attendance Rate</div>
                </div>
              </div>

              <div className="flex justify-center space-x-6">
                {/* Mobile App Previews */}
                <div className="w-20 h-36 glass-card rounded-2xl p-2 transform rotate-12 hover:rotate-6 transition-transform">
                  <div className="w-full h-full bg-gradient-to-b from-blue-500 to-blue-600 rounded-xl p-2 text-white">
                    <div className="text-xs font-bold mb-2">Itkan</div>
                    <div className="space-y-1">
                      <div className="w-full h-1 bg-white/30 rounded"></div>
                      <div className="w-3/4 h-1 bg-white/30 rounded"></div>
                      <div className="w-full h-6 bg-white/20 rounded mt-3"></div>
                      <div className="w-full h-4 bg-white/20 rounded"></div>
                    </div>
                  </div>
                </div>

                <div className="w-20 h-36 glass-card rounded-2xl p-2 transform -rotate-12 hover:-rotate-6 transition-transform">
                  <div className="w-full h-full bg-gradient-to-b from-teal-500 to-teal-600 rounded-xl p-2 text-white">
                    <div className="text-xs font-bold mb-2" dir="rtl">إتقان</div>
                    <div className="space-y-1">
                      <div className="w-full h-1 bg-white/30 rounded"></div>
                      <div className="w-2/3 h-1 bg-white/30 rounded"></div>
                      <div className="w-full h-6 bg-white/20 rounded mt-3"></div>
                      <div className="w-full h-4 bg-white/20 rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12 animate-fade-in">
              <Link
                to="/login"
                className="btn-primary inline-flex items-center justify-center text-lg px-10 py-5 shadow-xl hover:shadow-2xl transition-all duration-300 group"
              >
                <Monitor className="mr-3 w-6 h-6" />
                {t('home.hero.getStarted', 'Access Dashboard')}
                <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" />
              </Link>
              <button className="btn-secondary inline-flex items-center justify-center text-lg px-10 py-5 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <Play className="mr-3 w-6 h-6 group-hover:scale-110 transition-transform" />
                {t('home.hero.watchDemo', 'Watch Demo')}
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-500 dark:text-gray-400 animate-fade-in">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>Trusted by 100+ Universities</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>99.9% Uptime SLA</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>24/7 Support</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>Enterprise Security</span>
              </div>
            </div>
          </div>
        </div>

          {/* Stats Section */}
          <div className="mt-20 glass-card p-8 animate-fade-in">
            <div className="text-center mb-8">
              <p className="text-gray-600 dark:text-gray-400 text-lg">
                {t('home.hero.trusted', 'Trusted by leading educational institutions worldwide')}
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-500 mb-2">50K+</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">{t('home.hero.stats.students', 'Active Students')}</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-teal-500 mb-2">100+</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">{t('home.hero.stats.universities', 'Universities')}</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-500 mb-2">15+</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">{t('home.hero.stats.countries', 'Countries')}</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-500 mb-2">99.9%</div>
                <div className="text-gray-600 dark:text-gray-400 text-sm">{t('home.hero.stats.uptime', 'Uptime SLA')}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="glass-card p-3 rounded-full">
            <ArrowRight className="w-6 h-6 text-gray-600 dark:text-gray-400 transform rotate-90" />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <div className="glass-card inline-block p-6 mb-8">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {t('home.features.title', 'Complete University Management Platform')}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl">
                {t('home.features.subtitle', 'Everything your university needs: Web dashboard, mobile apps, role-based access, and comprehensive academic management')}
              </p>
            </div>
          </div>
          
          {/* Platform Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <div className="glass-card p-8 animate-slide-up">
              <div className="flex items-center mb-6">
                <Monitor className="w-12 h-12 text-blue-500 mr-4" />
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Web Dashboard</h3>
                  <p className="text-gray-600 dark:text-gray-400">Complete administrative interface</p>
                </div>
              </div>
              <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Role-based dashboards for all user types</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Real-time analytics and reporting</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Course management and content delivery</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Student enrollment and tracking</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Financial management and invoicing</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Academic calendar and scheduling</li>
              </ul>
            </div>

            <div className="glass-card p-8 animate-slide-up">
              <div className="flex items-center mb-6">
                <Smartphone className="w-12 h-12 text-teal-500 mr-4" />
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Mobile Applications</h3>
                  <p className="text-gray-600 dark:text-gray-400">Native iOS & Android apps</p>
                </div>
              </div>
              <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Full feature parity with web platform</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Offline content access and sync</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Push notifications for announcements</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Real-time messaging and chat</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />Assignment submission and grading</li>
                <li className="flex items-center"><CheckCircle className="w-5 h-5 text-green-500 mr-3" />QR code scanning for attendance</li>
              </ul>
            </div>
          </div>

          {/* Feature Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Users,
                title: t('home.features.roles.title', 'Multi-Role System'),
                description: t('home.features.roles.desc', 'Students, Teachers, Admins, Finance Officers, and Staff with customized dashboards'),
                features: ['Role-based permissions', 'Custom dashboards', 'Workflow automation', 'Access control']
              },
              {
                icon: MessageSquare,
                title: t('home.features.messaging.title', 'Real-time Communication'),
                description: t('home.features.messaging.desc', 'Instant messaging, announcements, and notifications'),
                features: ['Instant messaging', 'Group chats', 'Announcements', 'Email integration']
              },
              {
                icon: BookOpen,
                title: t('home.features.courses.title', 'Course Management'),
                description: t('home.features.courses.desc', 'Complete course lifecycle management'),
                features: ['Course creation', 'Content delivery', 'Enrollment management', 'Progress tracking']
              },
              {
                icon: BarChart3,
                title: t('home.features.analytics.title', 'Advanced Analytics'),
                description: t('home.features.analytics.desc', 'Comprehensive reporting and insights'),
                features: ['Performance dashboards', 'Custom reports', 'Data visualization', 'Export capabilities']
              },
              {
                icon: Calendar,
                title: t('home.features.calendar.title', 'Academic Calendar'),
                description: t('home.features.calendar.desc', 'Schedule and event management'),
                features: ['Class scheduling', 'Exam planning', 'Event management', 'Resource booking']
              },
              {
                icon: Award,
                title: t('home.features.assessment.title', 'Assessment & Grading'),
                description: t('home.features.assessment.desc', 'Comprehensive evaluation system'),
                features: ['Online exams', 'Automated grading', 'Transcript generation', 'Grade analytics']
              },
              {
                icon: Globe,
                title: t('home.features.multilingual.title', 'Arabic & English'),
                description: t('home.features.multilingual.desc', 'Full bilingual support with RTL'),
                features: ['RTL interface', 'Arabic fonts', 'Cultural localization', 'Language switching']
              },
              {
                icon: Shield,
                title: t('home.features.security.title', 'Enterprise Security'),
                description: t('home.features.security.desc', 'Advanced security and compliance'),
                features: ['Data encryption', 'Secure authentication', 'Audit trails', 'GDPR compliance']
              },
              {
                icon: Zap,
                title: t('home.features.integration.title', 'System Integration'),
                description: t('home.features.integration.desc', 'Connect with existing university systems'),
                features: ['API integration', 'Data import/export', 'SSO support', 'Third-party plugins']
              }
            ].map((feature, index) => (
              <div key={index} className="glass-card p-6 hover:scale-105 transition-all duration-300 animate-scale-in">
                <div className="flex items-center mb-4">
                  <div className="glass-card p-3 mr-4">
                    <feature.icon className="w-8 h-8 text-blue-500" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{feature.title}</h3>
                  </div>
                </div>
                <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.features.map((item, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <div className="glass-card inline-block p-6 mb-8">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {t('home.howItWorks.title', 'How Itkan Works')}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl">
                {t('home.howItWorks.subtitle', 'Deploy complete university management system in three simple steps')}
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                title: t('home.howItWorks.step1.title', 'Setup & Configuration'),
                description: t('home.howItWorks.step1.desc', 'Install Itkan platform, configure university settings, and set up initial admin accounts'),
                icon: Monitor,
                details: ['Platform installation', 'University branding', 'Initial configuration', 'Admin account setup']
              },
              {
                step: '02',
                title: t('home.howItWorks.step2.title', 'User Management'),
                description: t('home.howItWorks.step2.desc', 'Create user accounts, assign roles, and configure permissions for all stakeholders'),
                icon: Users,
                details: ['User account creation', 'Role assignment', 'Permission configuration', 'Department setup']
              },
              {
                step: '03',
                title: t('home.howItWorks.step3.title', 'Go Live'),
                description: t('home.howItWorks.step3.desc', 'Launch courses, enable mobile apps, and start managing your university digitally'),
                icon: Rocket,
                details: ['Course creation', 'Mobile app deployment', 'Training & support', 'Full operation']
              }
            ].map((step, index) => (
              <div key={index} className="text-center animate-slide-up" style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-card p-8 mb-6">
                  <div className="relative mb-6">
                    <div className="w-20 h-20 glass-card rounded-full flex items-center justify-center mx-auto">
                      <step.icon className="w-10 h-10 text-blue-500" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      {step.step}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">{step.title}</h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-6">{step.description}</p>
                  <ul className="space-y-2 text-left">
                    {step.details.map((detail, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Specifications Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <div className="glass-card inline-block p-6 mb-8">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {t('home.tech.title', 'Technical Specifications')}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl">
                {t('home.tech.subtitle', 'Built with modern technology stack for scalability, security, and performance')}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <div className="glass-card p-8 animate-slide-up">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <Monitor className="w-8 h-8 text-blue-500 mr-3" />
                Frontend Technologies
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="glass-card p-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Web Dashboard</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• React 18+ with TypeScript</li>
                    <li>• Vite for fast development</li>
                    <li>• Tailwind CSS styling</li>
                    <li>• Redux Toolkit state management</li>
                    <li>• React Router navigation</li>
                    <li>• Glass morphism design</li>
                  </ul>
                </div>
                <div className="glass-card p-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Mobile Apps</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• React Native with Expo</li>
                    <li>• Native iOS & Android</li>
                    <li>• React Navigation</li>
                    <li>• Offline data sync</li>
                    <li>• Push notifications</li>
                    <li>• Biometric authentication</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="glass-card p-8 animate-slide-up">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <Shield className="w-8 h-8 text-green-500 mr-3" />
                Backend Infrastructure
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="glass-card p-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Server Technology</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• Django 4.2+ framework</li>
                    <li>• Django REST Framework</li>
                    <li>• PostgreSQL database</li>
                    <li>• Redis for caching</li>
                    <li>• Celery task queue</li>
                    <li>• WebSocket real-time</li>
                  </ul>
                </div>
                <div className="glass-card p-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Security & Deployment</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• JWT authentication</li>
                    <li>• Role-based permissions</li>
                    <li>• Data encryption</li>
                    <li>• Docker containerization</li>
                    <li>• Cloud deployment ready</li>
                    <li>• Automated backups</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* About Itkan */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="animate-fade-in">
              <div className="glass-card p-8">
                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                  {t('home.about.title', 'About Itkan (إتقان)')}
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                  {t('home.about.description', 'Itkan, meaning "mastery" in Arabic, represents our commitment to educational excellence. We provide a comprehensive university management system that combines modern web technology with native mobile applications.')}
                </p>
                <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                  {t('home.about.mission', 'Our mission is to empower educational institutions with cutting-edge technology that enhances learning experiences, streamlines administrative processes, and fosters academic success.')}
                </p>
              </div>
            </div>

            <div className="animate-slide-up">
              <div className="space-y-6">
                {[
                  {
                    icon: Monitor,
                    title: t('home.about.platform.title', 'Dual Platform Architecture'),
                    description: t('home.about.platform.desc', 'Responsive web dashboard for administrators and native mobile apps for students and faculty')
                  },
                  {
                    icon: Users,
                    title: t('home.about.roles.title', 'Comprehensive Role Management'),
                    description: t('home.about.roles.desc', 'Five distinct user roles with customized interfaces: Students, Teachers, Admins, Finance Officers, and Staff')
                  },
                  {
                    icon: Globe,
                    title: t('home.about.bilingual.title', 'True Bilingual Support'),
                    description: t('home.about.bilingual.desc', 'Complete Arabic and English interface with RTL support, Arabic fonts, and cultural localization')
                  },
                  {
                    icon: MessageSquare,
                    title: t('home.about.communication.title', 'Real-time Communication'),
                    description: t('home.about.communication.desc', 'Instant messaging, announcements, notifications, and role-based communication channels')
                  }
                ].map((item, index) => (
                  <div key={index} className="glass-card p-4">
                    <div className="flex items-start space-x-4">
                      <div className="glass-card p-3 flex-shrink-0">
                        <item.icon className="w-6 h-6 text-blue-500" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{item.title}</h3>
                        <p className="text-gray-600 dark:text-gray-400">{item.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Platform Statistics */}
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-16 mt-16">
            <div className="relative animate-slide-up">
              <div className="glass-card p-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Platform Statistics</h3>
                <div className="grid grid-cols-2 gap-6">
                  <div className="glass-card p-4 text-center">
                    <div className="text-3xl font-bold mb-2 text-blue-500">100%</div>
                    <div className="text-gray-600 dark:text-gray-400">Feature Parity</div>
                    <div className="text-sm text-gray-500 dark:text-gray-500">Web & Mobile</div>
                  </div>
                  <div className="glass-card p-4 text-center">
                    <div className="text-3xl font-bold mb-2 text-teal-500">5</div>
                    <div className="text-gray-600 dark:text-gray-400">User Roles</div>
                    <div className="text-sm text-gray-500 dark:text-gray-500">Role-based Access</div>
                  </div>
                  <div className="glass-card p-4 text-center">
                    <div className="text-3xl font-bold mb-2 text-purple-500">2</div>
                    <div className="text-gray-600 dark:text-gray-400">Languages</div>
                    <div className="text-sm text-gray-500 dark:text-gray-500">Arabic & English</div>
                  </div>
                  <div className="glass-card p-4 text-center">
                    <div className="text-3xl font-bold mb-2 text-green-500">24/7</div>
                    <div className="text-gray-600 dark:text-gray-400">Availability</div>
                    <div className="text-sm text-gray-500 dark:text-gray-500">Cloud-based</div>
                  </div>
                </div>

                <div className="mt-8 glass-card p-6">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Key Capabilities</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                      <span className="text-gray-700 dark:text-gray-300">Real-time data synchronization</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                      <span className="text-gray-700 dark:text-gray-300">Offline mobile functionality</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                      <span className="text-gray-700 dark:text-gray-300">Advanced analytics dashboard</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                      <span className="text-gray-700 dark:text-gray-300">Automated report generation</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <div className="glass-card inline-block p-6 mb-8">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {t('home.testimonials.title', 'Trusted by Educational Institutions')}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                {t('home.testimonials.subtitle', 'Universities and colleges worldwide rely on Itkan for comprehensive academic management')}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: 'Dr. Sarah Al-Zahra',
                role: 'Dean, King Abdulaziz University',
                content: 'Itkan transformed our university operations. The bilingual interface and mobile apps increased student engagement by 85%. Perfect for our Arabic-speaking students.',
                rating: 5
              },
              {
                name: 'Ahmed Hassan',
                role: 'IT Director, American University of Beirut',
                content: 'Complete LMS solution with web and mobile. Role-based access for all staff types. Implementation took just 2 weeks. Outstanding Arabic RTL support.',
                rating: 5
              },
              {
                name: 'Dr. Maria Rodriguez',
                role: 'Professor, Cairo University',
                content: 'Real-time messaging, advanced analytics, and mobile access revolutionized our teaching. Students love the native mobile apps with offline support.',
                rating: 5
              }
            ].map((testimonial, index) => (
              <div key={index} className="glass-card p-8 animate-scale-in" style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-6 italic leading-relaxed">"{testimonial.content}"</p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">{testimonial.name}</div>
                    <div className="text-gray-500 dark:text-gray-400 text-sm">{testimonial.role}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="glass-card p-12 text-center animate-fade-in">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              {t('home.cta.title', 'Ready to Transform Your University?')}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
              {t('home.cta.subtitle', 'Join educational institutions worldwide using Itkan for complete university management. Web dashboard, mobile apps, role-based access, and full Arabic support included.')}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="glass-card p-6">
                <Monitor className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Web Dashboard</h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">Complete administrative interface with role-based access</p>
              </div>
              <div className="glass-card p-6">
                <Smartphone className="w-12 h-12 text-teal-500 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Mobile Apps</h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">Native iOS & Android applications with offline support</p>
              </div>
              <div className="glass-card p-6">
                <Globe className="w-12 h-12 text-purple-500 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Arabic Support</h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">Full RTL interface with Arabic localization</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/login"
                className="btn-primary inline-flex items-center justify-center"
              >
                {t('home.cta.getStarted', 'Access Dashboard')}
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
              <button className="btn-secondary inline-flex items-center justify-center">
                <Download className="mr-2 w-5 h-5" />
                {t('home.cta.downloadApp', 'Download Mobile Apps')}
              </button>
            </div>

            <div className="mt-8 flex flex-wrap justify-center gap-6 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                <span>Free trial available</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                <span>Setup support included</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                <span>24/7 technical support</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
