import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store';
import VideoLibrary from '../components/video/VideoLibrary';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  Video, 
  Upload, 
  BarChart3, 
  Settings,
  Play,
  Clock,
  Eye,
  Users
} from 'lucide-react';

const VideoStreaming: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAppSelector((state) => state.auth);
  const [activeTab, setActiveTab] = useState('library');

  // Check if user can upload videos
  const canUpload = user?.role === 'teacher' || user?.role === 'admin' || user?.role === 'super_admin';

  // Mock analytics data (would come from API)
  const analyticsData = {
    totalVideos: 45,
    totalViews: 1234,
    totalWatchTime: 8760, // minutes
    uniqueViewers: 567
  };

  const formatWatchTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {t('video.title')}
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  {t('video.library.title')}
                </p>
              </div>
              
              {/* Quick Stats */}
              {canUpload && (
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <div className="flex items-center">
                      <Video className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          {t('video.totalVideos')}
                        </p>
                        <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          {analyticsData.totalVideos}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <div className="flex items-center">
                      <Eye className="w-8 h-8 text-green-600 dark:text-green-400" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-600 dark:text-green-400">
                          {t('video.totalViews')}
                        </p>
                        <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                          {analyticsData.totalViews.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <div className="flex items-center">
                      <Clock className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                          {t('video.watchTime')}
                        </p>
                        <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                          {formatWatchTime(analyticsData.totalWatchTime)}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                    <div className="flex items-center">
                      <Users className="w-8 h-8 text-orange-600 dark:text-orange-400" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-orange-600 dark:text-orange-400">
                          {t('video.uniqueViewers')}
                        </p>
                        <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                          {analyticsData.uniqueViewers}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="library" className="flex items-center space-x-2">
              <Video className="w-4 h-4" />
              <span>{t('video.library.title')}</span>
            </TabsTrigger>
            
            {canUpload && (
              <>
                <TabsTrigger value="upload" className="flex items-center space-x-2">
                  <Upload className="w-4 h-4" />
                  <span>{t('video.upload.title')}</span>
                </TabsTrigger>
                
                <TabsTrigger value="analytics" className="flex items-center space-x-2">
                  <BarChart3 className="w-4 h-4" />
                  <span>{t('video.analytics')}</span>
                </TabsTrigger>
                
                <TabsTrigger value="settings" className="flex items-center space-x-2">
                  <Settings className="w-4 h-4" />
                  <span>{t('video.settings')}</span>
                </TabsTrigger>
              </>
            )}
          </TabsList>

          {/* Video Library Tab */}
          <TabsContent value="library" className="mt-6">
            <VideoLibrary
              showUpload={canUpload}
              showFilters={true}
              className="w-full"
            />
          </TabsContent>

          {/* Upload Tab */}
          {canUpload && (
            <TabsContent value="upload" className="mt-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {t('video.upload.title')}
                </h3>
                <VideoUpload
                  courseId="" // Would be selected from a dropdown
                  onUploadComplete={(videos) => {
                    console.log('Upload completed:', videos);
                    // Refresh library or show success message
                  }}
                />
              </div>
            </TabsContent>
          )}

          {/* Analytics Tab */}
          {canUpload && (
            <TabsContent value="analytics" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Overview Stats */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {t('video.analytics.overview')}
                  </h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        {t('video.totalVideos')}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {analyticsData.totalVideos}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        {t('video.totalViews')}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {analyticsData.totalViews.toLocaleString()}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        {t('video.watchTime')}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {formatWatchTime(analyticsData.totalWatchTime)}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        {t('video.uniqueViewers')}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {analyticsData.uniqueViewers}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Top Videos */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {t('video.analytics.topVideos')}
                  </h3>
                  
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                    <p>{t('video.analytics.comingSoon')}</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          )}

          {/* Settings Tab */}
          {canUpload && (
            <TabsContent value="settings" className="mt-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {t('video.settings')}
                </h3>
                
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <Settings className="w-12 h-12 mx-auto mb-2" />
                  <p>{t('video.settings.comingSoon')}</p>
                </div>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default VideoStreaming;
