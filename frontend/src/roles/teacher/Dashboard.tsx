import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '../../store';
import { GraduationCap, Users, FileText, Calendar, BookOpen, ClipboardList, MessageSquare } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON>hart } from '../../components/charts';
import { apiService } from '../../services/api';
import { processStudentsByStatus, processUsersByDepartment } from '../../utils/chartUtils';

const TeacherDashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, token } = useAppSelector((state) => state.auth);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeacherData = async () => {
      try {
        if (!token) return;

        apiService.setToken(token);

        // Fetch teacher-specific data
        const [courses, assignments, grades, students] = await Promise.all([
          apiService.request('/courses/?instructor=me'),
          apiService.request('/assignments/'),
          apiService.request('/grades/'),
          apiService.request('/users/?role=student')
        ]);

        setDashboardData({
          courses: courses.results || courses,
          assignments: assignments.results || assignments,
          grades: grades.results || grades,
          students: students.results || students
        });
      } catch (error) {
        console.error('Failed to fetch teacher dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeacherData();
  }, [token]);

  // Helper functions to process real data for charts
  const processGradeDistribution = (grades: any[]) => {
    const gradeCounts = grades.reduce((acc, grade) => {
      const letter = grade.letterGrade || grade.grade || 'N/A';
      acc[letter] = (acc[letter] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const colors = { 'A': '#10B981', 'B': '#3B82F6', 'C': '#F59E0B', 'D': '#EF4444', 'F': '#6B7280' };
    return Object.entries(gradeCounts).map(([grade, count]) => ({
      name: grade,
      value: count,
      color: colors[grade as keyof typeof colors] || '#6B7280'
    }));
  };

  const processCourseEnrollment = (courses: any[]) => {
    return courses.map((course, index) => ({
      name: course.name || course.title || `Course ${index + 1}`,
      value: course.enrollmentCount || course.enrolled_students || 0,
      color: ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6', '#06B6D4'][index % 5]
    }));
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="glass-card">
        <div className="flex items-center space-x-3">
          <GraduationCap className="w-8 h-8 text-green-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('dashboard.welcome')} {user?.firstName || user?.username}!
            </h1>
            <p className="text-gray-600">{t('dashboard.teacherDescription', 'Teacher Dashboard')}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-500 text-white">
              <GraduationCap className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('dashboard.myCourses', 'My Courses')}</p>
              <p className="text-2xl font-bold text-gray-900">4</p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-500 text-white">
              <Users className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('dashboard.totalStudents', 'Total Students')}</p>
              <p className="text-2xl font-bold text-gray-900">156</p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-yellow-500 text-white">
              <FileText className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('dashboard.assignmentsToGrade', 'Assignments to Grade')}</p>
              <p className="text-2xl font-bold text-gray-900">28</p>
            </div>
          </div>
        </div>

        <div className="glass-card">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-500 text-white">
              <Calendar className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('dashboard.classesToday', 'Classes Today')}</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="glass-card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('dashboard.quickActions', 'Quick Actions')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => navigate('/courses')}
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover: transition-colors"
          >
            <BookOpen className="w-6 h-6 text-blue-600" />
            <div className="text-left">
              <p className="font-medium text-gray-900">{t('teacher.manageCourses', 'Manage Courses')}</p>
              <p className="text-sm text-gray-600">{t('teacher.manageCoursesDesc', 'View and edit your courses')}</p>
            </div>
          </button>

          <button
            onClick={() => navigate('/assignments')}
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover: transition-colors"
          >
            <ClipboardList className="w-6 h-6 text-green-600" />
            <div className="text-left">
              <p className="font-medium text-gray-900">{t('teacher.gradeAssignments', 'Grade Assignments')}</p>
              <p className="text-sm text-gray-600">{t('teacher.gradeAssignmentsDesc', 'Review and grade student work')}</p>
            </div>
          </button>

          <button
            onClick={() => navigate('/messaging')}
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover: transition-colors"
          >
            <MessageSquare className="w-6 h-6 text-purple-600" />
            <div className="text-left">
              <p className="font-medium text-gray-900">{t('teacher.messageStudents', 'Message Students')}</p>
              <p className="text-sm text-gray-600">{t('teacher.messageStudentsDesc', 'Communicate with your students')}</p>
            </div>
          </button>

          <button
            onClick={() => navigate('/assessments')}
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover: transition-colors"
          >
            <ClipboardList className="w-6 h-6 text-indigo-600" />
            <div className="text-left">
              <p className="font-medium text-gray-900">{t('teacher.manageAssessments', 'Manage Assessments')}</p>
              <p className="text-sm text-gray-600">{t('teacher.manageAssessmentsDesc', 'Create and grade assessments')}</p>
            </div>
          </button>
        </div>
      </div>

      {/* Teacher Analytics Charts */}
      {!loading && dashboardData && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <PieChart
            data={processGradeDistribution(dashboardData.grades)}
            title={t('charts.gradeDistribution', 'Grade Distribution')}
            height={300}
          />
          <BarChart
            data={processCourseEnrollment(dashboardData.courses)}
            title={t('charts.courseEnrollment', 'Course Enrollment')}
            height={300}
            color="#8B5CF6"
          />
          <BarChart
            data={processStudentsByStatus(dashboardData.students)}
            title={t('charts.studentStatus', 'Student Status')}
            height={300}
            color="#10B981"
          />
        </div>
      )}

      <div className="glass-card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('dashboard.teachingOverview', 'Teaching Overview')}</h2>
        <p className="text-gray-600">
          {t('dashboard.teachingOverviewDescription', 'Course management, grading, and student interaction tools will be available here.')}
        </p>
      </div>
    </div>
  );
};

export default TeacherDashboard;
