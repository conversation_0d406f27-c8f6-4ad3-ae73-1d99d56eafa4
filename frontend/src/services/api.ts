// API service for UMLS application
import { securityService } from './securityServiceSimple';

const API_BASE_URL = 'http://localhost:8001/api';

class ApiService {
  private token: string | null = null;

  setToken(token: string | null) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    // Validate network security
    const isNetworkSecure = await securityService.validateNetworkSecurity();
    if (!isNetworkSecure) {
      console.warn('⚠️ Insecure network detected');
    }

    // Get security headers
    const securityHeaders = await securityService.getSecurityHeaders(
      options.method || 'GET',
      endpoint,
      typeof options.body === 'string' ? options.body : ''
    );

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...securityHeaders,
        ...options.headers,
      },
      ...options,
    };

    // Update last activity
    securityService.updateLastActivity();

    const response = await fetch(url, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle session timeout
      if (response.status === 401) {
        securityService.handleSessionTimeout();
      }

      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Authentication
  async login(email: string, password: string) {
    // Detect security threats before login
    const threats = await securityService.detectSecurityThreats();
    if (threats.length > 0) {
      console.warn('🚨 Security threats detected:', threats);
    }

    // Get device fingerprint for login
    const deviceFingerprint = securityService.getDeviceFingerprint();

    const loginData = {
      email,
      password,
      device_info: deviceFingerprint ? {
        device_id: deviceFingerprint.deviceId,
        platform: 'web',
        user_agent: deviceFingerprint.userAgent,
        screen_resolution: deviceFingerprint.screenResolution,
        timezone: deviceFingerprint.timezone
      } : undefined
    };

    return this.request('/auth/login/', {
      method: 'POST',
      body: JSON.stringify(loginData),
    });
  }

  async logout() {
    return this.request('/auth/logout/', {
      method: 'POST',
    });
  }

  async refreshToken(refreshToken: string) {
    return this.request('/auth/refresh/', {
      method: 'POST',
      body: JSON.stringify({ refresh: refreshToken }),
    });
  }

  async getCurrentUser() {
    const response = await this.request('/users/profile/') as any;
    return response.user; // Extract user data from the profile response
  }

  // Users
  async getProfile() {
    return this.request('/users/profile/');
  }



  async updateProfile(data: any) {
    return this.request('/users/profile/', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async getUsers(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/users/${queryString}`);
  }

  async createUser(userData: any) {
    return this.request('/users/', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(userId: number, userData: any) {
    return this.request(`/users/${userId}/`, {
      method: 'PATCH',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(userId: number) {
    return this.request(`/users/${userId}/`, {
      method: 'DELETE',
    });
  }

  async getUserProfile() {
    return this.request('/users/profile/');
  }

  async updateUserProfile(profileData: any) {
    return this.request('/users/profile/', {
      method: 'PATCH',
      body: JSON.stringify(profileData),
    });
  }

  // Enhanced user management methods
  async bulkImportUsers(file: FormData) {
    return this.request('/users/bulk-import/', {
      method: 'POST',
      body: file,
      headers: {
        // Don't set Content-Type for FormData, but keep Authorization
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      }
    });
  }

  async bulkUpdateUsers(data: { user_ids: number[], action: string, [key: string]: any }) {
    return this.request('/users/bulk-update/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async exportUsers(params?: string) {
    const url = params ? `/users/export/?${params}` : '/users/export/';
    return this.request(url);
  }

  async downloadImportTemplate() {
    return this.request('/users/import-template/');
  }

  async resetUserPassword(userId: number, password?: string) {
    return this.request(`/users/${userId}/reset-password/`, {
      method: 'POST',
      body: JSON.stringify({ password }),
    });
  }

  async toggleUserStatus(userId: number) {
    return this.request(`/users/${userId}/toggle-status/`, {
      method: 'POST',
    });
  }

  async getUserStatistics() {
    return this.request('/users/statistics/');
  }

  // Communications/Messaging methods
  async getConversations() {
    return this.request('/communications/conversations/');
  }

  async getConversation(id: number) {
    return this.request(`/communications/conversations/${id}/`);
  }

  async createConversation(data: any) {
    return this.request('/communications/conversations/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async startConversation(data: any) {
    return this.request('/communications/conversations/start/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getMessages(conversationId: number) {
    return this.request(`/communications/conversations/${conversationId}/messages/`);
  }

  async sendMessage(conversationId: number, data: FormData | any) {
    const isFormData = data instanceof FormData;
    return this.request(`/communications/conversations/${conversationId}/messages/`, {
      method: 'POST',
      body: isFormData ? data : JSON.stringify(data),
      headers: isFormData ? {
        // Don't set Content-Type for FormData, but keep Authorization
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      } : { 'Content-Type': 'application/json' },
    });
  }

  async markMessageAsRead(messageId: number) {
    return this.request(`/communications/messages/${messageId}/read/`, {
      method: 'POST',
    });
  }

  async addParticipant(conversationId: number, userId: number) {
    return this.request(`/communications/conversations/${conversationId}/add-participant/`, {
      method: 'POST',
      body: JSON.stringify({ user_id: userId }),
    });
  }

  async removeParticipant(conversationId: number, userId: number) {
    return this.request(`/communications/conversations/${conversationId}/remove-participant/`, {
      method: 'POST',
      body: JSON.stringify({ user_id: userId }),
    });
  }

  async searchConversations(query: string) {
    return this.request(`/communications/search/?q=${encodeURIComponent(query)}`);
  }

  async getUnreadMessageCount() {
    return this.request('/communications/unread-count/');
  }

  // Enhanced Notification methods
  async getNotificationPreferences() {
    return this.request('/notifications/preferences/');
  }

  async updateNotificationPreferences(data: any) {
    return this.request('/notifications/preferences/', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async createNotification(data: any) {
    return this.request('/notifications/create/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getNotificationStatistics() {
    return this.request('/notifications/statistics/');
  }

  async dismissNotification(notificationId: number) {
    return this.request(`/notifications/${notificationId}/dismiss/`, {
      method: 'POST',
    });
  }

  async getNotificationsByType(params: any) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/notifications/by-type/?${queryString}`);
  }

  async sendBulkNotifications(data: any) {
    return this.request('/notifications/bulk-send/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Analytics and Reporting methods
  async generateReport(reportType: string, filters: any = {}) {
    return this.request('/analytics/reports/generate/', {
      method: 'POST',
      body: JSON.stringify({
        report_type: reportType,
        filters: filters
      }),
    });
  }

  async getAnalyticsReports() {
    return this.request('/analytics/reports/');
  }

  async getSavedCharts() {
    return this.request('/analytics/charts/saved/');
  }

  async getAnalyticsReport(reportId: number) {
    return this.request(`/analytics/reports/${reportId}/`);
  }

  async deleteAnalyticsReport(reportId: number) {
    return this.request(`/analytics/reports/${reportId}/`, {
      method: 'DELETE',
    });
  }

  async getDashboardMetrics(period: string = 'month', department: string = 'all') {
    return this.request(`/analytics/dashboard-metrics/?period=${period}&department=${department}`);
  }

  async getStudentPerformanceAnalytics(filters: any = {}) {
    return this.request('/analytics/student-performance/', {
      method: 'POST',
      body: JSON.stringify(filters),
    });
  }

  async getCourseAnalytics(filters: any = {}) {
    return this.request('/analytics/course-analytics/', {
      method: 'POST',
      body: JSON.stringify(filters),
    });
  }

  async getAttendanceAnalytics(filters: any = {}) {
    return this.request('/analytics/attendance-analytics/', {
      method: 'POST',
      body: JSON.stringify(filters),
    });
  }

  async getGradeAnalytics(filters: any = {}) {
    return this.request('/analytics/grade-analytics/', {
      method: 'POST',
      body: JSON.stringify(filters),
    });
  }

  async getEnrollmentTrends(filters: any = {}) {
    return this.request('/analytics/enrollment-trends/', {
      method: 'POST',
      body: JSON.stringify(filters),
    });
  }

  async getCommunicationAnalytics(filters: any = {}) {
    return this.request('/analytics/communication-analytics/', {
      method: 'POST',
      body: JSON.stringify(filters),
    });
  }

  async exportAnalyticsData(reportType: string, format: string = 'csv') {
    return this.request(`/analytics/export/?type=${reportType}&format=${format}`, {
      method: 'GET',
    });
  }

  async scheduleReport(data: any) {
    return this.request('/analytics/schedule-report/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getScheduledReports() {
    return this.request('/analytics/scheduled-reports/');
  }

  // System Monitoring methods
  async getSystemHealth() {
    return this.request('/system/health/');
  }

  async getSystemAlerts() {
    return this.request('/system/alerts/');
  }

  async acknowledgeAlert(alertId: number) {
    return this.request(`/system/alerts/${alertId}/acknowledge/`, {
      method: 'POST',
    });
  }

  async resolveAlert(alertId: number, notes: string = '') {
    return this.request(`/system/alerts/${alertId}/resolve/`, {
      method: 'POST',
      body: JSON.stringify({ notes }),
    });
  }

  async getPerformanceMetrics(params: any = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/system/performance/?${queryString}`);
  }

  async getAuditLogs(params: any = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/system/audit/?${queryString}`);
  }

  async getSystemConfiguration() {
    return this.request('/system/config/');
  }

  async updateSystemConfiguration(config: any) {
    return this.request('/system/config/', {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  }

  async createBackup(backupType: string = 'full') {
    return this.request('/system/backup/', {
      method: 'POST',
      body: JSON.stringify({ backup_type: backupType }),
    });
  }

  async getBackupHistory() {
    return this.request('/system/backups/');
  }

  async downloadBackup(backupId: number) {
    return this.request(`/system/backups/${backupId}/download/`);
  }

  // Advanced search and filtering
  async advancedSearch(query: string, filters: any = {}) {
    return this.request('/search/advanced/', {
      method: 'POST',
      body: JSON.stringify({ query, filters }),
    });
  }

  async getSearchSuggestions(query: string) {
    return this.request(`/search/suggestions/?q=${encodeURIComponent(query)}`);
  }



  async bulkDeleteUsers(userIds: number[]) {
    return this.request('/users/bulk-delete/', {
      method: 'POST',
      body: JSON.stringify({ user_ids: userIds }),
    });
  }

  async bulkEnrollStudents(courseId: number, studentIds: number[]) {
    return this.request(`/courses/${courseId}/bulk-enroll/`, {
      method: 'POST',
      body: JSON.stringify({ student_ids: studentIds }),
    });
  }

  async bulkGradeAssignment(assignmentId: number, grades: any[]) {
    return this.request(`/assignments/${assignmentId}/bulk-grade/`, {
      method: 'POST',
      body: JSON.stringify({ grades }),
    });
  }

  // Content Management API methods
  async getCourseModules(courseId: string) {
    try {
      const response = await this.request(`/content/modules/?course_id=${courseId}`);
      return response.results || response;
    } catch (error) {
      console.error('Failed to fetch course modules:', error);
      // Return mock data for development
      return [
        {
          id: '1',
          title: 'Introduction to Modern Physics',
          description: 'Basic concepts and historical overview',
          status: 'published',
          estimated_duration: 120,
          completion_rate: 85,
          lessons: [
            { id: '1', title: 'Course Overview', content_type: 'video', estimated_duration: 15, progress: { is_completed: true } },
            { id: '2', title: 'Historical Context', content_type: 'document', estimated_duration: 30, progress: { is_completed: false } },
            { id: '3', title: 'Knowledge Check', content_type: 'quiz', estimated_duration: 10, progress: { is_completed: false } }
          ]
        },
        {
          id: '2',
          title: 'Quantum Mechanics Fundamentals',
          description: 'Core principles of quantum mechanics',
          status: 'draft',
          estimated_duration: 180,
          completion_rate: 0,
          lessons: [
            { id: '4', title: 'Wave-Particle Duality', content_type: 'video', estimated_duration: 45, progress: { is_completed: false } },
            { id: '5', title: 'Schrödinger Equation', content_type: 'document', estimated_duration: 60, progress: { is_completed: false } }
          ]
        }
      ];
    }
  }

  async createCourseModule(moduleData: any) {
    return this.request('/content/modules/', {
      method: 'POST',
      body: JSON.stringify(moduleData),
    });
  }

  async updateCourseModule(moduleId: string, moduleData: any) {
    return this.request(`/content/modules/${moduleId}/`, {
      method: 'PUT',
      body: JSON.stringify(moduleData),
    });
  }

  async deleteCourseModule(moduleId: string) {
    return this.request(`/content/modules/${moduleId}/`, {
      method: 'DELETE',
    });
  }

  async reorderModule(moduleId: string, newOrder: number) {
    return this.request(`/content/modules/${moduleId}/reorder/`, {
      method: 'POST',
      body: JSON.stringify({ order_index: newOrder }),
    });
  }

  async getCourseLessons(moduleId: string) {
    return this.request(`/content/lessons/?module_id=${moduleId}`);
  }

  async createLesson(lessonData: any) {
    return this.request('/content/lessons/', {
      method: 'POST',
      body: JSON.stringify(lessonData),
    });
  }

  async updateLesson(lessonId: string, lessonData: any) {
    return this.request(`/content/lessons/${lessonId}/`, {
      method: 'PUT',
      body: JSON.stringify(lessonData),
    });
  }

  async deleteLesson(lessonId: string) {
    return this.request(`/content/lessons/${lessonId}/`, {
      method: 'DELETE',
    });
  }

  async updateLessonProgress(lessonId: string, progressData: any) {
    return this.request(`/content/lessons/${lessonId}/update_progress/`, {
      method: 'POST',
      body: JSON.stringify(progressData),
    });
  }

  async trackContentInteraction(lessonId: string, interactionData: any) {
    return this.request(`/content/lessons/${lessonId}/track_interaction/`, {
      method: 'POST',
      body: JSON.stringify(interactionData),
    });
  }

  async getCourseContentTree(courseId: string) {
    return this.request(`/content/courses/${courseId}/content-tree/`);
  }

  async getStudentProgress(courseId?: string) {
    const params = courseId ? `?course_id=${courseId}` : '';
    return this.request(`/content/progress/modules/${params}`);
  }

  async getLessonProgress(moduleId?: string) {
    const params = moduleId ? `?module_id=${moduleId}` : '';
    return this.request(`/content/progress/lessons/${params}`);
  }

  // Forums API methods
  async getCourseForums(courseId: string) {
    try {
      const response = await this.request(`/forums/forums/?course_id=${courseId}`);
      return response.results || response;
    } catch (error) {
      console.error('Failed to fetch course forums:', error);
      // Return mock data for development
      return [
        {
          id: '1',
          title: 'Course Discussion Forum',
          description: 'Main discussion forum for the course',
          forum_type: 'course_discussion',
          is_active: true,
          topics_count: 3,
          posts_count: 8
        }
      ];
    }
  }

  async getForumTopics(forumId: string) {
    try {
      const response = await this.request(`/forums/topics/?forum_id=${forumId}`);
      return response.results || response;
    } catch (error) {
      console.error('Failed to fetch forum topics:', error);
      // Return mock data for development
      return [
        {
          id: '1',
          title: 'Welcome to Modern Physics Discussion',
          created_by_name: 'Dr. Sarah Johnson',
          created_by_role: 'teacher',
          created_at: '2024-12-20T10:00:00Z',
          last_activity: '2024-12-22T15:30:00Z',
          posts_count: 12,
          views_count: 45,
          is_pinned: true,
          status: 'open',
          is_qa_topic: false,
          tags: [{ name: 'announcement' }, { name: 'welcome' }],
          posts: []
        },
        {
          id: '2',
          title: 'Question about Wave-Particle Duality',
          created_by_name: 'Ahmed Ali',
          created_by_role: 'student',
          created_at: '2024-12-21T14:20:00Z',
          last_activity: '2024-12-22T09:15:00Z',
          posts_count: 5,
          views_count: 23,
          is_pinned: false,
          status: 'open',
          is_qa_topic: true,
          tags: [{ name: 'question' }, { name: 'quantum-mechanics' }],
          posts: [{ is_best_answer: true }]
        }
      ];
    }
  }

  async createForumTopic(topicData: any) {
    return this.request('/forums/topics/', {
      method: 'POST',
      body: JSON.stringify(topicData),
    });
  }

  async updateForumTopic(topicId: string, topicData: any) {
    return this.request(`/forums/topics/${topicId}/`, {
      method: 'PUT',
      body: JSON.stringify(topicData),
    });
  }

  async deleteForumTopic(topicId: string) {
    return this.request(`/forums/topics/${topicId}/`, {
      method: 'DELETE',
    });
  }

  async getTopicPosts(topicId: string) {
    return this.request(`/forums/topics/${topicId}/posts/`);
  }

  async createForumPost(postData: any) {
    return this.request('/forums/posts/', {
      method: 'POST',
      body: JSON.stringify(postData),
    });
  }

  async updateForumPost(postId: string, postData: any) {
    return this.request(`/forums/posts/${postId}/`, {
      method: 'PUT',
      body: JSON.stringify(postData),
    });
  }

  async deleteForumPost(postId: string) {
    return this.request(`/forums/posts/${postId}/`, {
      method: 'DELETE',
    });
  }

  async voteOnPost(postId: string, voteType: 'upvote' | 'downvote') {
    return this.request(`/forums/posts/${postId}/vote/`, {
      method: 'POST',
      body: JSON.stringify({ vote_type: voteType }),
    });
  }

  async followTopic(topicId: string) {
    return this.request(`/forums/topics/${topicId}/follow/`, {
      method: 'POST',
    });
  }

  async unfollowTopic(topicId: string) {
    return this.request(`/forums/topics/${topicId}/follow/`, {
      method: 'DELETE',
    });
  }

  // Data import/export
  async importData(dataType: string, file: File, options: any = {}) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data_type', dataType);
    formData.append('options', JSON.stringify(options));

    return this.request('/data/import/', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        // Don't set Content-Type for FormData
      },
    });
  }

  async exportData(dataType: string, format: string = 'csv', filters: any = {}) {
    return this.request('/data/export/', {
      method: 'POST',
      body: JSON.stringify({ data_type: dataType, format, filters }),
    });
  }

  async getImportHistory() {
    return this.request('/data/imports/');
  }

  async getExportHistory() {
    return this.request('/data/exports/');
  }

  // Integration endpoints
  async syncWithExternalSystem(systemType: string, config: any) {
    return this.request('/integrations/sync/', {
      method: 'POST',
      body: JSON.stringify({ system_type: systemType, config }),
    });
  }

  async getIntegrationStatus() {
    return this.request('/integrations/status/');
  }

  async testIntegration(systemType: string, config: any) {
    return this.request('/integrations/test/', {
      method: 'POST',
      body: JSON.stringify({ system_type: systemType, config }),
    });
  }



  async enrollInCourse(courseId: string | number, data?: any) {
    return this.request(`/courses/${courseId}/enroll/`, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async dropCourse(courseId: string | number, data?: any) {
    return this.request(`/courses/${courseId}/drop/`, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async getCourseWaitlist(courseId: string | number) {
    return this.request(`/courses/${courseId}/waitlist/`);
  }

  async withdrawFromCourse(enrollmentId: number) {
    return this.request(`/courses/enrollments/${enrollmentId}/withdraw/`, {
      method: 'POST',
    });
  }

  async getCourseEnrollments(courseId: number) {
    return this.request(`/courses/${courseId}/enrollments/`);
  }

  async getCourseStats() {
    return this.request('/courses/stats/');
  }

  // Courses
  async getCourses(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/courses/${queryString}`);
  }

  async createCourse(courseData: any) {
    return this.request('/courses/', {
      method: 'POST',
      body: JSON.stringify(courseData),
    });
  }

  async updateCourse(courseId: number, courseData: any) {
    return this.request(`/courses/${courseId}/`, {
      method: 'PATCH',
      body: JSON.stringify(courseData),
    });
  }

  async deleteCourse(courseId: number) {
    return this.request(`/courses/${courseId}/`, {
      method: 'DELETE',
    });
  }

  async getCourse(courseId: number) {
    return this.request(`/courses/${courseId}/`);
  }

  // Departments
  async getDepartments(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/courses/departments/${queryString}`);
  }

  async createDepartment(departmentData: any) {
    return this.request('/courses/departments/', {
      method: 'POST',
      body: JSON.stringify(departmentData),
    });
  }

  async updateDepartment(departmentId: number, departmentData: any) {
    return this.request(`/courses/departments/${departmentId}/`, {
      method: 'PATCH',
      body: JSON.stringify(departmentData),
    });
  }

  async deleteDepartment(departmentId: number) {
    return this.request(`/courses/departments/${departmentId}/`, {
      method: 'DELETE',
    });
  }

  async getDepartmentStats() {
    return this.request('/courses/departments/stats/');
  }

  // Enrollments
  async getEnrollments(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/courses/enrollments/${queryString}`);
  }

  async createEnrollment(enrollmentData: any) {
    return this.request('/courses/enrollments/', {
      method: 'POST',
      body: JSON.stringify(enrollmentData),
    });
  }

  async updateEnrollment(enrollmentId: number, enrollmentData: any) {
    return this.request(`/courses/enrollments/${enrollmentId}/`, {
      method: 'PATCH',
      body: JSON.stringify(enrollmentData),
    });
  }

  async deleteEnrollment(enrollmentId: number) {
    return this.request(`/courses/enrollments/${enrollmentId}/`, {
      method: 'DELETE',
    });
  }

  // Payments
  async getPayments(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/payments/${queryString}`);
  }

  async getPayment(id: number) {
    return this.request(`/payments/${id}/`);
  }

  async createPayment(data: any) {
    return this.request('/payments/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updatePayment(id: number, data: any) {
    return this.request(`/payments/${id}/`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async markPaymentAsPaid(id: number, paymentMethod: string, transactionId?: string) {
    return this.request(`/payments/${id}/mark-paid/`, {
      method: 'POST',
      body: JSON.stringify({
        payment_method: paymentMethod,
        transaction_id: transactionId,
      }),
    });
  }

  async getPaymentSummary(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/payments/summary/${queryString}`);
  }

  async getStudentPaymentHistory(studentId: string) {
    return this.request(`/payments/student/${studentId}/history/`);
  }

  // Fee Types
  async getFeeTypes() {
    return this.request('/payments/fee-types/');
  }

  async createFeeType(data: any) {
    return this.request('/payments/fee-types/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Assignments
  async getAssignments(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/assignments/${queryString}`);
  }

  async createAssignment(assignmentData: any) {
    return this.request('/assignments/', {
      method: 'POST',
      body: JSON.stringify(assignmentData),
    });
  }

  async updateAssignment(assignmentId: string | number, assignmentData: any) {
    return this.request(`/assignments/${assignmentId}/`, {
      method: 'PATCH',
      body: JSON.stringify(assignmentData),
    });
  }

  async deleteAssignment(assignmentId: number) {
    return this.request(`/assignments/${assignmentId}/`, {
      method: 'DELETE',
    });
  }

  async getAssignment(assignmentId: number) {
    return this.request(`/assignments/${assignmentId}/`);
  }

  async publishAssignment(assignmentId: number) {
    return this.request(`/assignments/${assignmentId}/publish/`, {
      method: 'POST',
    });
  }



  async getAssignmentStats() {
    return this.request('/assignments/stats/');
  }

  // Assignment Submissions
  async getAssignmentSubmissions(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/assignments/submissions/${queryString}`);
  }

  async createAssignmentSubmission(submissionData: any) {
    return this.request('/assignments/submissions/', {
      method: 'POST',
      body: JSON.stringify(submissionData),
    });
  }

  async updateAssignmentSubmission(submissionId: number, submissionData: any) {
    return this.request(`/assignments/submissions/${submissionId}/`, {
      method: 'PATCH',
      body: JSON.stringify(submissionData),
    });
  }

  async deleteAssignmentSubmission(submissionId: number) {
    return this.request(`/assignments/submissions/${submissionId}/`, {
      method: 'DELETE',
    });
  }

  async gradeSubmission(submissionId: number, gradeData: any) {
    return this.request(`/assignments/submissions/${submissionId}/grade/`, {
      method: 'POST',
      body: JSON.stringify(gradeData),
    });
  }

  async getAssignmentSubmissionsForAssignment(assignmentId: number) {
    return this.request(`/assignments/${assignmentId}/submissions/`);
  }

  async submitAssignment(assignmentId: string, data: any) {
    // Handle both FormData and regular objects
    const isFormData = data instanceof FormData;

    return this.request(`/assignments/${assignmentId}/submit/`, {
      method: 'POST',
      body: isFormData ? data : JSON.stringify(data),
      headers: isFormData ? {
        // Don't set Content-Type for FormData, but keep Authorization
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      } : undefined,
    });
  }

  async getAssignmentSubmission(assignmentId: string) {
    return this.request(`/assignments/${assignmentId}/submission/`);
  }

  async getSubmissionFiles(submissionId: string) {
    return this.request(`/assignments/submissions/${submissionId}/files/`);
  }

  async deleteSubmissionFile(fileId: string) {
    return this.request(`/assignments/submission-files/${fileId}/`, {
      method: 'DELETE',
    });
  }

  async downloadSubmission(submissionId: number) {
    return this.request(`/assignments/submissions/${submissionId}/download/`);
  }

  async bulkGradeSubmissions(submissions: any[]) {
    return this.request('/assignments/bulk-grade/', {
      method: 'POST',
      body: JSON.stringify({ submissions }),
    });
  }

  // Grades
  async getGrades(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/grades/${queryString}`);
  }

  async getStudentGrades(studentId: string) {
    return this.request(`/grades/student/${studentId}/`);
  }

  async getCourseGrades(courseId: number) {
    return this.request(`/grades/course/${courseId}/`);
  }

  async getGradebook(courseId: number) {
    return this.request(`/grades/gradebook/${courseId}/`);
  }

  async generateTranscript(studentId: string) {
    return this.request(`/grades/transcript/${studentId}/`);
  }

  async getGradeStats() {
    return this.request('/grades/stats/');
  }

  async bulkUpdateGrades(grades: any[]) {
    return this.request('/grades/bulk-update/', {
      method: 'POST',
      body: JSON.stringify({ grades }),
    });
  }

  async exportGrades(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/grades/export/${queryString}`);
  }

  // Notifications
  async getNotifications(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/notifications/${queryString}`);
  }

  async markNotificationRead(notificationId: number) {
    return this.request(`/notifications/${notificationId}/mark-read/`, {
      method: 'POST',
    });
  }

  async markAllNotificationsRead() {
    return this.request('/notifications/mark-all-read/', {
      method: 'POST',
    });
  }

  async getUnreadNotificationsCount() {
    return this.request('/notifications/unread-count/');
  }

  async deleteReadNotifications() {
    return this.request('/notifications/delete-read/', {
      method: 'DELETE',
    });
  }

  // Announcements
  async getAnnouncements(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/notifications/announcements/${queryString}`);
  }

  async createAnnouncement(data: any) {
    return this.request('/notifications/announcements/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateAnnouncement(announcementId: number, data: any) {
    return this.request(`/notifications/announcements/${announcementId}/`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async publishAnnouncement(announcementId: number) {
    return this.request(`/notifications/announcements/${announcementId}/publish/`, {
      method: 'POST',
    });
  }

  // Exams
  async getExams(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/exams/${queryString}`);
  }

  async createExam(data: any) {
    return this.request('/exams/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateExam(examId: number, data: any) {
    return this.request(`/exams/${examId}/`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async scheduleExam(examId: number) {
    return this.request(`/exams/${examId}/schedule/`, {
      method: 'POST',
    });
  }

  async getExamResults(examId: number) {
    return this.request(`/exams/${examId}/results/`);
  }

  async getStudentExamResults(studentId: string) {
    return this.request(`/exams/results/student/${studentId}/`);
  }

  async getExamStats() {
    return this.request('/exams/stats/');
  }

  async getExamAnalytics(examId: number) {
    return this.request(`/exams/analytics/${examId}/`);
  }

  // Library
  async getBooks(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/library/books/${queryString}`);
  }

  async getBook(id: number) {
    return this.request(`/library/books/${id}/`);
  }

  async createBook(data: any) {
    return this.request('/library/books/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async reserveBook(bookId: number, notes?: string) {
    return this.request(`/library/books/${bookId}/reserve/`, {
      method: 'POST',
      body: JSON.stringify({ notes }),
    });
  }

  async borrowBook(bookId: number, borrowerId: string, notes?: string) {
    return this.request(`/library/books/${bookId}/borrow/`, {
      method: 'POST',
      body: JSON.stringify({
        borrower_id: borrowerId,
        notes,
      }),
    });
  }

  async returnBook(borrowingId: number, condition?: string) {
    return this.request(`/library/borrowings/${borrowingId}/return/`, {
      method: 'POST',
      body: JSON.stringify({
        condition_at_return: condition,
      }),
    });
  }

  async renewBorrowing(borrowingId: number) {
    return this.request(`/library/borrowings/${borrowingId}/renew/`, {
      method: 'POST',
    });
  }

  async getBorrowings(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/library/borrowings/${queryString}`);
  }

  async getReservations(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/library/reservations/${queryString}`);
  }

  async getLibraryStats() {
    return this.request('/library/stats/');
  }

  // Categories, Authors, Publishers
  async getCategories() {
    return this.request('/library/categories/');
  }

  async getAuthors(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/library/authors/${queryString}`);
  }

  async getPublishers(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/library/publishers/${queryString}`);
  }

  // Attendance
  async getAttendanceSessions(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/attendance/sessions/${queryString}`);
  }

  async createAttendanceSession(data: any) {
    return this.request('/attendance/sessions/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async generateQRCode(sessionId: number) {
    return this.request(`/attendance/sessions/${sessionId}/generate-qr/`, {
      method: 'POST',
    });
  }

  async markAttendanceQR(qrCode: string, location?: string) {
    return this.request('/attendance/records/mark-qr/', {
      method: 'POST',
      body: JSON.stringify({
        qr_code: qrCode,
        student_location: location,
      }),
    });
  }

  async getAttendanceRecords(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/attendance/records/${queryString}`);
  }

  async bulkMarkAttendance(sessionId: number, attendanceRecords: any[]) {
    return this.request('/attendance/records/bulk-mark/', {
      method: 'POST',
      body: JSON.stringify({
        session_id: sessionId,
        attendance_records: attendanceRecords,
      }),
    });
  }

  async getAttendanceSummary(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/attendance/summary/${queryString}`);
  }

  async getAttendanceStats() {
    return this.request('/attendance/stats/');
  }

  async getStudentAttendanceReport(studentId: string, params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/attendance/student/${studentId}/report/${queryString}`);
  }

  // Schedules
  async getSchedules(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/schedules/${queryString}`);
  }

  async getWeeklySchedule(weekStart?: string) {
    const queryString = weekStart ? `?week_start=${weekStart}` : '';
    return this.request(`/schedules/weekly/${queryString}`);
  }

  async getRooms(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/schedules/rooms/${queryString}`);
  }

  async getRoomAvailability(roomId: number, date: string) {
    return this.request(`/schedules/rooms/${roomId}/availability/?date=${date}`);
  }

  async getTimeSlots() {
    return this.request('/schedules/time-slots/');
  }

  async getRoomBookings(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/schedules/bookings/${queryString}`);
  }

  async createRoomBooking(data: any) {
    return this.request('/schedules/bookings/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async approveBooking(bookingId: number) {
    return this.request(`/schedules/bookings/${bookingId}/approve/`, {
      method: 'POST',
    });
  }

  async rejectBooking(bookingId: number, reason: string) {
    return this.request(`/schedules/bookings/${bookingId}/reject/`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async getPersonalSchedule(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/schedules/personal/${queryString}`);
  }

  async createPersonalSchedule(data: any) {
    return this.request('/schedules/personal/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Study Group methods
  async getStudyGroups() {
    return this.request('/study-groups/');
  }

  async createStudyGroup(groupData: any) {
    return this.request('/study-groups/', {
      method: 'POST',
      body: JSON.stringify(groupData),
    });
  }

  async updateStudyGroup(groupId: string, groupData: any) {
    return this.request(`/study-groups/${groupId}/`, {
      method: 'PATCH',
      body: JSON.stringify(groupData),
    });
  }

  async deleteStudyGroup(groupId: string) {
    return this.request(`/study-groups/${groupId}/`, {
      method: 'DELETE',
    });
  }

  async joinStudyGroup(groupId: string) {
    return this.request(`/study-groups/${groupId}/join/`, {
      method: 'POST',
    });
  }

  async leaveStudyGroup(groupId: string) {
    return this.request(`/study-groups/${groupId}/leave/`, {
      method: 'POST',
    });
  }

  // Attendance endpoints
  async getAttendanceSession(sessionId: string) {
    return this.request(`/attendance/sessions/${sessionId}/`);
  }

  async markAttendance(sessionId: string, data: any) {
    return this.request(`/attendance/sessions/${sessionId}/mark/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Analytics endpoints
  async getStudentPerformance(studentId: string) {
    return this.request(`/analytics/student/${studentId}/performance/`);
  }

  async getFinancialDashboard(period: string) {
    return this.request(`/finance/dashboard/?period=${period}`);
  }

  // Invoice endpoints
  async getInvoices(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/invoices/${queryString}`);
  }

  async createInvoice(invoiceData: any) {
    return this.request('/invoices/', {
      method: 'POST',
      body: JSON.stringify(invoiceData),
    });
  }

  async updateInvoice(invoiceId: string, invoiceData: any) {
    return this.request(`/invoices/${invoiceId}/`, {
      method: 'PATCH',
      body: JSON.stringify(invoiceData),
    });
  }

  async sendInvoice(invoiceId: string) {
    return this.request(`/invoices/${invoiceId}/send/`, {
      method: 'POST',
    });
  }

  // Request endpoints
  async getRequests(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/requests/${queryString}`);
  }

  async createRequest(requestData: any) {
    return this.request('/requests/', {
      method: 'POST',
      body: JSON.stringify(requestData),
    });
  }

  async updateRequestStatus(requestId: string, status: string) {
    return this.request(`/requests/${requestId}/update-status/`, {
      method: 'POST',
      body: JSON.stringify({ status }),
    });
  }

  // Document endpoints
  async getDocuments(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/documents/${queryString}`);
  }

  async uploadDocument(documentData: FormData) {
    return this.request('/documents/', {
      method: 'POST',
      body: documentData,
      headers: {
        // Don't set Content-Type for FormData, but keep Authorization
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
    });
  }

  async downloadDocument(documentId: string) {
    return this.request(`/documents/${documentId}/download/`);
  }

  async deleteDocument(documentId: string) {
    return this.request(`/documents/${documentId}/`, {
      method: 'DELETE',
    });
  }

  // Task endpoints
  async getTasks(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/tasks/${queryString}`);
  }

  async createTask(taskData: any) {
    return this.request('/tasks/', {
      method: 'POST',
      body: JSON.stringify(taskData),
    });
  }

  async updateTask(taskId: string, taskData: any) {
    return this.request(`/tasks/${taskId}/`, {
      method: 'PATCH',
      body: JSON.stringify(taskData),
    });
  }

  async deleteTask(taskId: string) {
    return this.request(`/tasks/${taskId}/`, {
      method: 'DELETE',
    });
  }

  // Schedule endpoints
  async getSchedule(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/schedule/${queryString}`);
  }

  async createScheduleEvent(eventData: any) {
    return this.request('/schedule/', {
      method: 'POST',
      body: JSON.stringify(eventData),
    });
  }

  async updateScheduleEvent(eventId: string, eventData: any) {
    return this.request(`/schedule/${eventId}/`, {
      method: 'PATCH',
      body: JSON.stringify(eventData),
    });
  }

  async deleteScheduleEvent(eventId: string) {
    return this.request(`/schedule/${eventId}/`, {
      method: 'DELETE',
    });
  }

  // Course enrollment endpoints
  async unenrollFromCourse(courseId: string) {
    return this.request(`/courses/${courseId}/unenroll/`, {
      method: 'POST',
    });
  }



  // Student management endpoints
  async createStudent(studentData: any) {
    return this.request('/students/', {
      method: 'POST',
      body: JSON.stringify(studentData),
    });
  }

  async updateStudent(studentId: string, studentData: any) {
    return this.request(`/students/${studentId}/`, {
      method: 'PATCH',
      body: JSON.stringify(studentData),
    });
  }

  async deleteStudent(studentId: string) {
    return this.request(`/students/${studentId}/`, {
      method: 'DELETE',
    });
  }



  // ============================================================================
  // VALIDATION API
  // ============================================================================

  async checkEmailAvailability(email: string) {
    return this.request(`/users/check-email/?email=${encodeURIComponent(email)}`);
  }

  async checkStudentIdAvailability(studentId: string) {
    return this.request(`/users/check-student-id/?student_id=${encodeURIComponent(studentId)}`);
  }

  async checkEmployeeIdAvailability(employeeId: string) {
    return this.request(`/users/check-employee-id/?employee_id=${encodeURIComponent(employeeId)}`);
  }

  async checkCourseCodeAvailability(courseCode: string) {
    return this.request(`/courses/check-code/?code=${encodeURIComponent(courseCode)}`);
  }

  async validateFormData(formType: string, data: any) {
    return this.request(`/validation/${formType}/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // ============================================================================
  // EXPORT & REPORTING API
  // ============================================================================

  async generatePDFReport(reportType: string, options: any) {
    const response = await fetch(`${API_BASE_URL}/reports/pdf/${reportType}/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
      },
      body: JSON.stringify(options),
    });

    if (!response.ok) {
      throw new Error(`PDF generation failed: ${response.statusText}`);
    }

    return response.arrayBuffer();
  }

  async generateExcelReport(reportType: string, options: any) {
    const response = await fetch(`${API_BASE_URL}/reports/excel/${reportType}/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
      },
      body: JSON.stringify(options),
    });

    if (!response.ok) {
      throw new Error(`Excel generation failed: ${response.statusText}`);
    }

    return response.arrayBuffer();
  }

  async getReportTemplates() {
    return this.request('/reports/templates/');
  }

  async createCustomReport(reportData: any) {
    return this.request('/reports/custom/', {
      method: 'POST',
      body: JSON.stringify(reportData),
    });
  }

  async getReportHistory(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/reports/history/${queryString}`);
  }

  // ============================================================================
  // MEETING REQUESTS API
  // ============================================================================

  async getMeetingRequests(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/meetings/requests/${queryString}`);
  }

  async createMeetingRequest(data: any) {
    return this.request('/meetings/requests/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateMeetingRequest(id: string, data: any) {
    return this.request(`/meetings/requests/${id}/`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async cancelMeetingRequest(id: string) {
    return this.request(`/meetings/requests/${id}/cancel/`, {
      method: 'POST',
    });
  }

  async addMeetingNote(id: string, data: any) {
    return this.request(`/meetings/requests/${id}/add_note/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getUpcomingMeetings() {
    return this.request('/meetings/requests/my_upcoming/');
  }

  async getPendingMeetingRequests() {
    return this.request('/meetings/requests/pending_requests/');
  }

  async getMeetingStatistics() {
    return this.request('/meetings/requests/statistics/');
  }

  async getFacultyList() {
    return this.request('/meetings/faculty/');
  }

  async getMeetingAvailability(facultyId?: string) {
    const params = facultyId ? `?faculty=${facultyId}` : '';
    return this.request(`/meetings/availability/${params}`);
  }

}

export const apiService = new ApiService();
export default apiService;
