/**
 * Simplified Security Service for Web Frontend
 * Uses only native browser APIs for maximum compatibility
 */

export interface SecurityConfig {
  enableRequestSigning: boolean;
  enableDeviceFingerprinting: boolean;
  enableEncryption: boolean;
  maxRetryAttempts: number;
  requestTimeout: number;
  sessionTimeout: number;
}

export interface DeviceFingerprint {
  deviceId: string;
  deviceSignature: string;
  userAgent: string;
  language: string;
  platform: string;
  screenResolution: string;
  timezone: string;
  cookieEnabled: boolean;
  doNotTrack: string | null;
  timestamp: number;
}

export interface SecurityHeaders {
  'X-Device-ID'?: string;
  'X-Device-Signature'?: string;
  'X-Request-Signature'?: string;
  'X-Timestamp'?: string;
  'X-Platform'?: string;
  'X-App-Version'?: string;
  'User-Agent'?: string;
}

class SimpleSecurityService {
  private config: SecurityConfig = {
    enableRequestSigning: true,
    enableDeviceFingerprinting: true,
    enableEncryption: false, // Disabled for simplicity
    maxRetryAttempts: 3,
    requestTimeout: 30000,
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
  };

  private deviceFingerprint: DeviceFingerprint | null = null;
  private sessionTimer: number | null = null;
  private lastActivity: number = Date.now();

  async initialize(): Promise<void> {
    try {
      // Generate device fingerprint
      await this.generateDeviceFingerprint();
      
      // Setup session monitoring
      this.setupSessionMonitoring();
      
      // Setup activity tracking
      this.setupActivityTracking();
      
      console.log('🔐 Simple Security service initialized');
    } catch (error) {
      console.error('Security service initialization failed:', error);
    }
  }

  async generateDeviceFingerprint(): Promise<DeviceFingerprint> {
    try {
      // Check if fingerprint already exists
      const stored = localStorage.getItem('device_fingerprint');
      if (stored) {
        this.deviceFingerprint = JSON.parse(stored);
        return this.deviceFingerprint!;
      }

      // Generate new fingerprint using browser characteristics
      const fingerprint: DeviceFingerprint = {
        deviceId: this.generateDeviceId(),
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform || 'unknown',
        screenResolution: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        cookieEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack,
        timestamp: Date.now(),
        deviceSignature: '', // Will be set below
      };

      fingerprint.deviceSignature = this.generateDeviceSignature(fingerprint);

      // Store fingerprint
      localStorage.setItem('device_fingerprint', JSON.stringify(fingerprint));
      this.deviceFingerprint = fingerprint;

      return fingerprint;
    } catch (error) {
      console.error('Failed to generate device fingerprint:', error);
      throw error;
    }
  }

  private generateDeviceId(): string {
    try {
      // Try to get existing device ID
      let deviceId = localStorage.getItem('device_id');
      
      if (!deviceId) {
        // Generate new device ID using browser characteristics
        const deviceInfo = [
          navigator.userAgent,
          navigator.language,
          navigator.platform || 'unknown',
          screen.width,
          screen.height,
          new Date().getTimezoneOffset(),
          Date.now().toString()
        ].join('-');

        deviceId = this.simpleHash(deviceInfo);
        localStorage.setItem('device_id', deviceId);
      }

      return deviceId;
    } catch (error) {
      console.error('Failed to generate device ID:', error);
      // Fallback to random ID
      return Math.random().toString(36).substring(2, 15);
    }
  }

  private generateDeviceSignature(fingerprint: DeviceFingerprint): string {
    try {
      const signatureData = [
        fingerprint.deviceId,
        fingerprint.userAgent,
        fingerprint.platform,
        fingerprint.screenResolution,
        'UMLS-Web-App'
      ].join('|');

      return this.simpleHash(signatureData);
    } catch (error) {
      console.error('Failed to generate device signature:', error);
      return 'fallback-signature';
    }
  }

  async generateRequestSignature(method: string, path: string, body: string, timestamp: string): Promise<string> {
    try {
      const signatureString = `${method}:${path}:${body}:${timestamp}`;
      return this.simpleHash(signatureString);
    } catch (error) {
      console.error('Failed to generate request signature:', error);
      return '';
    }
  }

  async getSecurityHeaders(method: string = 'GET', path: string = '', body: string = ''): Promise<SecurityHeaders> {
    // Temporarily disable security headers for testing
    return {};

    const headers: SecurityHeaders = {};

    try {
      // Add device fingerprint headers
      if (this.config.enableDeviceFingerprinting && this.deviceFingerprint) {
        headers['X-Device-ID'] = this.deviceFingerprint.deviceId;
        headers['X-Device-Signature'] = this.deviceFingerprint.deviceSignature;
      }

      // Add request signature headers
      if (this.config.enableRequestSigning) {
        const timestamp = Date.now().toString();
        const signature = await this.generateRequestSignature(method, path, body, timestamp);

        headers['X-Request-Signature'] = signature;
        headers['X-Timestamp'] = timestamp;
      }

      // Add platform identifier
      headers['X-Platform'] = 'web';
      headers['X-App-Version'] = '1.0.0';
      headers['User-Agent'] = `UMLS-Web/${navigator.userAgent}`;

      return headers;
    } catch (error) {
      console.error('Failed to generate security headers:', error);
      return {};
    }
  }

  private setupSessionMonitoring(): void {
    // Clear existing timer
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
    }

    // Set up session timeout
    this.sessionTimer = window.setTimeout(() => {
      this.handleSessionTimeout();
    }, this.config.sessionTimeout);
  }

  private setupActivityTracking(): void {
    // Track user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    events.forEach(event => {
      document.addEventListener(event, () => {
        this.updateLastActivity();
      }, true);
    });

    // Check for inactivity every minute
    setInterval(() => {
      const inactiveTime = Date.now() - this.lastActivity;
      if (inactiveTime > this.config.sessionTimeout) {
        this.handleSessionTimeout();
      }
    }, 60000);
  }

  updateLastActivity(): void {
    this.lastActivity = Date.now();
    this.setupSessionMonitoring(); // Reset session timer
  }

  handleSessionTimeout(): void {
    console.warn('Session timeout detected');
    
    // Clear sensitive data
    this.clearSecurityData();
    
    // Redirect to login
    if (window.location.pathname !== '/login') {
      localStorage.setItem('session_expired', 'true');
      window.location.href = '/login?reason=timeout';
    }
  }

  async validateNetworkSecurity(): Promise<boolean> {
    try {
      // Check if using HTTPS
      if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
        console.warn('⚠️ Insecure connection detected');
        return false;
      }

      // Check for secure context
      if (!window.isSecureContext) {
        console.warn('⚠️ Not in secure context');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Network security validation failed:', error);
      return false;
    }
  }

  async detectSecurityThreats(): Promise<string[]> {
    const threats: string[] = [];

    try {
      // Check for development mode
      if (import.meta.env.DEV) {
        threats.push('Application is running in development mode');
      }

      // Check for insecure connection
      if (!(await this.validateNetworkSecurity())) {
        threats.push('Insecure network connection detected');
      }

      // Check for browser security features
      if (!window.crypto || !window.crypto.subtle) {
        threats.push('Browser lacks modern security features');
      }

      // Check for developer tools
      if (this.isDevToolsOpen()) {
        threats.push('Developer tools are open');
      }

      return threats;
    } catch (error) {
      console.error('Security threat detection failed:', error);
      return ['Security check failed'];
    }
  }

  private isDevToolsOpen(): boolean {
    try {
      const threshold = 160;
      return window.outerHeight - window.innerHeight > threshold ||
             window.outerWidth - window.innerWidth > threshold;
    } catch (error) {
      return false;
    }
  }

  async clearSecurityData(): Promise<void> {
    try {
      localStorage.removeItem('device_fingerprint');
      localStorage.removeItem('device_id');
      
      this.deviceFingerprint = null;
      
      if (this.sessionTimer) {
        window.clearTimeout(this.sessionTimer);
      }
      
      console.log('Security data cleared');
    } catch (error) {
      console.error('Failed to clear security data:', error);
    }
  }

  getConfig(): SecurityConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getDeviceFingerprint(): DeviceFingerprint | null {
    return this.deviceFingerprint;
  }

  getSessionTimeRemaining(): number {
    const elapsed = Date.now() - this.lastActivity;
    return Math.max(0, this.config.sessionTimeout - elapsed);
  }

  isSessionActive(): boolean {
    return this.getSessionTimeRemaining() > 0;
  }

  // Simple hash function for basic security (not cryptographically secure)
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(8, '0');
  }
}

export const securityService = new SimpleSecurityService();
